# Enhanced RAG System Environment Configuration
# Copy this to .env and update with your actual values

# =============================================================================
# LLM CONFIGURATION
# =============================================================================

# OpenAI Configuration (required for embeddings and chat completion)
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4o
OPENAI_EMBEDDING_MODEL=text-embedding-3-large

# =============================================================================
# VECTOR DATABASE CONFIGURATION
# =============================================================================

# Vector Database Type (chroma, pinecone, weaviate)
VECTOR_DB_TYPE=chroma

# ChromaDB Configuration (for development)
CHROMA_PERSIST_DIRECTORY=./data/chroma
CHROMA_HOST=localhost
CHROMA_PORT=8000

# Pinecone Configuration (for production)
PINECONE_API_KEY=your-pinecone-api-key-here
PINECONE_ENVIRONMENT=your-pinecone-environment
PINECONE_INDEX_NAME=rag-system-enhanced

# Weaviate Configuration (alternative production option)
WEAVIATE_URL=http://localhost:8080
WEAVIATE_API_KEY=your-weaviate-api-key-here

# =============================================================================
# EMBEDDING MODELS CONFIGURATION
# =============================================================================

# Primary embedding model (for production)
PRIMARY_EMBEDDING_MODEL=all-mpnet-base-v2
USE_OPENAI_EMBEDDINGS=true

# Cross-encoder for reranking
CROSS_ENCODER_MODEL=cross-encoder/ms-marco-MiniLM-L-12-v2

# =============================================================================
# RETRIEVAL PIPELINE CONFIGURATION
# =============================================================================

# Multi-stage retrieval settings
INITIAL_RETRIEVAL_SIZE=50
RERANK_SIZE=20
DIVERSITY_SELECTION_SIZE=10
FINAL_RESULTS_SIZE=5

# Retrieval thresholds
SIMILARITY_THRESHOLD=0.8
RERANK_THRESHOLD=0.5
DIVERSITY_THRESHOLD=0.7

# Query expansion settings
QUERY_EXPANSION_LIMIT=3
ENABLE_QUERY_EXPANSION=true

# =============================================================================
# CHUNKING CONFIGURATION
# =============================================================================

# Advanced chunking settings
CHUNKING_STRATEGY=semantic_structure
MAX_CHUNK_SIZE=512
CHUNK_OVERLAP=100
SIMILARITY_THRESHOLD_CHUNKING=0.8

# SpaCy model for NLP processing
SPACY_MODEL=en_core_web_sm

# =============================================================================
# RESPONSE GENERATION CONFIGURATION
# =============================================================================

# Response template settings
USE_STRUCTURED_TEMPLATES=true
DEFAULT_TEMPLATE_TYPE=analytical

# Multi-source synthesis settings
SYNTHESIS_STRATEGY=auto
ENABLE_MULTI_SOURCE_SYNTHESIS=true
MAX_SOURCES_FOR_SYNTHESIS=10

# Quality thresholds
QUALITY_THRESHOLD=3.5
ENABLE_AUTO_IMPROVEMENT=true
MAX_IMPROVEMENT_ITERATIONS=2

# =============================================================================
# CACHING CONFIGURATION
# =============================================================================

# Redis for caching (optional but recommended for production)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Cache settings
ENABLE_QUERY_CACHE=true
ENABLE_EMBEDDING_CACHE=true
CACHE_TTL=3600

# =============================================================================
# SERVICE CONFIGURATION
# =============================================================================

# Knowledge Base Service
KNOWLEDGE_BASE_SERVICE_HOST=localhost
KNOWLEDGE_BASE_SERVICE_PORT=8002

# Conversation Service
CONVERSATION_SERVICE_HOST=localhost
CONVERSATION_SERVICE_PORT=8001

# Analytics Service
ANALYTICS_SERVICE_HOST=localhost
ANALYTICS_SERVICE_PORT=8005

# =============================================================================
# MONITORING AND OBSERVABILITY
# =============================================================================

# Prometheus metrics
ENABLE_METRICS=true
METRICS_PORT=9090

# Logging configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
ENABLE_STRUCTURED_LOGGING=true

# Performance monitoring
ENABLE_PERFORMANCE_TRACKING=true
BENCHMARK_MODE=false

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================

# Environment type
ENVIRONMENT=development
DEBUG=true

# Rate limiting
ENABLE_RATE_LIMITING=false
REQUESTS_PER_MINUTE=60

# Security settings
ENABLE_CORS=true
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8501

# Database settings (for metadata storage)
DATABASE_URL=postgresql://user:password@localhost:5432/rag_system
ENABLE_DATABASE_LOGGING=false

# =============================================================================
# ADVANCED FEATURES
# =============================================================================

# A/B Testing
ENABLE_AB_TESTING=false
AB_TEST_TRAFFIC_SPLIT=0.5

# Feature flags
ENABLE_ADVANCED_CHUNKING=true
ENABLE_MULTI_STAGE_RETRIEVAL=true
ENABLE_CROSS_ENCODER_RERANKING=true
ENABLE_DIVERSITY_SELECTION=true
ENABLE_RESPONSE_TEMPLATES=true
ENABLE_MULTI_SOURCE_SYNTHESIS=true

# Experimental features
ENABLE_EXPERIMENTAL_FEATURES=false
EXPERIMENTAL_EMBEDDING_MODEL=
EXPERIMENTAL_RETRIEVAL_STRATEGY=

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Hot reloading
ENABLE_HOT_RELOAD=true

# Development tools
ENABLE_DEBUG_ENDPOINTS=true
ENABLE_ADMIN_INTERFACE=false

# Sample data
LOAD_SAMPLE_DATA=true
SAMPLE_DATA_PATH=./data/sample_documents.json

# Testing
ENABLE_MOCK_SERVICES=false
TEST_DATA_PATH=./tests/data/
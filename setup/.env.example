# LLM-Powered Retrieval System - Environment Configuration

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_EMBEDDING_MODEL=text-embedding-3-large

# Vector Database Configuration
VECTOR_STORE_TYPE=pinecone  # pinecone, weaviate, chroma
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=us-west1-gcp-free
PINECONE_INDEX_NAME=llm-retrieval-kb

# Alternative Vector Stores (uncomment if using)
# WEAVIATE_URL=http://localhost:8080
# WEAVIATE_API_KEY=your_weaviate_api_key
# CHROMA_HOST=localhost
# CHROMA_PORT=8000

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=llm_retrieval
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_URL=redis://localhost:6379

# Service URLs (for inter-service communication)
KNOWLEDGE_BASE_SERVICE_URL=http://localhost:8002
CONVERSATION_SERVICE_URL=http://localhost:8001
ANALYTICS_SERVICE_URL=http://localhost:8005

# Monitoring Configuration
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
GRAFANA_PASSWORD=admin

# JWT Configuration (if implementing authentication)
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Development/Production Flags
DEBUG=false
TESTING=false
DEVELOPMENT=true

# Docker Configuration
COMPOSE_PROJECT_NAME=llm-retrieval-system

# Performance Settings
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_SEARCH_RESULTS=20
CONTEXT_WINDOW=4000

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# CORS Origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
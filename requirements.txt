# LLM-Powered Retrieval System - Root Requirements
# This is a meta-requirements file for development setup

# Development and Testing Tools
pytest>=7.4.3
pytest-asyncio>=0.23.2
pytest-cov>=4.1.0
requests>=2.31.0
httpx>=0.25.2

# Code Quality Tools
black>=23.11.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.7.1
pre-commit>=3.6.0

# Documentation Tools
mkdocs>=1.5.0
mkdocs-material>=9.0.0

# Monitoring and Observability
prometheus-client>=0.19.0

# Utilities
python-dotenv>=1.0.0

# Note: 
# - System tools like curl, jq, docker-compose are installed separately
# - Individual services have their own requirements.txt files:
#   - services/knowledge-base-service/requirements.txt
#   - services/conversation-service/requirements.txt  
#   - services/analytics-service/requirements.txt

# To set up development environment:
# 1. Copy setup/.env.example to .env and configure
# 2. Run: cd setup && docker-compose up -d
# 3. Each service runs in its own container with its own dependencies


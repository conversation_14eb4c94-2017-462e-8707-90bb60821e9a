#!/usr/bin/env python3
"""
Comprehensive analysis of Phase 1 and Phase 2 implementations
Analyzes code quality, functionality, and potential issues
"""

import os
import re
import ast
from typing import Dict, List, Any, Set
from collections import defaultdict

class CodeAnalyzer:
    """Analyze code quality and functionality."""
    
    def __init__(self):
        self.results = {
            "files_analyzed": 0,
            "total_lines": 0,
            "total_classes": 0,
            "total_methods": 0,
            "async_methods": 0,
            "error_handling": 0,
            "logging_statements": 0,
            "type_hints": 0,
            "docstrings": 0,
            "imports": defaultdict(int),
            "issues": [],
            "features": [],
            "complexity_score": 0
        }
    
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """Analyze a single Python file."""
        
        if not os.path.exists(file_path):
            return {"error": "File not found"}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse AST
            tree = ast.parse(content)
            
            file_analysis = {
                "file": file_path,
                "lines": len(content.split('\n')),
                "classes": [],
                "methods": [],
                "async_methods": [],
                "imports": [],
                "error_handling": 0,
                "logging": 0,
                "type_hints": 0,
                "docstrings": 0,
                "issues": [],
                "features": []
            }
            
            # Analyze AST
            self._analyze_ast(tree, file_analysis)
            
            # Analyze content patterns
            self._analyze_content_patterns(content, file_analysis)
            
            # Update global results
            self._update_global_results(file_analysis)
            
            return file_analysis
            
        except Exception as e:
            return {"error": str(e)}
    
    def _analyze_ast(self, tree: ast.AST, analysis: Dict[str, Any]):
        """Analyze AST structure."""
        
        for node in ast.walk(tree):
            # Classes
            if isinstance(node, ast.ClassDef):
                analysis["classes"].append({
                    "name": node.name,
                    "methods": len([n for n in node.body if isinstance(n, (ast.FunctionDef, ast.AsyncFunctionDef))]),
                    "has_docstring": ast.get_docstring(node) is not None
                })
                if ast.get_docstring(node):
                    analysis["docstrings"] += 1
            
            # Methods
            elif isinstance(node, ast.FunctionDef):
                analysis["methods"].append(node.name)
                if ast.get_docstring(node):
                    analysis["docstrings"] += 1
                # Check for type hints
                if node.returns or any(arg.annotation for arg in node.args.args):
                    analysis["type_hints"] += 1
            
            # Async methods
            elif isinstance(node, ast.AsyncFunctionDef):
                analysis["async_methods"].append(node.name)
                if ast.get_docstring(node):
                    analysis["docstrings"] += 1
                if node.returns or any(arg.annotation for arg in node.args.args):
                    analysis["type_hints"] += 1
            
            # Imports
            elif isinstance(node, ast.Import):
                for alias in node.names:
                    analysis["imports"].append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ""
                for alias in node.names:
                    analysis["imports"].append(f"{module}.{alias.name}")
            
            # Error handling
            elif isinstance(node, ast.Try):
                analysis["error_handling"] += 1
    
    def _analyze_content_patterns(self, content: str, analysis: Dict[str, Any]):
        """Analyze content patterns using regex."""
        
        # Logging statements
        logging_patterns = [
            r'self\.logger\.',
            r'logging\.',
            r'log\.',
            r'\.info\(',
            r'\.error\(',
            r'\.warning\(',
            r'\.debug\('
        ]
        
        for pattern in logging_patterns:
            matches = re.findall(pattern, content)
            analysis["logging"] += len(matches)
        
        # Feature detection
        features = {
            "async_support": r'async def|await ',
            "type_hints": r': \w+|-> \w+',
            "dataclasses": r'@dataclass',
            "enums": r'class \w+\(Enum\)',
            "error_handling": r'try:|except:|finally:',
            "logging": r'logger\.|logging\.',
            "caching": r'cache|Cache',
            "validation": r'validate|Validate',
            "configuration": r'config|Config',
            "metrics": r'metrics|stats|statistics'
        }
        
        for feature, pattern in features.items():
            if re.search(pattern, content):
                analysis["features"].append(feature)
        
        # Issue detection
        issues = {
            "long_lines": r'.{120,}',
            "todo_comments": r'TODO|FIXME|XXX',
            "print_statements": r'print\(',
            "hardcoded_values": r'localhost|127\.0\.0\.1|password|secret',
            "missing_error_handling": r'open\(|requests\.|urllib\.'
        }
        
        for issue, pattern in issues.items():
            matches = re.findall(pattern, content)
            if matches:
                analysis["issues"].append({
                    "type": issue,
                    "count": len(matches)
                })
    
    def _update_global_results(self, file_analysis: Dict[str, Any]):
        """Update global analysis results."""
        
        self.results["files_analyzed"] += 1
        self.results["total_lines"] += file_analysis["lines"]
        self.results["total_classes"] += len(file_analysis["classes"])
        self.results["total_methods"] += len(file_analysis["methods"])
        self.results["async_methods"] += len(file_analysis["async_methods"])
        self.results["error_handling"] += file_analysis["error_handling"]
        self.results["logging_statements"] += file_analysis["logging"]
        self.results["type_hints"] += file_analysis["type_hints"]
        self.results["docstrings"] += file_analysis["docstrings"]
        
        # Aggregate imports
        for imp in file_analysis["imports"]:
            self.results["imports"][imp] += 1
        
        # Aggregate features
        for feature in file_analysis["features"]:
            if feature not in self.results["features"]:
                self.results["features"].append(feature)
        
        # Aggregate issues
        for issue in file_analysis["issues"]:
            self.results["issues"].append(issue)

def analyze_implementation_completeness():
    """Analyze implementation completeness."""
    
    required_components = {
        "Phase 1": [
            "knowledge_taxonomy.py",
            "content_categorizer.py", 
            "response_formatter.py",
            "evidence_tracker.py"
        ],
        "Phase 2": [
            "uncertainty_detector.py",
            "escalation_manager.py",
            "consistency_validator.py",
            "query_router.py",
            "domain_retrievers.py"
        ]
    }
    
    file_paths = [
        'services/knowledge-base-service/src/core/knowledge_taxonomy.py',
        'services/knowledge-base-service/src/core/content_categorizer.py',
        'services/conversation-service/src/core/response_formatter.py',
        'services/conversation-service/src/core/evidence_tracker.py',
        'services/conversation-service/src/core/uncertainty_detector.py',
        'services/conversation-service/src/core/escalation_manager.py',
        'services/conversation-service/src/core/consistency_validator.py',
        'services/knowledge-base-service/src/core/query_router.py',
        'services/knowledge-base-service/src/core/domain_retrievers.py'
    ]
    
    completeness = {
        "total_required": 9,
        "implemented": 0,
        "missing": [],
        "present": []
    }
    
    for file_path in file_paths:
        if os.path.exists(file_path):
            completeness["implemented"] += 1
            completeness["present"].append(os.path.basename(file_path))
        else:
            completeness["missing"].append(os.path.basename(file_path))
    
    completeness["completion_rate"] = (completeness["implemented"] / completeness["total_required"]) * 100
    
    return completeness

def analyze_functionality_coverage():
    """Analyze functionality coverage based on method names and patterns."""
    
    expected_functionality = {
        "knowledge_taxonomy": [
            "classify_content", "create_node", "get_taxonomy_structure",
            "search_taxonomy", "export_taxonomy", "import_taxonomy"
        ],
        "content_categorizer": [
            "classify_content", "train_model", "get_category_statistics",
            "_rule_based_classification", "_ml_based_classification"
        ],
        "response_formatter": [
            "format_response", "_apply_content_formatting", "_apply_style_formatting",
            "_generate_alternative_formats", "_calculate_readability_score"
        ],
        "evidence_tracker": [
            "create_evidence_map", "_map_segment_evidence", "_find_supporting_evidence",
            "validate_response_evidence", "export_evidence_map"
        ],
        "uncertainty_detector": [
            "assess_uncertainty", "_detect_uncertainty_signals", "should_escalate",
            "enhance_response_with_uncertainty", "get_confidence_modifier"
        ],
        "escalation_manager": [
            "evaluate_escalation_need", "create_escalation_ticket", "assign_ticket",
            "resolve_ticket", "get_escalation_message"
        ],
        "consistency_validator": [
            "validate_consistency", "_check_policy_compliance", "_check_factual_consistency",
            "add_policy_rule", "get_validation_summary"
        ],
        "query_router": [
            "route_query", "_classify_domain", "_assess_complexity",
            "_select_retrievers", "get_retriever_recommendations"
        ],
        "domain_retrievers": [
            "retrieve", "_domain_specific_search", "multi_domain_retrieve",
            "get_best_domain_for_query", "health_check"
        ]
    }
    
    file_paths = {
        "knowledge_taxonomy": 'services/knowledge-base-service/src/core/knowledge_taxonomy.py',
        "content_categorizer": 'services/knowledge-base-service/src/core/content_categorizer.py',
        "response_formatter": 'services/conversation-service/src/core/response_formatter.py',
        "evidence_tracker": 'services/conversation-service/src/core/evidence_tracker.py',
        "uncertainty_detector": 'services/conversation-service/src/core/uncertainty_detector.py',
        "escalation_manager": 'services/conversation-service/src/core/escalation_manager.py',
        "consistency_validator": 'services/conversation-service/src/core/consistency_validator.py',
        "query_router": 'services/knowledge-base-service/src/core/query_router.py',
        "domain_retrievers": 'services/knowledge-base-service/src/core/domain_retrievers.py'
    }
    
    coverage = {}
    
    for component, file_path in file_paths.items():
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                content = f.read()
            
            expected_methods = expected_functionality.get(component, [])
            found_methods = []
            missing_methods = []
            
            for method in expected_methods:
                if f"def {method}" in content or f"async def {method}" in content:
                    found_methods.append(method)
                else:
                    missing_methods.append(method)
            
            coverage[component] = {
                "expected": len(expected_methods),
                "found": len(found_methods),
                "missing": missing_methods,
                "coverage_rate": (len(found_methods) / len(expected_methods)) * 100 if expected_methods else 100
            }
        else:
            coverage[component] = {
                "expected": len(expected_functionality.get(component, [])),
                "found": 0,
                "missing": expected_functionality.get(component, []),
                "coverage_rate": 0
            }
    
    return coverage

def main():
    """Run comprehensive analysis."""
    
    print("🔍 COMPREHENSIVE PHASE 1 & 2 IMPLEMENTATION ANALYSIS")
    print("=" * 70)
    
    # 1. Implementation Completeness
    print("\n📋 1. IMPLEMENTATION COMPLETENESS")
    print("-" * 40)
    completeness = analyze_implementation_completeness()
    print(f"Total Required Components: {completeness['total_required']}")
    print(f"Implemented Components: {completeness['implemented']}")
    print(f"Completion Rate: {completeness['completion_rate']:.1f}%")
    
    if completeness['missing']:
        print(f"❌ Missing: {', '.join(completeness['missing'])}")
    else:
        print("✅ All components implemented!")
    
    # 2. Functionality Coverage
    print("\n🎯 2. FUNCTIONALITY COVERAGE")
    print("-" * 40)
    coverage = analyze_functionality_coverage()
    
    total_expected = sum(comp['expected'] for comp in coverage.values())
    total_found = sum(comp['found'] for comp in coverage.values())
    overall_coverage = (total_found / total_expected) * 100 if total_expected > 0 else 0
    
    print(f"Overall Functionality Coverage: {overall_coverage:.1f}%")
    print(f"Total Expected Methods: {total_expected}")
    print(f"Total Implemented Methods: {total_found}")
    
    for component, data in coverage.items():
        status = "✅" if data['coverage_rate'] >= 90 else "⚠️" if data['coverage_rate'] >= 70 else "❌"
        print(f"{status} {component}: {data['coverage_rate']:.1f}% ({data['found']}/{data['expected']})")
        
        if data['missing']:
            print(f"   Missing: {', '.join(data['missing'][:3])}{'...' if len(data['missing']) > 3 else ''}")
    
    # 3. Code Quality Analysis
    print("\n📊 3. CODE QUALITY ANALYSIS")
    print("-" * 40)
    
    analyzer = CodeAnalyzer()
    file_paths = [
        'services/knowledge-base-service/src/core/knowledge_taxonomy.py',
        'services/knowledge-base-service/src/core/content_categorizer.py',
        'services/conversation-service/src/core/response_formatter.py',
        'services/conversation-service/src/core/evidence_tracker.py',
        'services/conversation-service/src/core/uncertainty_detector.py',
        'services/conversation-service/src/core/escalation_manager.py',
        'services/conversation-service/src/core/consistency_validator.py',
        'services/knowledge-base-service/src/core/query_router.py',
        'services/knowledge-base-service/src/core/domain_retrievers.py'
    ]
    
    for file_path in file_paths:
        if os.path.exists(file_path):
            analyzer.analyze_file(file_path)
    
    results = analyzer.results
    
    print(f"Files Analyzed: {results['files_analyzed']}")
    print(f"Total Lines of Code: {results['total_lines']:,}")
    print(f"Total Classes: {results['total_classes']}")
    print(f"Total Methods: {results['total_methods']}")
    print(f"Async Methods: {results['async_methods']}")
    print(f"Error Handling Blocks: {results['error_handling']}")
    print(f"Logging Statements: {results['logging_statements']}")
    print(f"Type Hints: {results['type_hints']}")
    print(f"Docstrings: {results['docstrings']}")
    
    # 4. Feature Analysis
    print("\n🚀 4. FEATURE ANALYSIS")
    print("-" * 40)
    print("Implemented Features:")
    for feature in sorted(results['features']):
        print(f"✅ {feature}")
    
    # 5. Quality Score
    print("\n⭐ 5. OVERALL QUALITY SCORE")
    print("-" * 40)
    
    # Calculate quality score
    quality_factors = {
        "completion": completeness['completion_rate'] / 100,
        "functionality": overall_coverage / 100,
        "async_support": min(results['async_methods'] / 50, 1.0),  # Normalize to 50 async methods
        "error_handling": min(results['error_handling'] / 30, 1.0),  # Normalize to 30 try blocks
        "documentation": min(results['docstrings'] / 100, 1.0),  # Normalize to 100 docstrings
        "type_hints": min(results['type_hints'] / 80, 1.0)  # Normalize to 80 type hints
    }
    
    overall_quality = sum(quality_factors.values()) / len(quality_factors) * 100
    
    print(f"Implementation Completeness: {quality_factors['completion']*100:.1f}%")
    print(f"Functionality Coverage: {quality_factors['functionality']*100:.1f}%")
    print(f"Async Support: {quality_factors['async_support']*100:.1f}%")
    print(f"Error Handling: {quality_factors['error_handling']*100:.1f}%")
    print(f"Documentation: {quality_factors['documentation']*100:.1f}%")
    print(f"Type Hints: {quality_factors['type_hints']*100:.1f}%")
    
    print(f"\n🎯 OVERALL QUALITY SCORE: {overall_quality:.1f}%")
    
    if overall_quality >= 90:
        print("🏆 EXCELLENT - Production ready implementation!")
    elif overall_quality >= 80:
        print("🥇 VERY GOOD - High quality implementation with minor improvements needed")
    elif overall_quality >= 70:
        print("🥈 GOOD - Solid implementation with some areas for improvement")
    elif overall_quality >= 60:
        print("🥉 FAIR - Functional implementation but needs significant improvements")
    else:
        print("⚠️ NEEDS WORK - Implementation requires major improvements")

if __name__ == "__main__":
    main()

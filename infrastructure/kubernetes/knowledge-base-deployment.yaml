apiVersion: apps/v1
kind: Deployment
metadata:
  name: knowledge-base-service
  namespace: customer-support
  labels:
    app: knowledge-base-service
    component: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: knowledge-base-service
  template:
    metadata:
      labels:
        app: knowledge-base-service
        component: backend
    spec:
      containers:
      - name: knowledge-base-service
        image: llm-retrieval/knowledge-base-service:latest
        ports:
        - containerPort: 8002
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: openai-secret
              key: api-key
        - name: VECTOR_STORE_TYPE
          value: "pinecone"
        - name: PINECONE_API_KEY
          valueFrom:
            secretKeyRef:
              name: pinecone-secret
              key: api-key
        - name: PINECONE_INDEX_NAME
          value: "llm-retrieval-kb"
        - name: REDIS_URL
          value: "redis://redis:6379"
        - name: POSTGRES_HOST
          value: "postgresql"
        - name: POSTGRES_DB
          value: "llm_retrieval"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8002
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8002
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: knowledge-base-service
  namespace: customer-support
  labels:
    app: knowledge-base-service
spec:
  selector:
    app: knowledge-base-service
  ports:
  - port: 8002
    targetPort: 8002
    name: http
  type: ClusterIP
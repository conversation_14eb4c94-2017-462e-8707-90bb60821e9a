apiVersion: apps/v1
kind: Deployment
metadata:
  name: analytics-service
  namespace: customer-support
  labels:
    app: analytics-service
    component: backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: analytics-service
  template:
    metadata:
      labels:
        app: analytics-service
        component: backend
    spec:
      containers:
      - name: analytics-service
        image: llm-retrieval/analytics-service:latest
        ports:
        - containerPort: 8005
        env:
        - name: PROMETHEUS_PORT
          value: "8005"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "250m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8005
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8005
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: analytics-service
  namespace: customer-support
  labels:
    app: analytics-service
spec:
  selector:
    app: analytics-service
  ports:
  - port: 8005
    targetPort: 8005
    name: http
  - port: 8005
    targetPort: 8005
    name: metrics
  type: ClusterIP
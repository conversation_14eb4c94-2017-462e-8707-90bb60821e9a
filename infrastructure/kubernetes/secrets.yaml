apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
  namespace: customer-support
type: Opaque
stringData:
  username: "postgres"
  password: "your-postgres-password-here"  # Change this in production

---
apiVersion: v1
kind: Secret
metadata:
  name: openai-secret
  namespace: customer-support
type: Opaque
stringData:
  api-key: "your-openai-api-key-here"  # Change this in production

---
apiVersion: v1
kind: Secret
metadata:
  name: jwt-secret
  namespace: customer-support
type: Opaque
stringData:
  secret-key: "your-jwt-secret-key-here"  # Change this in production

---
apiVersion: v1
kind: Secret
metadata:
  name: pinecone-secret
  namespace: customer-support
type: Opaque
stringData:
  api-key: "your-pinecone-api-key-here"  # Change this in production
  environment: "your-pinecone-environment-here"  # Change this in production
  index-name: "customer-support-kb"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: customer-support
data:
  LOG_LEVEL: "INFO"
  DEBUG: "false"
  DEVELOPMENT: "false"
  VECTOR_STORE_TYPE: "pinecone"
  RATE_LIMIT_REQUESTS: "100"
  RATE_LIMIT_WINDOW: "60"
  CHUNK_SIZE: "1000"
  CHUNK_OVERLAP: "200"
  MAX_SEARCH_RESULTS: "10"
  SESSION_TIMEOUT: "3600"
  MAX_CONVERSATION_LENGTH: "50"
#!/usr/bin/env python3
"""
Comprehensive test script for Phase 1 and Phase 2 implementations
Tests all functionality and identifies errors or missing dependencies
"""

import sys
import asyncio
import traceback
from datetime import datetime
from typing import Dict, List, Any

# Test results storage
test_results = {
    "passed": [],
    "failed": [],
    "warnings": [],
    "import_errors": [],
    "runtime_errors": []
}

def log_result(test_name: str, status: str, message: str = "", error: str = ""):
    """Log test result."""
    result = {
        "test": test_name,
        "status": status,
        "message": message,
        "error": error,
        "timestamp": datetime.now().isoformat()
    }
    
    if status == "PASS":
        test_results["passed"].append(result)
        print(f"✅ {test_name}: {message}")
    elif status == "FAIL":
        test_results["failed"].append(result)
        print(f"❌ {test_name}: {message}")
        if error:
            print(f"   Error: {error}")
    elif status == "WARN":
        test_results["warnings"].append(result)
        print(f"⚠️  {test_name}: {message}")
    elif status == "IMPORT_ERROR":
        test_results["import_errors"].append(result)
        print(f"🚫 {test_name}: Import failed - {error}")
    elif status == "RUNTIME_ERROR":
        test_results["runtime_errors"].append(result)
        print(f"💥 {test_name}: Runtime error - {error}")

async def test_knowledge_taxonomy():
    """Test Knowledge Taxonomy System."""
    try:
        # Add the services path to sys.path
        sys.path.append('services/knowledge-base-service/src')
        
        from core.knowledge_taxonomy import KnowledgeTaxonomy, TaxonomyNode, ClassificationResult
        
        log_result("Knowledge Taxonomy Import", "PASS", "Successfully imported all classes")
        
        # Test initialization
        config = {"test": True}
        taxonomy = KnowledgeTaxonomy(config)
        log_result("Knowledge Taxonomy Init", "PASS", "Successfully initialized taxonomy system")
        
        # Test content classification
        test_content = "How do I configure the API authentication settings?"
        result = await taxonomy.classify_content(test_content, "API Configuration")
        
        if isinstance(result, ClassificationResult):
            log_result("Knowledge Taxonomy Classification", "PASS", 
                      f"Classification completed with {len(result.alternative_paths)} alternative paths")
        else:
            log_result("Knowledge Taxonomy Classification", "FAIL", "Invalid classification result type")
        
        # Test taxonomy structure
        structure = await taxonomy.get_taxonomy_structure()
        if isinstance(structure, dict) and len(structure) > 0:
            log_result("Knowledge Taxonomy Structure", "PASS", 
                      f"Retrieved taxonomy structure with {len(structure)} root nodes")
        else:
            log_result("Knowledge Taxonomy Structure", "FAIL", "Empty or invalid taxonomy structure")
            
    except ImportError as e:
        log_result("Knowledge Taxonomy", "IMPORT_ERROR", "", str(e))
    except Exception as e:
        log_result("Knowledge Taxonomy", "RUNTIME_ERROR", "", str(e))

async def test_content_categorizer():
    """Test Content Categorizer."""
    try:
        sys.path.append('services/knowledge-base-service/src')
        
        from core.content_categorizer import ContentCategorizer, ClassificationResult, CategoryType
        
        log_result("Content Categorizer Import", "PASS", "Successfully imported all classes")
        
        # Test initialization
        config = {"test": True}
        categorizer = ContentCategorizer(config)
        log_result("Content Categorizer Init", "PASS", "Successfully initialized categorizer")
        
        # Test content classification
        test_content = "I need help with billing issues and payment problems"
        result = await categorizer.classify_content(test_content, "Billing Help")
        
        if isinstance(result, ClassificationResult):
            log_result("Content Categorizer Classification", "PASS", 
                      f"Classification completed with confidence {result.overall_confidence:.2f}")
        else:
            log_result("Content Categorizer Classification", "FAIL", "Invalid classification result")
        
        # Test statistics
        stats = await categorizer.get_category_statistics()
        if isinstance(stats, dict):
            log_result("Content Categorizer Stats", "PASS", "Successfully retrieved statistics")
        else:
            log_result("Content Categorizer Stats", "FAIL", "Invalid statistics format")
            
    except ImportError as e:
        log_result("Content Categorizer", "IMPORT_ERROR", "", str(e))
    except Exception as e:
        log_result("Content Categorizer", "RUNTIME_ERROR", "", str(e))

async def test_response_formatter():
    """Test Response Formatter."""
    try:
        sys.path.append('services/conversation-service/src')
        
        from core.response_formatter import ResponseFormatter, FormattingOptions, ResponseFormat, ContentType
        
        log_result("Response Formatter Import", "PASS", "Successfully imported all classes")
        
        # Test initialization
        formatter = ResponseFormatter()
        log_result("Response Formatter Init", "PASS", "Successfully initialized formatter")
        
        # Test formatting
        test_content = "Step 1: Login to your account. Step 2: Navigate to settings. Step 3: Update your preferences."
        options = FormattingOptions(
            format_type=ResponseFormat.MARKDOWN,
            content_type=ContentType.STEP_BY_STEP
        )
        
        result = await formatter.format_response(test_content, options)
        
        if hasattr(result, 'content') and hasattr(result, 'word_count'):
            log_result("Response Formatter Formatting", "PASS", 
                      f"Formatted response with {result.word_count} words")
        else:
            log_result("Response Formatter Formatting", "FAIL", "Invalid formatting result")
            
    except ImportError as e:
        log_result("Response Formatter", "IMPORT_ERROR", "", str(e))
    except Exception as e:
        log_result("Response Formatter", "RUNTIME_ERROR", "", str(e))

async def test_evidence_tracker():
    """Test Evidence Tracker."""
    try:
        sys.path.append('services/conversation-service/src')
        
        from core.evidence_tracker import EvidenceTracker, EvidenceMap, SourceEvidence
        
        log_result("Evidence Tracker Import", "PASS", "Successfully imported all classes")
        
        # Test initialization
        tracker = EvidenceTracker()
        log_result("Evidence Tracker Init", "PASS", "Successfully initialized tracker")
        
        # Test evidence mapping
        response_content = "Based on our documentation, you can reset your password by clicking the forgot password link."
        source_docs = [
            {
                "id": "doc1",
                "title": "Password Reset Guide",
                "content": "To reset your password, click the forgot password link on the login page."
            }
        ]
        
        evidence_map = await tracker.create_evidence_map(response_content, source_docs)
        
        if isinstance(evidence_map, EvidenceMap):
            log_result("Evidence Tracker Mapping", "PASS", 
                      f"Created evidence map with {len(evidence_map.segments)} segments")
        else:
            log_result("Evidence Tracker Mapping", "FAIL", "Invalid evidence map")
            
    except ImportError as e:
        log_result("Evidence Tracker", "IMPORT_ERROR", "", str(e))
    except Exception as e:
        log_result("Evidence Tracker", "RUNTIME_ERROR", "", str(e))

async def test_uncertainty_detector():
    """Test Uncertainty Detector."""
    try:
        sys.path.append('services/conversation-service/src')
        
        from core.uncertainty_detector import UncertaintyDetector, UncertaintyAssessment, UncertaintyLevel
        
        log_result("Uncertainty Detector Import", "PASS", "Successfully imported all classes")
        
        # Test initialization
        detector = UncertaintyDetector()
        log_result("Uncertainty Detector Init", "PASS", "Successfully initialized detector")
        
        # Test uncertainty assessment
        query = "I'm not sure about this complex technical issue"
        documents = [
            {"content": "Some technical information", "id": "doc1", "title": "Tech Doc"}
        ]
        
        assessment = await detector.assess_uncertainty(query, documents)
        
        if isinstance(assessment, UncertaintyAssessment):
            log_result("Uncertainty Detector Assessment", "PASS", 
                      f"Assessment completed with uncertainty level: {assessment.uncertainty_level.value}")
        else:
            log_result("Uncertainty Detector Assessment", "FAIL", "Invalid assessment result")
            
    except ImportError as e:
        log_result("Uncertainty Detector", "IMPORT_ERROR", "", str(e))
    except Exception as e:
        log_result("Uncertainty Detector", "RUNTIME_ERROR", "", str(e))

async def test_escalation_manager():
    """Test Escalation Manager."""
    try:
        sys.path.append('services/conversation-service/src')
        
        from core.escalation_manager import EscalationManager, EscalationTicket, CustomerContext, EscalationReason, EscalationPriority
        
        log_result("Escalation Manager Import", "PASS", "Successfully imported all classes")
        
        # Test initialization
        manager = EscalationManager()
        log_result("Escalation Manager Init", "PASS", "Successfully initialized manager")
        
        # Test escalation evaluation
        query = "This is urgent! I need immediate help with a critical issue!"
        context = []
        customer_context = CustomerContext(customer_id="test123", customer_tier="enterprise")
        
        evaluation = await manager.evaluate_escalation_need(query, context, customer_context)
        
        if isinstance(evaluation, dict) and "should_escalate" in evaluation:
            log_result("Escalation Manager Evaluation", "PASS", 
                      f"Evaluation completed: should_escalate={evaluation['should_escalate']}")
        else:
            log_result("Escalation Manager Evaluation", "FAIL", "Invalid evaluation result")
            
    except ImportError as e:
        log_result("Escalation Manager", "IMPORT_ERROR", "", str(e))
    except Exception as e:
        log_result("Escalation Manager", "RUNTIME_ERROR", "", str(e))

async def test_consistency_validator():
    """Test Consistency Validator."""
    try:
        sys.path.append('services/conversation-service/src')
        
        from core.consistency_validator import ConsistencyValidator, ConsistencyReport, ConsistencyLevel
        
        log_result("Consistency Validator Import", "PASS", "Successfully imported all classes")
        
        # Test initialization
        validator = ConsistencyValidator()
        log_result("Consistency Validator Init", "PASS", "Successfully initialized validator")
        
        # Test validation
        response_content = "We guarantee 100% uptime and promise immediate refunds for any issues."
        
        report = await validator.validate_consistency(response_content)
        
        if isinstance(report, ConsistencyReport):
            log_result("Consistency Validator Validation", "PASS", 
                      f"Validation completed with {len(report.violations)} violations")
        else:
            log_result("Consistency Validator Validation", "FAIL", "Invalid validation report")
            
    except ImportError as e:
        log_result("Consistency Validator", "IMPORT_ERROR", "", str(e))
    except Exception as e:
        log_result("Consistency Validator", "RUNTIME_ERROR", "", str(e))

async def test_query_router():
    """Test Query Router."""
    try:
        sys.path.append('services/knowledge-base-service/src')
        
        from core.query_router import QueryRouter, RoutingDecision, QueryDomain, QueryComplexity
        
        log_result("Query Router Import", "PASS", "Successfully imported all classes")
        
        # Test initialization
        router = QueryRouter()
        log_result("Query Router Init", "PASS", "Successfully initialized router")
        
        # Test query routing
        query = "How do I integrate the API with my application?"
        
        decision = await router.route_query(query)
        
        if isinstance(decision, RoutingDecision):
            log_result("Query Router Routing", "PASS", 
                      f"Routing completed: domain={decision.primary_domain.value}, retriever={decision.primary_retriever}")
        else:
            log_result("Query Router Routing", "FAIL", "Invalid routing decision")
            
    except ImportError as e:
        log_result("Query Router", "IMPORT_ERROR", "", str(e))
    except Exception as e:
        log_result("Query Router", "RUNTIME_ERROR", "", str(e))

async def test_domain_retrievers():
    """Test Domain Retrievers."""
    try:
        sys.path.append('services/knowledge-base-service/src')
        
        from core.domain_retrievers import DomainRetrieverManager, DomainRetrievalRequest, RetrievalDomain
        
        log_result("Domain Retrievers Import", "PASS", "Successfully imported all classes")
        
        # Test initialization
        manager = DomainRetrieverManager()
        log_result("Domain Retrievers Init", "PASS", "Successfully initialized manager")
        
        # Test retrieval
        request = DomainRetrievalRequest(
            query="How do I fix login errors?",
            domain=RetrievalDomain.TROUBLESHOOTING
        )
        
        results = await manager.retrieve(RetrievalDomain.TROUBLESHOOTING, request)
        
        if isinstance(results, list):
            log_result("Domain Retrievers Retrieval", "PASS", 
                      f"Retrieval completed with {len(results)} results")
        else:
            log_result("Domain Retrievers Retrieval", "FAIL", "Invalid retrieval results")
            
    except ImportError as e:
        log_result("Domain Retrievers", "IMPORT_ERROR", "", str(e))
    except Exception as e:
        log_result("Domain Retrievers", "RUNTIME_ERROR", "", str(e))

async def run_all_tests():
    """Run all tests."""
    print("🧪 Starting comprehensive functionality tests...\n")
    
    tests = [
        test_knowledge_taxonomy,
        test_content_categorizer,
        test_response_formatter,
        test_evidence_tracker,
        test_uncertainty_detector,
        test_escalation_manager,
        test_consistency_validator,
        test_query_router,
        test_domain_retrievers
    ]
    
    for test_func in tests:
        try:
            await test_func()
        except Exception as e:
            log_result(test_func.__name__, "RUNTIME_ERROR", "", str(e))
        print()  # Add spacing between tests

def print_summary():
    """Print test summary."""
    print("=" * 80)
    print("📊 TEST SUMMARY")
    print("=" * 80)
    
    total_tests = (len(test_results["passed"]) + len(test_results["failed"]) + 
                  len(test_results["warnings"]) + len(test_results["import_errors"]) + 
                  len(test_results["runtime_errors"]))
    
    print(f"Total Tests: {total_tests}")
    print(f"✅ Passed: {len(test_results['passed'])}")
    print(f"❌ Failed: {len(test_results['failed'])}")
    print(f"⚠️  Warnings: {len(test_results['warnings'])}")
    print(f"🚫 Import Errors: {len(test_results['import_errors'])}")
    print(f"💥 Runtime Errors: {len(test_results['runtime_errors'])}")
    
    success_rate = (len(test_results["passed"]) / total_tests * 100) if total_tests > 0 else 0
    print(f"\n📈 Success Rate: {success_rate:.1f}%")
    
    # Print detailed errors
    if test_results["import_errors"]:
        print("\n🚫 IMPORT ERRORS:")
        for error in test_results["import_errors"]:
            print(f"   - {error['test']}: {error['error']}")
    
    if test_results["runtime_errors"]:
        print("\n💥 RUNTIME ERRORS:")
        for error in test_results["runtime_errors"]:
            print(f"   - {error['test']}: {error['error']}")
    
    if test_results["failed"]:
        print("\n❌ FAILED TESTS:")
        for failure in test_results["failed"]:
            print(f"   - {failure['test']}: {failure['message']}")
            if failure["error"]:
                print(f"     Error: {failure['error']}")

if __name__ == "__main__":
    asyncio.run(run_all_tests())
    print_summary()

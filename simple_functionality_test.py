#!/usr/bin/env python3
"""
Simple functionality test for Phase 1 and Phase 2 implementations
Tests basic imports and class instantiation
"""

import sys
import os
import traceback

# Add service paths
sys.path.insert(0, os.path.join(os.getcwd(), 'services', 'knowledge-base-service', 'src'))
sys.path.insert(0, os.path.join(os.getcwd(), 'services', 'conversation-service', 'src'))

def test_imports():
    """Test all imports."""
    results = {}
    
    # Test Knowledge Taxonomy
    try:
        from core.knowledge_taxonomy import KnowledgeTaxonomy, TaxonomyNode, ClassificationResult
        results['knowledge_taxonomy'] = {'status': 'PASS', 'error': None}
        print("✅ Knowledge Taxonomy - Import successful")
    except Exception as e:
        results['knowledge_taxonomy'] = {'status': 'FAIL', 'error': str(e)}
        print(f"❌ Knowledge Taxonomy - Import failed: {e}")
    
    # Test Content Categorizer
    try:
        from core.content_categorizer import ContentCategorizer, ClassificationResult as CatResult, CategoryType
        results['content_categorizer'] = {'status': 'PASS', 'error': None}
        print("✅ Content Categorizer - Import successful")
    except Exception as e:
        results['content_categorizer'] = {'status': 'FAIL', 'error': str(e)}
        print(f"❌ Content Categorizer - Import failed: {e}")
    
    # Test Response Formatter
    try:
        from core.response_formatter import ResponseFormatter, FormattingOptions, ResponseFormat
        results['response_formatter'] = {'status': 'PASS', 'error': None}
        print("✅ Response Formatter - Import successful")
    except Exception as e:
        results['response_formatter'] = {'status': 'FAIL', 'error': str(e)}
        print(f"❌ Response Formatter - Import failed: {e}")
    
    # Test Evidence Tracker
    try:
        from core.evidence_tracker import EvidenceTracker, EvidenceMap, SourceEvidence
        results['evidence_tracker'] = {'status': 'PASS', 'error': None}
        print("✅ Evidence Tracker - Import successful")
    except Exception as e:
        results['evidence_tracker'] = {'status': 'FAIL', 'error': str(e)}
        print(f"❌ Evidence Tracker - Import failed: {e}")
    
    # Test Uncertainty Detector
    try:
        from core.uncertainty_detector import UncertaintyDetector, UncertaintyAssessment
        results['uncertainty_detector'] = {'status': 'PASS', 'error': None}
        print("✅ Uncertainty Detector - Import successful")
    except Exception as e:
        results['uncertainty_detector'] = {'status': 'FAIL', 'error': str(e)}
        print(f"❌ Uncertainty Detector - Import failed: {e}")
    
    # Test Escalation Manager
    try:
        from core.escalation_manager import EscalationManager, EscalationTicket, CustomerContext
        results['escalation_manager'] = {'status': 'PASS', 'error': None}
        print("✅ Escalation Manager - Import successful")
    except Exception as e:
        results['escalation_manager'] = {'status': 'FAIL', 'error': str(e)}
        print(f"❌ Escalation Manager - Import failed: {e}")
    
    # Test Consistency Validator
    try:
        from core.consistency_validator import ConsistencyValidator, ConsistencyReport
        results['consistency_validator'] = {'status': 'PASS', 'error': None}
        print("✅ Consistency Validator - Import successful")
    except Exception as e:
        results['consistency_validator'] = {'status': 'FAIL', 'error': str(e)}
        print(f"❌ Consistency Validator - Import failed: {e}")
    
    # Test Query Router
    try:
        from core.query_router import QueryRouter, RoutingDecision, QueryDomain
        results['query_router'] = {'status': 'PASS', 'error': None}
        print("✅ Query Router - Import successful")
    except Exception as e:
        results['query_router'] = {'status': 'FAIL', 'error': str(e)}
        print(f"❌ Query Router - Import failed: {e}")
    
    # Test Domain Retrievers
    try:
        from core.domain_retrievers import DomainRetrieverManager, RetrievalDomain
        results['domain_retrievers'] = {'status': 'PASS', 'error': None}
        print("✅ Domain Retrievers - Import successful")
    except Exception as e:
        results['domain_retrievers'] = {'status': 'FAIL', 'error': str(e)}
        print(f"❌ Domain Retrievers - Import failed: {e}")
    
    return results

def test_basic_instantiation():
    """Test basic class instantiation."""
    results = {}
    
    # Test Knowledge Taxonomy instantiation
    try:
        from core.knowledge_taxonomy import KnowledgeTaxonomy
        taxonomy = KnowledgeTaxonomy({})
        results['knowledge_taxonomy_init'] = {'status': 'PASS', 'error': None}
        print("✅ Knowledge Taxonomy - Instantiation successful")
    except Exception as e:
        results['knowledge_taxonomy_init'] = {'status': 'FAIL', 'error': str(e)}
        print(f"❌ Knowledge Taxonomy - Instantiation failed: {e}")
    
    # Test Content Categorizer instantiation
    try:
        from core.content_categorizer import ContentCategorizer
        categorizer = ContentCategorizer({})
        results['content_categorizer_init'] = {'status': 'PASS', 'error': None}
        print("✅ Content Categorizer - Instantiation successful")
    except Exception as e:
        results['content_categorizer_init'] = {'status': 'FAIL', 'error': str(e)}
        print(f"❌ Content Categorizer - Instantiation failed: {e}")
    
    # Test Response Formatter instantiation
    try:
        from core.response_formatter import ResponseFormatter
        formatter = ResponseFormatter()
        results['response_formatter_init'] = {'status': 'PASS', 'error': None}
        print("✅ Response Formatter - Instantiation successful")
    except Exception as e:
        results['response_formatter_init'] = {'status': 'FAIL', 'error': str(e)}
        print(f"❌ Response Formatter - Instantiation failed: {e}")
    
    # Test Evidence Tracker instantiation
    try:
        from core.evidence_tracker import EvidenceTracker
        tracker = EvidenceTracker()
        results['evidence_tracker_init'] = {'status': 'PASS', 'error': None}
        print("✅ Evidence Tracker - Instantiation successful")
    except Exception as e:
        results['evidence_tracker_init'] = {'status': 'FAIL', 'error': str(e)}
        print(f"❌ Evidence Tracker - Instantiation failed: {e}")
    
    # Test Uncertainty Detector instantiation
    try:
        from core.uncertainty_detector import UncertaintyDetector
        detector = UncertaintyDetector()
        results['uncertainty_detector_init'] = {'status': 'PASS', 'error': None}
        print("✅ Uncertainty Detector - Instantiation successful")
    except Exception as e:
        results['uncertainty_detector_init'] = {'status': 'FAIL', 'error': str(e)}
        print(f"❌ Uncertainty Detector - Instantiation failed: {e}")
    
    # Test Escalation Manager instantiation
    try:
        from core.escalation_manager import EscalationManager
        manager = EscalationManager()
        results['escalation_manager_init'] = {'status': 'PASS', 'error': None}
        print("✅ Escalation Manager - Instantiation successful")
    except Exception as e:
        results['escalation_manager_init'] = {'status': 'FAIL', 'error': str(e)}
        print(f"❌ Escalation Manager - Instantiation failed: {e}")
    
    # Test Consistency Validator instantiation
    try:
        from core.consistency_validator import ConsistencyValidator
        validator = ConsistencyValidator()
        results['consistency_validator_init'] = {'status': 'PASS', 'error': None}
        print("✅ Consistency Validator - Instantiation successful")
    except Exception as e:
        results['consistency_validator_init'] = {'status': 'FAIL', 'error': str(e)}
        print(f"❌ Consistency Validator - Instantiation failed: {e}")
    
    # Test Query Router instantiation
    try:
        from core.query_router import QueryRouter
        router = QueryRouter()
        results['query_router_init'] = {'status': 'PASS', 'error': None}
        print("✅ Query Router - Instantiation successful")
    except Exception as e:
        results['query_router_init'] = {'status': 'FAIL', 'error': str(e)}
        print(f"❌ Query Router - Instantiation failed: {e}")
    
    # Test Domain Retrievers instantiation
    try:
        from core.domain_retrievers import DomainRetrieverManager
        manager = DomainRetrieverManager()
        results['domain_retrievers_init'] = {'status': 'PASS', 'error': None}
        print("✅ Domain Retrievers - Instantiation successful")
    except Exception as e:
        results['domain_retrievers_init'] = {'status': 'FAIL', 'error': str(e)}
        print(f"❌ Domain Retrievers - Instantiation failed: {e}")
    
    return results

def analyze_syntax_errors():
    """Check for syntax errors in all files."""
    files_to_check = [
        'services/knowledge-base-service/src/core/knowledge_taxonomy.py',
        'services/knowledge-base-service/src/core/content_categorizer.py',
        'services/conversation-service/src/core/response_formatter.py',
        'services/conversation-service/src/core/evidence_tracker.py',
        'services/conversation-service/src/core/uncertainty_detector.py',
        'services/conversation-service/src/core/escalation_manager.py',
        'services/conversation-service/src/core/consistency_validator.py',
        'services/knowledge-base-service/src/core/query_router.py',
        'services/knowledge-base-service/src/core/domain_retrievers.py'
    ]
    
    syntax_results = {}
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Try to compile the code
            compile(content, file_path, 'exec')
            syntax_results[file_path] = {'status': 'PASS', 'error': None}
            print(f"✅ {file_path} - Syntax OK")
            
        except SyntaxError as e:
            syntax_results[file_path] = {'status': 'FAIL', 'error': f"Syntax Error: {e}"}
            print(f"❌ {file_path} - Syntax Error: {e}")
        except FileNotFoundError:
            syntax_results[file_path] = {'status': 'FAIL', 'error': "File not found"}
            print(f"❌ {file_path} - File not found")
        except Exception as e:
            syntax_results[file_path] = {'status': 'FAIL', 'error': str(e)}
            print(f"❌ {file_path} - Error: {e}")
    
    return syntax_results

def main():
    """Run all tests."""
    print("🔍 Phase 1 & 2 Implementation Analysis")
    print("=" * 50)
    
    print("\n📋 1. Syntax Analysis")
    print("-" * 30)
    syntax_results = analyze_syntax_errors()
    
    print("\n📦 2. Import Tests")
    print("-" * 30)
    import_results = test_imports()
    
    print("\n🏗️  3. Instantiation Tests")
    print("-" * 30)
    init_results = test_basic_instantiation()
    
    # Summary
    print("\n📊 SUMMARY")
    print("=" * 50)
    
    total_syntax = len(syntax_results)
    passed_syntax = sum(1 for r in syntax_results.values() if r['status'] == 'PASS')
    
    total_imports = len(import_results)
    passed_imports = sum(1 for r in import_results.values() if r['status'] == 'PASS')
    
    total_inits = len(init_results)
    passed_inits = sum(1 for r in init_results.values() if r['status'] == 'PASS')
    
    print(f"Syntax Check: {passed_syntax}/{total_syntax} files passed")
    print(f"Import Test: {passed_imports}/{total_imports} modules passed")
    print(f"Instantiation Test: {passed_inits}/{total_inits} classes passed")
    
    overall_success = (passed_syntax + passed_imports + passed_inits) / (total_syntax + total_imports + total_inits) * 100
    print(f"\nOverall Success Rate: {overall_success:.1f}%")
    
    # List failures
    failures = []
    for name, result in {**syntax_results, **import_results, **init_results}.items():
        if result['status'] == 'FAIL':
            failures.append(f"{name}: {result['error']}")
    
    if failures:
        print(f"\n❌ Issues Found ({len(failures)}):")
        for failure in failures:
            print(f"   - {failure}")
    else:
        print("\n🎉 All tests passed! Implementation is working correctly.")

if __name__ == "__main__":
    main()

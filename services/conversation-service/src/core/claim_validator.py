from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import logging
import json
import re
from datetime import datetime
import asyncio
from langchain_openai import ChatOpenAI
from langchain.schema import BaseMessage, HumanMessage, SystemMessage


class ValidationStatus(Enum):
    """Status of claim validation."""
    VALIDATED = "validated"           # Claim fully supported by sources
    PARTIALLY_SUPPORTED = "partial"  # Claim partially supported
    UNSUPPORTED = "unsupported"     # No source support found
    CONFLICTING = "conflicting"     # Sources provide conflicting information
    INSUFFICIENT_INFO = "insufficient" # Not enough information to validate


class ValidationSeverity(Enum):
    """Severity levels for validation issues."""
    CRITICAL = "critical"    # Major factual errors
    HIGH = "high"           # Significant unsupported claims
    MEDIUM = "medium"       # Minor unsupported details
    LOW = "low"            # Style or presentation issues


@dataclass
class ValidationIssue:
    """Represents a validation issue found in a claim."""
    issue_type: str
    severity: ValidationSeverity
    description: str
    claim_text: str
    expected_sources: List[str] = field(default_factory=list)
    actual_sources: List[str] = field(default_factory=list)
    suggestion: str = ""
    position: Tuple[int, int] = (0, 0)  # Start, end positions in text


@dataclass
class SourceEvidence:
    """Evidence from a source supporting or contradicting a claim."""
    source_id: str
    source_title: str
    evidence_text: str
    support_type: str  # "supporting", "contradicting", "neutral"
    confidence_score: float
    relevance_score: float
    position_in_source: int = 0


@dataclass
class ClaimValidationResult:
    """Result of validating a single claim."""
    claim_text: str
    validation_status: ValidationStatus
    confidence_score: float
    supporting_evidence: List[SourceEvidence]
    contradicting_evidence: List[SourceEvidence]
    validation_issues: List[ValidationIssue]
    claim_type: str
    factual_accuracy_score: float
    source_alignment_score: float


@dataclass
class ValidationReport:
    """Complete validation report for a response."""
    overall_status: ValidationStatus
    overall_confidence: float
    total_claims: int
    validated_claims: int
    validation_results: List[ClaimValidationResult]
    critical_issues: List[ValidationIssue]
    recommendations: List[str]
    metadata: Dict[str, Any]


class ClaimValidator:
    """
    Validates response claims against source documents to prevent hallucination.
    Ensures factual accuracy and source alignment.
    """
    
    def __init__(self, 
                 model_name: str = "gpt-4o-mini",
                 validation_temperature: float = 0.05):
        """
        Initialize the claim validator.
        
        Args:
            model_name: LLM model to use for validation
            validation_temperature: Very low temperature for consistent validation
        """
        self.model_name = model_name
        self.validation_temperature = validation_temperature
        
        # Initialize validation LLM with conservative settings
        self.validator_llm = ChatOpenAI(
            model=model_name,
            temperature=validation_temperature,
            max_tokens=800
        )
        
        # Logger
        self.logger = logging.getLogger(__name__)
        
        # Validation patterns for different claim types
        self.claim_type_validators = {
            "factual": self._validate_factual_claim,
            "procedural": self._validate_procedural_claim,
            "quantitative": self._validate_quantitative_claim,
            "definition": self._validate_definition_claim,
            "comparative": self._validate_comparative_claim
        }
        
        # Critical validation patterns
        self.critical_patterns = [
            r'\b(?:never|always|all|none|impossible|guaranteed|certainly)\b',
            r'\b\d+(?:\.\d+)?(?:%|percent|dollars?|years?|months?|days?)\b',
            r'\b(?:first|second|third|last|only|best|worst|most|least)\b'
        ]
        
        # Hallucination indicators
        self.hallucination_indicators = [
            "as everyone knows", "obviously", "clearly", "it's well known",
            "studies show", "research indicates", "experts agree",
            "according to statistics", "data shows", "it's proven"
        ]
    
    async def validate_claims(self,
                            response_text: str,
                            source_documents: List[Dict[str, Any]],
                            query: str = "",
                            query_type: str = "general") -> ValidationReport:
        """
        Validate all claims in a response against source documents.
        
        Args:
            response_text: The response text to validate
            source_documents: List of source documents used for the response
            query: Original query for context
            query_type: Type of query for validation strategy
            
        Returns:
            ValidationReport with detailed validation results
        """
        try:
            self.logger.info(f"Validating response with {len(source_documents)} sources")
            
            # Step 1: Extract claims from response
            claims = self._extract_claims_from_response(response_text)
            
            if not claims:
                return self._create_no_claims_report(response_text, query)
            
            # Step 2: Validate each claim
            validation_results = []
            for claim in claims:
                result = await self._validate_single_claim(
                    claim, source_documents, query_type
                )
                validation_results.append(result)
            
            # Step 3: Analyze overall validation status
            overall_status, overall_confidence = self._calculate_overall_validation(
                validation_results
            )
            
            # Step 4: Identify critical issues
            critical_issues = self._identify_critical_issues(validation_results)
            
            # Step 5: Generate recommendations
            recommendations = self._generate_recommendations(validation_results, critical_issues)
            
            # Step 6: Create validation report
            validated_claims = len([r for r in validation_results 
                                 if r.validation_status == ValidationStatus.VALIDATED])
            
            validation_report = ValidationReport(
                overall_status=overall_status,
                overall_confidence=overall_confidence,
                total_claims=len(claims),
                validated_claims=validated_claims,
                validation_results=validation_results,
                critical_issues=critical_issues,
                recommendations=recommendations,
                metadata={
                    "query": query,
                    "query_type": query_type,
                    "num_sources": len(source_documents),
                    "validation_timestamp": datetime.now().isoformat(),
                    "validator_model": self.model_name,
                    "response_length": len(response_text)
                }
            )
            
            self.logger.info(f"Validation complete: {validated_claims}/{len(claims)} claims validated, "
                           f"overall confidence: {overall_confidence:.2f}")
            
            return validation_report
            
        except Exception as e:
            self.logger.error(f"Error in claim validation: {e}")
            return self._create_error_validation_report(response_text, str(e))
    
    def _extract_claims_from_response(self, response_text: str) -> List[str]:
        """Extract individual claims from response text."""
        
        # Split by sentences, but be smart about abbreviations and numbers
        sentences = re.split(r'(?<=[.!?])\s+(?=[A-Z])', response_text)
        
        claims = []
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            # Filter out very short sentences that are likely not claims
            if len(sentence) < 10:
                continue
                
            # Remove citation markers for cleaner validation
            clean_sentence = re.sub(r'\[\d+\]', '', sentence).strip()
            if clean_sentence:
                claims.append(clean_sentence)
        
        return claims
    
    async def _validate_single_claim(self,
                                   claim: str,
                                   source_documents: List[Dict[str, Any]],
                                   query_type: str) -> ClaimValidationResult:
        """Validate a single claim against source documents."""
        
        # Step 1: Classify claim type
        claim_type = self._classify_claim_for_validation(claim)
        
        # Step 2: Find relevant evidence in sources
        supporting_evidence, contradicting_evidence = await self._find_evidence_for_claim(
            claim, source_documents
        )
        
        # Step 3: Determine validation status
        validation_status = self._determine_validation_status(
            supporting_evidence, contradicting_evidence
        )
        
        # Step 4: Calculate confidence scores
        confidence_score = self._calculate_claim_confidence(
            supporting_evidence, contradicting_evidence
        )
        
        factual_accuracy_score = self._calculate_factual_accuracy(
            claim, supporting_evidence, contradicting_evidence
        )
        
        source_alignment_score = self._calculate_source_alignment(
            claim, supporting_evidence
        )
        
        # Step 5: Identify validation issues
        validation_issues = self._identify_claim_issues(
            claim, supporting_evidence, contradicting_evidence, validation_status
        )
        
        return ClaimValidationResult(
            claim_text=claim,
            validation_status=validation_status,
            confidence_score=confidence_score,
            supporting_evidence=supporting_evidence,
            contradicting_evidence=contradicting_evidence,
            validation_issues=validation_issues,
            claim_type=claim_type,
            factual_accuracy_score=factual_accuracy_score,
            source_alignment_score=source_alignment_score
        )
    
    def _classify_claim_for_validation(self, claim: str) -> str:
        """Classify claim type for appropriate validation strategy."""
        claim_lower = claim.lower()
        
        # Check for quantitative claims
        if re.search(r'\b\d+(?:\.\d+)?(?:%|percent|dollars?|years?|months?|days?)\b', claim_lower):
            return "quantitative"
        
        # Check for procedural claims
        if any(word in claim_lower for word in ["step", "first", "then", "next", "procedure"]):
            return "procedural"
        
        # Check for definition claims
        if any(word in claim_lower for word in ["is defined as", "refers to", "means", "is a"]):
            return "definition"
        
        # Check for comparative claims
        if any(word in claim_lower for word in ["better", "worse", "more", "less", "compared to"]):
            return "comparative"
        
        # Default to factual
        return "factual"
    
    async def _find_evidence_for_claim(self,
                                     claim: str,
                                     source_documents: List[Dict[str, Any]]) -> Tuple[List[SourceEvidence], List[SourceEvidence]]:
        """Find supporting and contradicting evidence for a claim."""
        
        supporting_evidence = []
        contradicting_evidence = []
        
        for doc in source_documents:
            doc_id = doc.get("id", "unknown")
            doc_title = doc.get("title", "")
            doc_content = doc.get("content", "")
            
            # Use LLM to find evidence in document
            evidence = await self._extract_evidence_from_document(
                claim, doc_content, doc_id, doc_title
            )
            
            if evidence.support_type == "supporting":
                supporting_evidence.append(evidence)
            elif evidence.support_type == "contradicting":
                contradicting_evidence.append(evidence)
        
        return supporting_evidence, contradicting_evidence
    
    async def _extract_evidence_from_document(self,
                                            claim: str,
                                            document_content: str,
                                            doc_id: str,
                                            doc_title: str) -> SourceEvidence:
        """Extract evidence from a single document for a claim."""
        
        system_prompt = """You are a precise evidence extraction system. Your task is to find evidence in a document that either supports or contradicts a given claim.

INSTRUCTIONS:
1. Carefully analyze the document content for information related to the claim
2. Extract the most relevant text passage that relates to the claim
3. Determine if the evidence SUPPORTS, CONTRADICTS, or is NEUTRAL to the claim
4. Provide confidence and relevance scores (0.0-1.0)
5. Be conservative - only mark as supporting/contradicting if there's clear evidence

OUTPUT FORMAT:
SUPPORT_TYPE: [supporting/contradicting/neutral]
EVIDENCE_TEXT: [exact quote from document]
CONFIDENCE: [0.0-1.0]
RELEVANCE: [0.0-1.0]
REASONING: [brief explanation]"""

        user_prompt = f"""CLAIM TO VALIDATE: {claim}

DOCUMENT CONTENT:
{document_content[:1500]}

Find the most relevant evidence for or against this claim."""

        try:
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = await self.validator_llm.agenerate([messages])
            result_text = response.generations[0][0].text.strip()
            
            # Parse the structured response
            return self._parse_evidence_response(result_text, doc_id, doc_title)
            
        except Exception as e:
            self.logger.error(f"Error extracting evidence: {e}")
            # Return neutral evidence with low confidence
            return SourceEvidence(
                source_id=doc_id,
                source_title=doc_title,
                evidence_text="",
                support_type="neutral",
                confidence_score=0.0,
                relevance_score=0.0
            )
    
    def _parse_evidence_response(self, response_text: str, doc_id: str, doc_title: str) -> SourceEvidence:
        """Parse structured evidence response from LLM."""
        
        # Extract structured information
        support_type = "neutral"
        evidence_text = ""
        confidence_score = 0.0
        relevance_score = 0.0
        
        try:
            # Extract support type
            support_match = re.search(r'SUPPORT_TYPE:\s*(\w+)', response_text, re.IGNORECASE)
            if support_match:
                support_type = support_match.group(1).lower()
            
            # Extract evidence text
            evidence_match = re.search(r'EVIDENCE_TEXT:\s*(.+?)(?=\n[A-Z_]+:|$)', response_text, re.DOTALL | re.IGNORECASE)
            if evidence_match:
                evidence_text = evidence_match.group(1).strip()
            
            # Extract confidence
            confidence_match = re.search(r'CONFIDENCE:\s*([\d.]+)', response_text, re.IGNORECASE)
            if confidence_match:
                confidence_score = float(confidence_match.group(1))
            
            # Extract relevance
            relevance_match = re.search(r'RELEVANCE:\s*([\d.]+)', response_text, re.IGNORECASE)
            if relevance_match:
                relevance_score = float(relevance_match.group(1))
                
        except Exception as e:
            self.logger.warning(f"Error parsing evidence response: {e}")
        
        return SourceEvidence(
            source_id=doc_id,
            source_title=doc_title,
            evidence_text=evidence_text,
            support_type=support_type,
            confidence_score=confidence_score,
            relevance_score=relevance_score
        )
    
    def _determine_validation_status(self,
                                   supporting_evidence: List[SourceEvidence],
                                   contradicting_evidence: List[SourceEvidence]) -> ValidationStatus:
        """Determine overall validation status for a claim."""
        
        # Calculate evidence strength
        support_strength = sum(e.confidence_score * e.relevance_score for e in supporting_evidence)
        contradict_strength = sum(e.confidence_score * e.relevance_score for e in contradicting_evidence)
        
        # Determine status based on evidence
        if contradict_strength > 0.3:
            if support_strength > contradict_strength:
                return ValidationStatus.CONFLICTING
            else:
                return ValidationStatus.UNSUPPORTED
        
        if support_strength > 0.7:
            return ValidationStatus.VALIDATED
        elif support_strength > 0.3:
            return ValidationStatus.PARTIALLY_SUPPORTED
        elif support_strength > 0.1:
            return ValidationStatus.INSUFFICIENT_INFO
        else:
            return ValidationStatus.UNSUPPORTED
    
    def _calculate_claim_confidence(self,
                                  supporting_evidence: List[SourceEvidence],
                                  contradicting_evidence: List[SourceEvidence]) -> float:
        """Calculate confidence score for claim validation."""
        
        if not supporting_evidence and not contradicting_evidence:
            return 0.0
        
        # Weight supporting evidence positively, contradicting negatively
        support_score = sum(e.confidence_score * e.relevance_score for e in supporting_evidence)
        contradict_score = sum(e.confidence_score * e.relevance_score for e in contradicting_evidence)
        
        # Calculate final confidence
        if support_score > 0:
            confidence = support_score / (support_score + contradict_score + 0.1)
        else:
            confidence = 0.0
        
        return min(1.0, max(0.0, confidence))
    
    def _calculate_factual_accuracy(self,
                                  claim: str,
                                  supporting_evidence: List[SourceEvidence],
                                  contradicting_evidence: List[SourceEvidence]) -> float:
        """Calculate factual accuracy score."""
        
        # Check for critical patterns that require high accuracy
        is_critical = any(re.search(pattern, claim, re.IGNORECASE) 
                         for pattern in self.critical_patterns)
        
        # Check for hallucination indicators
        has_hallucination_indicators = any(indicator in claim.lower() 
                                         for indicator in self.hallucination_indicators)
        
        if has_hallucination_indicators and not supporting_evidence:
            return 0.1  # Very low accuracy for unsupported broad claims
        
        if is_critical:
            # Critical claims need strong evidence
            support_strength = sum(e.confidence_score for e in supporting_evidence)
            return min(1.0, support_strength)
        
        # Regular factual accuracy calculation
        return self._calculate_claim_confidence(supporting_evidence, contradicting_evidence)
    
    def _calculate_source_alignment(self, claim: str, supporting_evidence: List[SourceEvidence]) -> float:
        """Calculate how well claim aligns with source content."""
        
        if not supporting_evidence:
            return 0.0
        
        alignment_scores = []
        for evidence in supporting_evidence:
            # Simple word overlap alignment (can be enhanced with semantic similarity)
            claim_words = set(claim.lower().split())
            evidence_words = set(evidence.evidence_text.lower().split())
            
            overlap = len(claim_words.intersection(evidence_words))
            alignment = overlap / len(claim_words) if claim_words else 0.0
            alignment_scores.append(alignment * evidence.relevance_score)
        
        return sum(alignment_scores) / len(alignment_scores) if alignment_scores else 0.0
    
    def _identify_claim_issues(self,
                             claim: str,
                             supporting_evidence: List[SourceEvidence],
                             contradicting_evidence: List[SourceEvidence],
                             validation_status: ValidationStatus) -> List[ValidationIssue]:
        """Identify specific issues with a claim."""
        
        issues = []
        
        # Check for unsupported claims
        if validation_status == ValidationStatus.UNSUPPORTED:
            issues.append(ValidationIssue(
                issue_type="unsupported_claim",
                severity=ValidationSeverity.HIGH,
                description="Claim has no supporting evidence in provided sources",
                claim_text=claim,
                suggestion="Remove claim or find supporting sources"
            ))
        
        # Check for conflicting information
        if validation_status == ValidationStatus.CONFLICTING:
            issues.append(ValidationIssue(
                issue_type="conflicting_sources",
                severity=ValidationSeverity.CRITICAL,
                description="Sources provide conflicting information about this claim",
                claim_text=claim,
                suggestion="Acknowledge uncertainty or present multiple perspectives"
            ))
        
        # Check for hallucination indicators
        for indicator in self.hallucination_indicators:
            if indicator in claim.lower() and not supporting_evidence:
                issues.append(ValidationIssue(
                    issue_type="potential_hallucination",
                    severity=ValidationSeverity.HIGH,
                    description=f"Contains indicator '{indicator}' without source support",
                    claim_text=claim,
                    suggestion="Remove unsupported generalizations"
                ))
        
        # Check for critical patterns without strong evidence
        for pattern in self.critical_patterns:
            if re.search(pattern, claim, re.IGNORECASE):
                support_strength = sum(e.confidence_score for e in supporting_evidence)
                if support_strength < 0.8:
                    issues.append(ValidationIssue(
                        issue_type="critical_claim_insufficient_support",
                        severity=ValidationSeverity.CRITICAL,
                        description="Critical claim lacks strong supporting evidence",
                        claim_text=claim,
                        suggestion="Provide stronger evidence or qualify the statement"
                    ))
        
        return issues
    
    def _calculate_overall_validation(self, 
                                    validation_results: List[ClaimValidationResult]) -> Tuple[ValidationStatus, float]:
        """Calculate overall validation status and confidence."""
        
        if not validation_results:
            return ValidationStatus.INSUFFICIENT_INFO, 0.0
        
        # Count validation statuses
        status_counts = {}
        total_confidence = 0.0
        
        for result in validation_results:
            status = result.validation_status
            status_counts[status] = status_counts.get(status, 0) + 1
            total_confidence += result.confidence_score
        
        avg_confidence = total_confidence / len(validation_results)
        
        # Determine overall status
        total_claims = len(validation_results)
        validated_count = status_counts.get(ValidationStatus.VALIDATED, 0)
        unsupported_count = status_counts.get(ValidationStatus.UNSUPPORTED, 0)
        conflicting_count = status_counts.get(ValidationStatus.CONFLICTING, 0)
        
        if conflicting_count > 0:
            overall_status = ValidationStatus.CONFLICTING
        elif unsupported_count > total_claims * 0.3:  # More than 30% unsupported
            overall_status = ValidationStatus.UNSUPPORTED
        elif validated_count > total_claims * 0.8:    # More than 80% validated
            overall_status = ValidationStatus.VALIDATED
        elif validated_count > total_claims * 0.5:    # More than 50% validated
            overall_status = ValidationStatus.PARTIALLY_SUPPORTED
        else:
            overall_status = ValidationStatus.INSUFFICIENT_INFO
        
        return overall_status, avg_confidence
    
    def _identify_critical_issues(self, validation_results: List[ClaimValidationResult]) -> List[ValidationIssue]:
        """Identify critical issues across all validation results."""
        
        critical_issues = []
        
        for result in validation_results:
            for issue in result.validation_issues:
                if issue.severity in [ValidationSeverity.CRITICAL, ValidationSeverity.HIGH]:
                    critical_issues.append(issue)
        
        # Sort by severity
        severity_order = {
            ValidationSeverity.CRITICAL: 0,
            ValidationSeverity.HIGH: 1,
            ValidationSeverity.MEDIUM: 2,
            ValidationSeverity.LOW: 3
        }
        
        critical_issues.sort(key=lambda x: severity_order[x.severity])
        
        return critical_issues
    
    def _generate_recommendations(self,
                                validation_results: List[ClaimValidationResult],
                                critical_issues: List[ValidationIssue]) -> List[str]:
        """Generate recommendations based on validation results."""
        
        recommendations = []
        
        # Overall statistics
        total_claims = len(validation_results)
        validated_claims = len([r for r in validation_results 
                              if r.validation_status == ValidationStatus.VALIDATED])
        
        validation_rate = validated_claims / total_claims if total_claims > 0 else 0
        
        # Generate specific recommendations
        if validation_rate < 0.5:
            recommendations.append("Consider revising response - less than 50% of claims are validated")
        
        if len(critical_issues) > 0:
            recommendations.append(f"Address {len(critical_issues)} critical validation issues")
        
        # Check for common patterns
        conflicting_claims = [r for r in validation_results 
                            if r.validation_status == ValidationStatus.CONFLICTING]
        if conflicting_claims:
            recommendations.append("Acknowledge conflicting information in sources")
        
        unsupported_claims = [r for r in validation_results 
                            if r.validation_status == ValidationStatus.UNSUPPORTED]
        if len(unsupported_claims) > 2:
            recommendations.append("Remove or find sources for unsupported claims")
        
        # Average confidence recommendations
        avg_confidence = sum(r.confidence_score for r in validation_results) / total_claims
        if avg_confidence < 0.6:
            recommendations.append("Consider qualifying statements with uncertainty indicators")
        
        return recommendations
    
    def _create_no_claims_report(self, response_text: str, query: str) -> ValidationReport:
        """Create validation report when no claims are found."""
        
        return ValidationReport(
            overall_status=ValidationStatus.INSUFFICIENT_INFO,
            overall_confidence=0.0,
            total_claims=0,
            validated_claims=0,
            validation_results=[],
            critical_issues=[],
            recommendations=["Response contains no identifiable claims to validate"],
            metadata={
                "query": query,
                "response_length": len(response_text),
                "status": "no_claims_found"
            }
        )
    
    def _create_error_validation_report(self, response_text: str, error: str) -> ValidationReport:
        """Create validation report when validation fails."""
        
        return ValidationReport(
            overall_status=ValidationStatus.INSUFFICIENT_INFO,
            overall_confidence=0.0,
            total_claims=0,
            validated_claims=0,
            validation_results=[],
            critical_issues=[ValidationIssue(
                issue_type="validation_error",
                severity=ValidationSeverity.CRITICAL,
                description=f"Validation failed: {error}",
                claim_text=response_text[:100] + "...",
                suggestion="Retry validation or check system configuration"
            )],
            recommendations=["Fix validation system error before proceeding"],
            metadata={
                "error": error,
                "response_length": len(response_text),
                "status": "validation_error"
            }
        )

    def get_validation_statistics(self) -> Dict[str, Any]:
        """Get statistics about validation performance."""
        # This would track validation statistics over time in a real implementation
        return {
            "message": "Validation statistics tracking not yet implemented",
            "suggestions": [
                "Implement validation history tracking",
                "Add accuracy metrics over time",
                "Track most common validation issues"
            ]
        }
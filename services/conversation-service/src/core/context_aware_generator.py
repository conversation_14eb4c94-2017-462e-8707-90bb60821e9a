"""
Context-Aware Response Generator
Generates personalized responses based on customer context and preferences.
"""

import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import asyncio
from datetime import datetime

from .customer_profile import CustomerProfileManager, CustomerProfile
from .response_generator import ResponseGenerator
from ..models.query_analysis import QueryAnalysis

@dataclass
class ContextualResponse:
    """Response with contextual information."""
    content: str
    tone_used: str
    personalization_applied: List[str]
    customer_context: Dict[str, Any]
    generation_time: float
    confidence_score: float

class ContextAwareResponseGenerator:
    """Generates responses adapted to customer context and preferences."""
    
    def __init__(self, config, customer_profile_manager: CustomerProfileManager):
        self.config = config
        self.profile_manager = customer_profile_manager
        self.base_generator = ResponseGenerator(config)
        self.logger = logging.getLogger(__name__)
        
        # Tone-specific prompts
        self.tone_prompts = {
            "formal": "Respond in a professional, formal tone. Use complete sentences and proper business language.",
            "casual": "Respond in a friendly, conversational tone. Use simple language and be approachable.",
            "technical": "Respond with technical precision. Include relevant technical details and use industry terminology.",
            "empathetic": "Respond with understanding and empathy. Acknowledge any frustration and be supportive.",
            "concise": "Respond concisely and directly. Get straight to the point with minimal explanation."
        }
        
        # Expertise-level adaptations
        self.expertise_adaptations = {
            "beginner": "Explain concepts clearly with basic terminology. Include background information.",
            "intermediate": "Use standard terminology with moderate detail. Assume basic knowledge.",
            "advanced": "Use technical language and focus on implementation details. Skip basic explanations.",
            "expert": "Provide comprehensive technical details. Assume deep domain knowledge."
        }

    async def generate_contextual_response(
        self,
        query: str,
        retrieved_context: str,
        query_analysis: QueryAnalysis,
        customer_id: str,
        conversation_context: Optional[Dict] = None
    ) -> ContextualResponse:
        """Generate a response adapted to customer context."""
        
        start_time = datetime.now()
        personalization_applied = []
        
        try:
            # Get customer context
            customer_context = await self.profile_manager.get_contextual_preferences(
                customer_id, 
                query_analysis.query_type.value if query_analysis else None,
                query_analysis.primary_topic if query_analysis and hasattr(query_analysis, 'primary_topic') else None
            )
            
            # Build personalized prompt
            personalized_prompt = await self._build_personalized_prompt(
                query, retrieved_context, customer_context, query_analysis
            )
            personalization_applied.extend(self._get_applied_personalizations(customer_context))
            
            # Generate response using personalized prompt
            response = await self.base_generator.generate_response(
                query=query,
                context=retrieved_context,
                query_analysis=query_analysis,
                conversation_context=conversation_context,
                custom_prompt=personalized_prompt
            )
            
            # Apply post-generation adaptations
            adapted_response = await self._apply_post_generation_adaptations(
                response, customer_context, query_analysis
            )
            
            # Calculate confidence based on context match
            confidence_score = self._calculate_contextual_confidence(
                customer_context, query_analysis, len(retrieved_context)
            )
            
            generation_time = (datetime.now() - start_time).total_seconds()
            
            return ContextualResponse(
                content=adapted_response,
                tone_used=customer_context.get("preferred_tone", "formal"),
                personalization_applied=personalization_applied,
                customer_context=customer_context,
                generation_time=generation_time,
                confidence_score=confidence_score
            )
            
        except Exception as e:
            self.logger.error(f"Contextual response generation failed: {e}")
            
            # Fallback to base generator
            fallback_response = await self.base_generator.generate_response(
                query, retrieved_context, query_analysis, conversation_context
            )
            
            return ContextualResponse(
                content=fallback_response,
                tone_used="formal",
                personalization_applied=["fallback_used"],
                customer_context={},
                generation_time=(datetime.now() - start_time).total_seconds(),
                confidence_score=0.5
            )

    async def _build_personalized_prompt(
        self,
        query: str,
        context: str,
        customer_context: Dict[str, Any],
        query_analysis: Optional[QueryAnalysis]
    ) -> str:
        """Build a personalized prompt based on customer context."""
        
        prompt_parts = []
        
        # Base instruction
        prompt_parts.append("You are a helpful customer support assistant. Generate a response based on the provided context.")
        
        # Tone adaptation
        tone = customer_context.get("preferred_tone", "formal")
        if tone in self.tone_prompts:
            prompt_parts.append(self.tone_prompts[tone])
        
        # Expertise level adaptation
        expertise = customer_context.get("expertise_level", "intermediate")
        if expertise in self.expertise_adaptations:
            prompt_parts.append(self.expertise_adaptations[expertise])
        
        # Customer tier considerations
        tier = customer_context.get("customer_tier", "basic")
        if tier in ["premium", "enterprise", "vip"]:
            prompt_parts.append("This is a valued customer. Provide exceptional service and detailed assistance.")
        
        # Response length preference
        length_pref = customer_context.get("response_length", "medium")
        length_instructions = {
            "short": "Keep the response concise and to the point.",
            "medium": "Provide a balanced response with adequate detail.",
            "long": "Provide a comprehensive, detailed response with thorough explanations."
        }
        if length_pref in length_instructions:
            prompt_parts.append(length_instructions[length_pref])
        
        # Code examples preference
        if customer_context.get("include_code_examples", True) and query_analysis:
            if any(keyword in query.lower() for keyword in ["code", "example", "implement", "script"]):
                prompt_parts.append("Include relevant code examples when appropriate.")
        
        # Step-by-step preference
        if customer_context.get("include_step_by_step", True):
            if any(keyword in query.lower() for keyword in ["how to", "steps", "process", "guide"]):
                prompt_parts.append("Provide step-by-step instructions when explaining processes.")
        
        # Interaction history context
        interaction_context = customer_context.get("interaction_context", {})
        if interaction_context.get("is_returning_customer", False):
            prompt_parts.append("This is a returning customer. You can reference previous interactions if relevant.")
        
        if interaction_context.get("needs_extra_care", False):
            prompt_parts.append("This customer has had previous escalations. Be extra helpful and thorough.")
        
        # Recent topics context
        recent_topics = customer_context.get("recent_topics", [])
        if recent_topics:
            prompt_parts.append(f"Recent topics of interest: {', '.join(recent_topics[:3])}")
        
        # Familiar topic handling
        if customer_context.get("familiar_topic", False) and customer_context.get("can_skip_basics", False):
            prompt_parts.append("This customer is familiar with this topic. You can skip basic explanations.")
        
        # Combine all parts
        full_prompt = "\n\n".join(prompt_parts)
        
        # Add context and query
        full_prompt += f"\n\nContext:\n{context}\n\nCustomer Query: {query}\n\nResponse:"
        
        return full_prompt

    async def _apply_post_generation_adaptations(
        self,
        response: str,
        customer_context: Dict[str, Any],
        query_analysis: Optional[QueryAnalysis]
    ) -> str:
        """Apply post-generation adaptations to the response."""
        
        adapted_response = response
        
        # Add customer-specific closing based on tier
        tier = customer_context.get("customer_tier", "basic")
        if tier == "vip":
            adapted_response += "\n\nAs a VIP customer, please don't hesitate to reach out if you need any additional assistance."
        elif tier in ["premium", "enterprise"]:
            adapted_response += "\n\nIf you need further assistance, our premium support team is available to help."
        
        # Add follow-up suggestions for returning customers
        interaction_context = customer_context.get("interaction_context", {})
        if interaction_context.get("is_returning_customer", False):
            recent_topics = customer_context.get("recent_topics", [])
            if recent_topics and query_analysis:
                current_topic = getattr(query_analysis, 'primary_topic', None)
                if current_topic and current_topic not in recent_topics[:2]:
                    adapted_response += f"\n\nSince you've been working with {recent_topics[0]}, you might also find our documentation on related topics helpful."
        
        return adapted_response

    def _get_applied_personalizations(self, customer_context: Dict[str, Any]) -> List[str]:
        """Get list of personalizations that were applied."""
        
        personalizations = []
        
        if customer_context.get("preferred_tone") != "formal":
            personalizations.append(f"tone_adapted_{customer_context['preferred_tone']}")
        
        if customer_context.get("expertise_level") != "intermediate":
            personalizations.append(f"expertise_adapted_{customer_context['expertise_level']}")
        
        if customer_context.get("customer_tier") in ["premium", "enterprise", "vip"]:
            personalizations.append(f"tier_recognition_{customer_context['customer_tier']}")
        
        interaction_context = customer_context.get("interaction_context", {})
        if interaction_context.get("is_returning_customer"):
            personalizations.append("returning_customer_context")
        
        if interaction_context.get("needs_extra_care"):
            personalizations.append("extra_care_mode")
        
        if customer_context.get("recent_topics"):
            personalizations.append("recent_topics_context")
        
        if customer_context.get("familiar_topic"):
            personalizations.append("familiar_topic_optimization")
        
        return personalizations

    def _calculate_contextual_confidence(
        self,
        customer_context: Dict[str, Any],
        query_analysis: Optional[QueryAnalysis],
        context_length: int
    ) -> float:
        """Calculate confidence score based on contextual factors."""
        
        base_confidence = 0.7
        
        # Boost confidence for returning customers with good history
        interaction_context = customer_context.get("interaction_context", {})
        success_rate = interaction_context.get("success_rate", 0.0)
        if success_rate > 0.8:
            base_confidence += 0.1
        
        # Boost for familiar topics
        if customer_context.get("familiar_topic", False):
            base_confidence += 0.05
        
        # Boost for premium customers (more context available)
        if customer_context.get("customer_tier") in ["premium", "enterprise", "vip"]:
            base_confidence += 0.05
        
        # Adjust based on context quality
        if context_length > 1000:
            base_confidence += 0.1
        elif context_length < 200:
            base_confidence -= 0.1
        
        # Cap at 1.0
        return min(base_confidence, 1.0)
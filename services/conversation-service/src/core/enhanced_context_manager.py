"""
Enhanced Conversation Context Manager
Advanced context management with conversation state persistence and intelligent pruning
"""

from typing import Dict, List, Any, Optional, Tuple
import logging
import json
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import asyncio
import hashlib

@dataclass
class ConversationTurn:
    """Individual turn in a conversation."""
    
    turn_id: str
    user_message: str
    assistant_response: str
    timestamp: datetime
    context_used: List[str]
    sources_cited: List[str]
    intent: str
    confidence: float
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ConversationState:
    """Complete conversation state with context tracking."""
    
    conversation_id: str
    user_id: str
    turns: List[ConversationTurn]
    active_topics: List[str]
    context_summary: str
    last_updated: datetime
    total_turns: int
    conversation_type: str = "general"
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    
    def get_recent_turns(self, count: int = 5) -> List[ConversationTurn]:
        """Get most recent conversation turns."""
        return self.turns[-count:] if self.turns else []
    
    def get_context_window(self, max_tokens: int = 2000) -> str:
        """Get conversation context within token limit."""
        context_parts = []
        current_tokens = 0
        
        for turn in reversed(self.turns):
            turn_text = f"User: {turn.user_message}\nAssistant: {turn.assistant_response}\n"
            turn_tokens = len(turn_text.split()) * 1.3  # Rough token estimation
            
            if current_tokens + turn_tokens > max_tokens:
                break
                
            context_parts.insert(0, turn_text)
            current_tokens += turn_tokens
        
        return "\n".join(context_parts)

class ContextRelevanceStrategy(Enum):
    """Strategies for determining context relevance."""
    
    RECENCY_BASED = "recency_based"
    TOPIC_SIMILARITY = "topic_similarity"
    INTENT_CONTINUITY = "intent_continuity"
    HYBRID_SCORING = "hybrid_scoring"

class EnhancedContextManager:
    """Advanced conversation context management with intelligent pruning."""
    
    def __init__(self, max_context_length: int = 4000):
        self.logger = logging.getLogger(__name__)
        self.max_context_length = max_context_length
        
        # Conversation storage (in-memory for now, can be extended to persistent storage)
        self.conversations: Dict[str, ConversationState] = {}
        
        # Context management settings
        self.context_settings = {
            "max_turns_to_keep": 20,
            "context_compression_threshold": 0.7,
            "relevance_decay_factor": 0.9,
            "topic_similarity_threshold": 0.6,
            "intent_continuity_weight": 0.3,
            "recency_weight": 0.4,
            "topic_weight": 0.3
        }
        
        # Topic tracking
        self.topic_extractor = TopicExtractor()
        self.intent_tracker = IntentTracker()
        
        # Context compression
        self.context_compressor = ContextCompressor()

    async def get_conversation_context(self,
                                     conversation_id: str,
                                     current_message: str,
                                     user_id: str,
                                     strategy: ContextRelevanceStrategy = ContextRelevanceStrategy.HYBRID_SCORING) -> Dict[str, Any]:
        """Get optimized conversation context for current message."""
        
        try:
            # Get or create conversation state
            conversation = await self._get_or_create_conversation(
                conversation_id, user_id
            )
            
            # Analyze current message
            current_analysis = await self._analyze_current_message(
                current_message, conversation
            )
            
            # Select relevant context based on strategy
            relevant_context = await self._select_relevant_context(
                conversation, current_analysis, strategy
            )
            
            # Compress context if needed
            if len(relevant_context["context_text"]) > self.max_context_length:
                relevant_context = await self._compress_context(
                    relevant_context, current_analysis
                )
            
            # Update conversation state
            await self._update_conversation_tracking(
                conversation, current_message, current_analysis
            )
            
            return {
                "conversation_id": conversation_id,
                "context_text": relevant_context["context_text"],
                "relevant_turns": relevant_context["relevant_turns"],
                "active_topics": conversation.active_topics,
                "conversation_summary": conversation.context_summary,
                "context_metadata": {
                    "total_turns": conversation.total_turns,
                    "context_strategy": strategy.value,
                    "compression_applied": relevant_context.get("compressed", False),
                    "relevance_scores": relevant_context.get("relevance_scores", {}),
                    "current_intent": current_analysis["intent"],
                    "topic_continuity": current_analysis["topic_continuity"]
                }
            }
            
        except Exception as e:
            self.logger.error(f"Context retrieval failed: {e}")
            return self._get_fallback_context(conversation_id, current_message)

    async def _get_or_create_conversation(self,
                                        conversation_id: str,
                                        user_id: str) -> ConversationState:
        """Get existing conversation or create new one."""
        
        if conversation_id not in self.conversations:
            self.conversations[conversation_id] = ConversationState(
                conversation_id=conversation_id,
                user_id=user_id,
                turns=[],
                active_topics=[],
                context_summary="",
                last_updated=datetime.now(),
                total_turns=0
            )
        
        return self.conversations[conversation_id]

    async def _analyze_current_message(self,
                                     message: str,
                                     conversation: ConversationState) -> Dict[str, Any]:
        """Analyze current message for context selection."""
        
        # Extract topics from current message
        current_topics = await self.topic_extractor.extract_topics(message)
        
        # Determine intent
        current_intent = await self.intent_tracker.classify_intent(
            message, conversation.get_recent_turns(3)
        )
        
        # Calculate topic continuity with previous turns
        topic_continuity = await self._calculate_topic_continuity(
            current_topics, conversation.active_topics
        )
        
        # Analyze message complexity
        message_complexity = self._analyze_message_complexity(message)
        
        return {
            "topics": current_topics,
            "intent": current_intent,
            "topic_continuity": topic_continuity,
            "complexity": message_complexity,
            "requires_context": self._requires_conversation_context(message, current_intent)
        }

    async def _select_relevant_context(self,
                                     conversation: ConversationState,
                                     current_analysis: Dict[str, Any],
                                     strategy: ContextRelevanceStrategy) -> Dict[str, Any]:
        """Select most relevant context based on strategy."""
        
        if not conversation.turns:
            return {
                "context_text": "",
                "relevant_turns": [],
                "relevance_scores": {}
            }
        
        if strategy == ContextRelevanceStrategy.RECENCY_BASED:
            return await self._select_by_recency(conversation, current_analysis)
        
        elif strategy == ContextRelevanceStrategy.TOPIC_SIMILARITY:
            return await self._select_by_topic_similarity(conversation, current_analysis)
        
        elif strategy == ContextRelevanceStrategy.INTENT_CONTINUITY:
            return await self._select_by_intent_continuity(conversation, current_analysis)
        
        else:  # HYBRID_SCORING
            return await self._select_by_hybrid_scoring(conversation, current_analysis)

    async def _select_by_hybrid_scoring(self,
                                      conversation: ConversationState,
                                      current_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Select context using hybrid relevance scoring."""
        
        scored_turns = []
        current_time = datetime.now()
        
        for i, turn in enumerate(conversation.turns):
            # Calculate individual scores
            recency_score = self._calculate_recency_score(turn.timestamp, current_time)
            topic_score = await self._calculate_topic_similarity_score(
                current_analysis["topics"], turn.metadata.get("topics", [])
            )
            intent_score = self._calculate_intent_continuity_score(
                current_analysis["intent"], turn.intent
            )
            
            # Weighted hybrid score
            hybrid_score = (
                self.context_settings["recency_weight"] * recency_score +
                self.context_settings["topic_weight"] * topic_score +
                self.context_settings["intent_continuity_weight"] * intent_score
            )
            
            scored_turns.append({
                "turn": turn,
                "score": hybrid_score,
                "recency_score": recency_score,
                "topic_score": topic_score,
                "intent_score": intent_score
            })
        
        # Sort by score and select top turns
        scored_turns.sort(key=lambda x: x["score"], reverse=True)
        
        # Select turns within context limit
        selected_turns = []
        context_text_parts = []
        current_length = 0
        
        for scored_turn in scored_turns:
            turn = scored_turn["turn"]
            turn_text = f"User: {turn.user_message}\nAssistant: {turn.assistant_response}\n"
            
            if current_length + len(turn_text) <= self.max_context_length:
                selected_turns.append(turn)
                context_text_parts.append(turn_text)
                current_length += len(turn_text)
            else:
                break
        
        # Sort selected turns chronologically
        selected_turns.sort(key=lambda x: x.timestamp)
        context_text_parts = [
            f"User: {turn.user_message}\nAssistant: {turn.assistant_response}\n"
            for turn in selected_turns
        ]
        
        return {
            "context_text": "\n".join(context_text_parts),
            "relevant_turns": selected_turns,
            "relevance_scores": {turn.turn_id: score["score"] for turn, score in 
                               zip(selected_turns, scored_turns[:len(selected_turns)])}
        }

    async def _select_by_recency(self,
                               conversation: ConversationState,
                               current_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Select most recent turns within context limit."""
        
        recent_turns = conversation.get_recent_turns(
            self.context_settings["max_turns_to_keep"]
        )
        
        context_parts = []
        current_length = 0
        selected_turns = []
        
        for turn in reversed(recent_turns):
            turn_text = f"User: {turn.user_message}\nAssistant: {turn.assistant_response}\n"
            
            if current_length + len(turn_text) <= self.max_context_length:
                context_parts.insert(0, turn_text)
                selected_turns.insert(0, turn)
                current_length += len(turn_text)
            else:
                break
        
        return {
            "context_text": "\n".join(context_parts),
            "relevant_turns": selected_turns,
            "relevance_scores": {}
        }

    async def _select_by_topic_similarity(self,
                                        conversation: ConversationState,
                                        current_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Select turns based on topic similarity."""
        
        current_topics = current_analysis["topics"]
        topic_scored_turns = []
        
        for turn in conversation.turns:
            turn_topics = turn.metadata.get("topics", [])
            similarity_score = await self._calculate_topic_similarity_score(
                current_topics, turn_topics
            )
            
            if similarity_score >= self.context_settings["topic_similarity_threshold"]:
                topic_scored_turns.append({
                    "turn": turn,
                    "similarity": similarity_score
                })
        
        # Sort by similarity and select within context limit
        topic_scored_turns.sort(key=lambda x: x["similarity"], reverse=True)
        
        selected_turns = []
        context_parts = []
        current_length = 0
        
        for scored_turn in topic_scored_turns:
            turn = scored_turn["turn"]
            turn_text = f"User: {turn.user_message}\nAssistant: {turn.assistant_response}\n"
            
            if current_length + len(turn_text) <= self.max_context_length:
                selected_turns.append(turn)
                context_parts.append(turn_text)
                current_length += len(turn_text)
            else:
                break
        
        # Sort chronologically
        selected_turns.sort(key=lambda x: x.timestamp)
        context_parts = [
            f"User: {turn.user_message}\nAssistant: {turn.assistant_response}\n"
            for turn in selected_turns
        ]
        
        return {
            "context_text": "\n".join(context_parts),
            "relevant_turns": selected_turns,
            "relevance_scores": {turn.turn_id: scored["similarity"] for turn, scored in 
                               zip(selected_turns, topic_scored_turns[:len(selected_turns)])}
        }

    async def _compress_context(self,
                              context_data: Dict[str, Any],
                              current_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Compress context while preserving important information."""
        
        compressed_context = await self.context_compressor.compress_conversation_context(
            context_data["context_text"],
            current_analysis["topics"],
            target_length=self.max_context_length
        )
        
        return {
            **context_data,
            "context_text": compressed_context,
            "compressed": True
        }

    def _calculate_recency_score(self, timestamp: datetime, current_time: datetime) -> float:
        """Calculate recency score with exponential decay."""
        
        time_diff = (current_time - timestamp).total_seconds()
        hours_diff = time_diff / 3600
        
        # Exponential decay: score decreases by decay_factor each hour
        decay_factor = self.context_settings["relevance_decay_factor"]
        return decay_factor ** hours_diff

    async def _calculate_topic_similarity_score(self,
                                              topics1: List[str],
                                              topics2: List[str]) -> float:
        """Calculate topic similarity between two topic lists."""
        
        if not topics1 or not topics2:
            return 0.0
        
        # Simple Jaccard similarity
        set1 = set(topics1)
        set2 = set(topics2)
        
        intersection = set1.intersection(set2)
        union = set1.union(set2)
        
        return len(intersection) / len(union) if union else 0.0

    def _calculate_intent_continuity_score(self, intent1: str, intent2: str) -> float:
        """Calculate intent continuity score."""
        
        if intent1 == intent2:
            return 1.0
        
        # Define intent similarity groups
        intent_groups = {
            "information_seeking": ["factual", "explanation", "definition"],
            "problem_solving": ["troubleshooting", "how_to", "procedural"],
            "comparison": ["comparison", "evaluation", "recommendation"],
            "creative": ["brainstorming", "creative", "ideation"]
        }
        
        # Check if intents are in same group
        for group in intent_groups.values():
            if intent1 in group and intent2 in group:
                return 0.7
        
        return 0.0

    async def _calculate_topic_continuity(self,
                                        current_topics: List[str],
                                        active_topics: List[str]) -> float:
        """Calculate topic continuity with conversation."""
        
        if not current_topics or not active_topics:
            return 0.0
        
        return await self._calculate_topic_similarity_score(current_topics, active_topics)

    def _analyze_message_complexity(self, message: str) -> Dict[str, Any]:
        """Analyze message complexity for context requirements."""
        
        word_count = len(message.split())
        question_count = message.count('?')
        has_technical_terms = any(term in message.lower() for term in 
                                ['api', 'database', 'configuration', 'integration'])
        
        complexity_score = 0
        if word_count > 20:
            complexity_score += 1
        if question_count > 1:
            complexity_score += 1
        if has_technical_terms:
            complexity_score += 1
        
        return {
            "word_count": word_count,
            "question_count": question_count,
            "has_technical_terms": has_technical_terms,
            "complexity_level": "high" if complexity_score >= 2 else "medium" if complexity_score == 1 else "low"
        }

    def _requires_conversation_context(self, message: str, intent: str) -> bool:
        """Determine if message requires conversation context."""
        
        context_indicators = [
            "that", "this", "it", "they", "them", "previous", "earlier",
            "continue", "also", "additionally", "furthermore", "moreover"
        ]
        
        # Check for context-requiring intents
        context_requiring_intents = [
            "follow_up", "clarification", "continuation", "reference"
        ]
        
        return (
            any(indicator in message.lower() for indicator in context_indicators) or
            intent in context_requiring_intents or
            len(message.split()) < 5  # Very short messages often need context
        )

    async def _update_conversation_tracking(self,
                                          conversation: ConversationState,
                                          current_message: str,
                                          current_analysis: Dict[str, Any]):
        """Update conversation state tracking."""
        
        # Update active topics
        new_topics = current_analysis["topics"]
        
        # Merge with existing topics, keeping most recent
        all_topics = conversation.active_topics + new_topics
        unique_topics = list(dict.fromkeys(all_topics))  # Preserve order, remove duplicates
        conversation.active_topics = unique_topics[-10:]  # Keep last 10 topics
        
        # Update last activity
        conversation.last_updated = datetime.now()

    async def add_conversation_turn(self,
                                  conversation_id: str,
                                  user_message: str,
                                  assistant_response: str,
                                  context_used: List[str],
                                  sources_cited: List[str],
                                  intent: str,
                                  confidence: float,
                                  metadata: Optional[Dict[str, Any]] = None):
        """Add a new turn to conversation history."""
        
        conversation = self.conversations.get(conversation_id)
        if not conversation:
            self.logger.warning(f"Conversation {conversation_id} not found for turn addition")
            return
        
        # Extract topics from user message
        topics = await self.topic_extractor.extract_topics(user_message)
        
        # Create turn
        turn = ConversationTurn(
            turn_id=f"{conversation_id}_{len(conversation.turns)}",
            user_message=user_message,
            assistant_response=assistant_response,
            timestamp=datetime.now(),
            context_used=context_used,
            sources_cited=sources_cited,
            intent=intent,
            confidence=confidence,
            metadata={
                **(metadata or {}),
                "topics": topics
            }
        )
        
        # Add to conversation
        conversation.turns.append(turn)
        conversation.total_turns += 1
        
        # Update conversation summary if needed
        if len(conversation.turns) % 5 == 0:  # Update summary every 5 turns
            conversation.context_summary = await self._generate_conversation_summary(conversation)
        
        # Prune old turns if needed
        await self._prune_conversation_history(conversation)

    async def _generate_conversation_summary(self, conversation: ConversationState) -> str:
        """Generate a summary of the conversation."""
        
        if not conversation.turns:
            return ""
        
        # Simple summary generation (can be enhanced with LLM)
        recent_topics = conversation.active_topics[-5:]
        turn_count = len(conversation.turns)
        
        summary_parts = [
            f"Conversation with {turn_count} turns",
            f"Recent topics: {', '.join(recent_topics)}" if recent_topics else "No specific topics identified"
        ]
        
        return ". ".join(summary_parts)

    async def _prune_conversation_history(self, conversation: ConversationState):
        """Prune old conversation turns to manage memory."""
        
        max_turns = self.context_settings["max_turns_to_keep"]
        
        if len(conversation.turns) > max_turns:
            # Keep most recent turns and high-relevance older turns
            recent_turns = conversation.turns[-max_turns//2:]
            older_turns = conversation.turns[:-max_turns//2]
            
            # Score older turns for importance
            important_older_turns = []
            for turn in older_turns:
                importance_score = self._calculate_turn_importance(turn, conversation)
                if importance_score > 0.7:  # Keep important turns
                    important_older_turns.append(turn)
            
            # Combine important older turns with recent turns
            conversation.turns = important_older_turns + recent_turns
            
            # Limit total turns
            if len(conversation.turns) > max_turns:
                conversation.turns = conversation.turns[-max_turns:]

    def _calculate_turn_importance(self, turn: ConversationTurn, conversation: ConversationState) -> float:
        """Calculate importance score for a conversation turn."""
        
        importance_score = 0.0
        
        # High confidence responses are more important
        importance_score += turn.confidence * 0.3
        
        # Turns with sources are more important
        if turn.sources_cited:
            importance_score += 0.3
        
        # Turns with active topics are more important
        turn_topics = turn.metadata.get("topics", [])
        topic_overlap = len(set(turn_topics).intersection(set(conversation.active_topics)))
        importance_score += (topic_overlap / max(len(conversation.active_topics), 1)) * 0.4
        
        return min(importance_score, 1.0)

    def _get_fallback_context(self, conversation_id: str, current_message: str) -> Dict[str, Any]:
        """Get fallback context when main context retrieval fails."""
        
        return {
            "conversation_id": conversation_id,
            "context_text": "",
            "relevant_turns": [],
            "active_topics": [],
            "conversation_summary": "",
            "context_metadata": {
                "total_turns": 0,
                "context_strategy": "fallback",
                "compression_applied": False,
                "relevance_scores": {},
                "current_intent": "unknown",
                "topic_continuity": 0.0
            }
        }

    async def get_conversation_analytics(self, conversation_id: str) -> Dict[str, Any]:
        """Get analytics for a conversation."""
        
        conversation = self.conversations.get(conversation_id)
        if not conversation:
            return {"error": "Conversation not found"}
        
        # Calculate analytics
        total_turns = len(conversation.turns)
        avg_confidence = sum(turn.confidence for turn in conversation.turns) / total_turns if total_turns > 0 else 0
        
        intent_distribution = {}
        for turn in conversation.turns:
            intent = turn.intent
            intent_distribution[intent] = intent_distribution.get(intent, 0) + 1
        
        topic_frequency = {}
        for turn in conversation.turns:
            topics = turn.metadata.get("topics", [])
            for topic in topics:
                topic_frequency[topic] = topic_frequency.get(topic, 0) + 1
        
        return {
            "conversation_id": conversation_id,
            "total_turns": total_turns,
            "active_topics": conversation.active_topics,
            "avg_confidence": avg_confidence,
            "intent_distribution": intent_distribution,
            "top_topics": sorted(topic_frequency.items(), key=lambda x: x[1], reverse=True)[:10],
            "conversation_duration": (conversation.last_updated - conversation.turns[0].timestamp).total_seconds() / 3600 if conversation.turns else 0,
            "last_activity": conversation.last_updated.isoformat()
        }

    async def clear_conversation(self, conversation_id: str):
        """Clear conversation history."""
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]

    async def export_conversation(self, conversation_id: str) -> Dict[str, Any]:
        """Export conversation data."""
        
        conversation = self.conversations.get(conversation_id)
        if not conversation:
            return {"error": "Conversation not found"}
        
        return {
            "conversation_id": conversation.conversation_id,
            "user_id": conversation.user_id,
            "total_turns": conversation.total_turns,
            "active_topics": conversation.active_topics,
            "context_summary": conversation.context_summary,
            "turns": [
                {
                    "turn_id": turn.turn_id,
                    "user_message": turn.user_message,
                    "assistant_response": turn.assistant_response,
                    "timestamp": turn.timestamp.isoformat(),
                    "intent": turn.intent,
                    "confidence": turn.confidence,
                    "topics": turn.metadata.get("topics", [])
                }
                for turn in conversation.turns
            ]
        }


class TopicExtractor:
    """Extract topics from text messages."""
    
    async def extract_topics(self, text: str) -> List[str]:
        """Extract topics from text."""
        
        # Simple keyword-based topic extraction
        # In production, this could use NLP models
        
        topic_keywords = {
            "authentication": ["login", "password", "auth", "token", "credentials"],
            "database": ["database", "sql", "query", "table", "data"],
            "api": ["api", "endpoint", "request", "response", "integration"],
            "configuration": ["config", "setup", "settings", "environment"],
            "troubleshooting": ["error", "issue", "problem", "bug", "fix"],
            "deployment": ["deploy", "production", "server", "hosting"],
            "security": ["security", "encryption", "ssl", "certificate"],
            "performance": ["performance", "speed", "optimization", "cache"]
        }
        
        text_lower = text.lower()
        detected_topics = []
        
        for topic, keywords in topic_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                detected_topics.append(topic)
        
        return detected_topics


class IntentTracker:
    """Track conversation intent."""
    
    async def classify_intent(self, message: str, recent_turns: List[ConversationTurn]) -> str:
        """Classify message intent."""
        
        message_lower = message.lower()
        
        # Intent patterns
        if any(word in message_lower for word in ["how", "what", "why", "when", "where"]):
            return "information_seeking"
        elif any(word in message_lower for word in ["error", "problem", "issue", "fix", "troubleshoot"]):
            return "troubleshooting"
        elif any(word in message_lower for word in ["compare", "difference", "better", "vs", "versus"]):
            return "comparison"
        elif any(word in message_lower for word in ["help", "assist", "support"]):
            return "help_request"
        elif any(word in message_lower for word in ["thank", "thanks", "appreciate"]):
            return "acknowledgment"
        else:
            return "general"


class ContextCompressor:
    """Compress conversation context while preserving key information."""
    
    async def compress_conversation_context(self,
                                          context_text: str,
                                          current_topics: List[str],
                                          target_length: int) -> str:
        """Compress context to target length while preserving relevance."""
        
        if len(context_text) <= target_length:
            return context_text
        
        # Split into turns
        turns = context_text.split("\n\n")
        
        # Score turns by relevance to current topics
        scored_turns = []
        for turn in turns:
            if turn.strip():
                relevance_score = self._calculate_turn_relevance(turn, current_topics)
                scored_turns.append({
                    "text": turn,
                    "score": relevance_score,
                    "length": len(turn)
                })
        
        # Select turns within target length, prioritizing relevance
        scored_turns.sort(key=lambda x: x["score"], reverse=True)
        
        selected_turns = []
        current_length = 0
        
        for turn_data in scored_turns:
            if current_length + turn_data["length"] <= target_length:
                selected_turns.append(turn_data["text"])
                current_length += turn_data["length"]
        
        return "\n\n".join(selected_turns)
    
    def _calculate_turn_relevance(self, turn_text: str, current_topics: List[str]) -> float:
        """Calculate relevance of a turn to current topics."""
        
        if not current_topics:
            return 0.5  # Neutral relevance
        
        turn_lower = turn_text.lower()
        topic_matches = sum(1 for topic in current_topics if topic.lower() in turn_lower)
        
        return min(topic_matches / len(current_topics), 1.0)
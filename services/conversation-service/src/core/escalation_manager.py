"""
Escalation Manager
Handle escalation to human agents when the system cannot provide adequate responses
"""

from typing import Dict, List, Any, Optional, Tuple, Set
import logging
import asyncio
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import json
import uuid
from collections import defaultdict

class EscalationReason(Enum):
    """Reasons for escalating to human agents."""
    HIGH_UNCERTAINTY = "high_uncertainty"
    COMPLEX_QUERY = "complex_query"
    CUSTOMER_REQUEST = "customer_request"
    SYSTEM_ERROR = "system_error"
    POLICY_VIOLATION = "policy_violation"
    SENSITIVE_TOPIC = "sensitive_topic"
    UNSUPPORTED_LANGUAGE = "unsupported_language"
    TECHNICAL_LIMITATION = "technical_limitation"
    QUALITY_THRESHOLD = "quality_threshold"

class EscalationPriority(Enum):
    """Priority levels for escalations."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"
    CRITICAL = "critical"

class EscalationStatus(Enum):
    """Status of escalation tickets."""
    PENDING = "pending"
    ASSIGNED = "assigned"
    IN_PROGRESS = "in_progress"
    RESOLVED = "resolved"
    CLOSED = "closed"
    CANCELLED = "cancelled"

@dataclass
class CustomerContext:
    """Customer context for escalation."""
    
    customer_id: str = ""
    customer_tier: str = "standard"  # free, standard, premium, enterprise
    interaction_history: List[Dict[str, Any]] = field(default_factory=list)
    preferences: Dict[str, Any] = field(default_factory=dict)
    
    # Current session info
    session_id: str = ""
    conversation_length: int = 0
    previous_escalations: int = 0
    
    # Contact information
    contact_method: str = "chat"  # chat, email, phone
    timezone: str = "UTC"
    language: str = "en"

@dataclass
class EscalationTicket:
    """Escalation ticket for human agent handling."""
    
    ticket_id: str
    reason: EscalationReason
    priority: EscalationPriority
    status: EscalationStatus
    
    # Query and context
    original_query: str
    conversation_context: List[Dict[str, Any]] = field(default_factory=list)
    customer_context: CustomerContext = field(default_factory=CustomerContext)
    
    # System analysis
    uncertainty_assessment: Dict[str, Any] = field(default_factory=dict)
    attempted_responses: List[str] = field(default_factory=list)
    system_limitations: List[str] = field(default_factory=list)
    
    # Escalation metadata
    created_at: datetime = field(default_factory=datetime.now)
    assigned_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    assigned_agent: str = ""
    
    # Resolution tracking
    resolution_summary: str = ""
    customer_satisfaction: Optional[int] = None  # 1-5 scale
    resolution_time: Optional[timedelta] = None
    
    # Internal notes
    agent_notes: List[str] = field(default_factory=list)
    system_notes: List[str] = field(default_factory=list)

class EscalationManager:
    """Manage escalations to human agents with intelligent routing and tracking."""
    
    def __init__(self, config=None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Escalation settings
        self.escalation_settings = {
            "auto_escalate_uncertainty_threshold": 0.8,
            "max_conversation_length": 10,
            "max_retry_attempts": 3,
            "priority_response_times": {
                "critical": 300,    # 5 minutes
                "urgent": 900,      # 15 minutes
                "high": 3600,       # 1 hour
                "medium": 14400,    # 4 hours
                "low": 86400        # 24 hours
            }
        }
        
        # Active tickets
        self.active_tickets: Dict[str, EscalationTicket] = {}
        
        # Escalation triggers
        self.escalation_triggers = self._initialize_escalation_triggers()
        
        # Agent availability (mock - would integrate with real system)
        self.agent_availability = {
            "general": 5,
            "technical": 3,
            "billing": 2,
            "enterprise": 1
        }
    
    def _initialize_escalation_triggers(self) -> Dict[str, Dict[str, Any]]:
        """Initialize triggers that cause automatic escalation."""
        
        return {
            "uncertainty_threshold": {
                "threshold": 0.8,
                "reason": EscalationReason.HIGH_UNCERTAINTY,
                "priority": EscalationPriority.MEDIUM
            },
            "conversation_length": {
                "threshold": 10,
                "reason": EscalationReason.COMPLEX_QUERY,
                "priority": EscalationPriority.HIGH
            },
            "system_errors": {
                "threshold": 3,
                "reason": EscalationReason.SYSTEM_ERROR,
                "priority": EscalationPriority.HIGH
            },
            "customer_tier_enterprise": {
                "immediate": True,
                "reason": EscalationReason.CUSTOMER_REQUEST,
                "priority": EscalationPriority.HIGH
            },
            "sensitive_keywords": {
                "keywords": ["legal", "lawsuit", "discrimination", "harassment", "privacy breach"],
                "reason": EscalationReason.SENSITIVE_TOPIC,
                "priority": EscalationPriority.URGENT
            }
        }
    
    async def evaluate_escalation_need(self, 
                                     query: str, 
                                     conversation_context: List[Dict[str, Any]], 
                                     customer_context: CustomerContext,
                                     uncertainty_assessment: Optional[Dict[str, Any]] = None,
                                     system_performance: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Evaluate whether escalation is needed and determine priority."""
        
        escalation_analysis = {
            "should_escalate": False,
            "reason": None,
            "priority": EscalationPriority.LOW,
            "confidence": 0.0,
            "triggers_activated": [],
            "estimated_wait_time": None
        }
        
        try:
            # Check uncertainty threshold
            if uncertainty_assessment:
                uncertainty_score = uncertainty_assessment.get("overall_uncertainty", 0.0)
                if uncertainty_score >= self.escalation_settings["auto_escalate_uncertainty_threshold"]:
                    escalation_analysis["should_escalate"] = True
                    escalation_analysis["reason"] = EscalationReason.HIGH_UNCERTAINTY
                    escalation_analysis["priority"] = EscalationPriority.MEDIUM
                    escalation_analysis["triggers_activated"].append("uncertainty_threshold")
            
            # Check conversation length
            if len(conversation_context) >= self.escalation_settings["max_conversation_length"]:
                escalation_analysis["should_escalate"] = True
                escalation_analysis["reason"] = EscalationReason.COMPLEX_QUERY
                escalation_analysis["priority"] = EscalationPriority.HIGH
                escalation_analysis["triggers_activated"].append("conversation_length")
            
            # Check customer tier
            if customer_context.customer_tier == "enterprise":
                escalation_analysis["should_escalate"] = True
                escalation_analysis["reason"] = EscalationReason.CUSTOMER_REQUEST
                escalation_analysis["priority"] = EscalationPriority.HIGH
                escalation_analysis["triggers_activated"].append("enterprise_tier")
            
            # Check for sensitive keywords
            sensitive_keywords = self.escalation_triggers["sensitive_keywords"]["keywords"]
            query_lower = query.lower()
            for keyword in sensitive_keywords:
                if keyword in query_lower:
                    escalation_analysis["should_escalate"] = True
                    escalation_analysis["reason"] = EscalationReason.SENSITIVE_TOPIC
                    escalation_analysis["priority"] = EscalationPriority.URGENT
                    escalation_analysis["triggers_activated"].append(f"sensitive_keyword_{keyword}")
                    break
            
            # Check system performance issues
            if system_performance:
                error_count = system_performance.get("error_count", 0)
                if error_count >= 3:
                    escalation_analysis["should_escalate"] = True
                    escalation_analysis["reason"] = EscalationReason.SYSTEM_ERROR
                    escalation_analysis["priority"] = EscalationPriority.HIGH
                    escalation_analysis["triggers_activated"].append("system_errors")
            
            # Check for explicit escalation requests
            escalation_phrases = [
                "speak to human", "talk to agent", "escalate", "human support",
                "customer service", "representative", "supervisor"
            ]
            for phrase in escalation_phrases:
                if phrase in query_lower:
                    escalation_analysis["should_escalate"] = True
                    escalation_analysis["reason"] = EscalationReason.CUSTOMER_REQUEST
                    escalation_analysis["priority"] = EscalationPriority.MEDIUM
                    escalation_analysis["triggers_activated"].append("explicit_request")
                    break
            
            # Calculate confidence in escalation decision
            if escalation_analysis["should_escalate"]:
                escalation_analysis["confidence"] = min(1.0, len(escalation_analysis["triggers_activated"]) * 0.3)
                
                # Estimate wait time
                escalation_analysis["estimated_wait_time"] = await self._estimate_wait_time(
                    escalation_analysis["priority"]
                )
            
            return escalation_analysis
            
        except Exception as e:
            self.logger.error(f"Escalation evaluation failed: {e}")
            return escalation_analysis
    
    async def create_escalation_ticket(self, 
                                     query: str, 
                                     reason: EscalationReason, 
                                     priority: EscalationPriority,
                                     conversation_context: List[Dict[str, Any]], 
                                     customer_context: CustomerContext,
                                     uncertainty_assessment: Optional[Dict[str, Any]] = None,
                                     attempted_responses: List[str] = None) -> EscalationTicket:
        """Create a new escalation ticket."""
        
        try:
            ticket_id = str(uuid.uuid4())
            
            ticket = EscalationTicket(
                ticket_id=ticket_id,
                reason=reason,
                priority=priority,
                status=EscalationStatus.PENDING,
                original_query=query,
                conversation_context=conversation_context,
                customer_context=customer_context,
                uncertainty_assessment=uncertainty_assessment or {},
                attempted_responses=attempted_responses or []
            )
            
            # Add system analysis
            await self._analyze_escalation_context(ticket)
            
            # Store ticket
            self.active_tickets[ticket_id] = ticket
            
            # Log escalation
            self.logger.info(f"Escalation ticket created: {ticket_id} - {reason.value} - {priority.value}")
            
            return ticket
            
        except Exception as e:
            self.logger.error(f"Failed to create escalation ticket: {e}")
            raise
    
    async def _analyze_escalation_context(self, ticket: EscalationTicket):
        """Analyze the context of an escalation to provide insights for agents."""
        
        # Analyze query complexity
        query_analysis = await self._analyze_query_complexity(ticket.original_query)
        ticket.system_notes.append(f"Query complexity: {query_analysis}")
        
        # Analyze conversation patterns
        if ticket.conversation_context:
            conversation_analysis = await self._analyze_conversation_patterns(
                ticket.conversation_context
            )
            ticket.system_notes.append(f"Conversation pattern: {conversation_analysis}")
        
        # Identify system limitations
        limitations = await self._identify_system_limitations(ticket)
        ticket.system_limitations.extend(limitations)
        
        # Add customer insights
        customer_insights = await self._analyze_customer_context(ticket.customer_context)
        ticket.system_notes.append(f"Customer insights: {customer_insights}")
    
    async def _analyze_query_complexity(self, query: str) -> str:
        """Analyze the complexity of the query."""
        
        word_count = len(query.split())
        question_marks = query.count('?')
        
        if word_count > 50 or question_marks > 2:
            return "High complexity - multi-part query"
        elif word_count > 20 or question_marks > 1:
            return "Medium complexity - detailed question"
        else:
            return "Low complexity - simple question"
    
    async def _analyze_conversation_patterns(self, context: List[Dict[str, Any]]) -> str:
        """Analyze patterns in the conversation."""
        
        if len(context) > 8:
            return "Extended conversation - customer may be frustrated"
        elif len(context) > 5:
            return "Lengthy conversation - complex issue"
        else:
            return "Standard conversation length"
    
    async def _identify_system_limitations(self, ticket: EscalationTicket) -> List[str]:
        """Identify specific system limitations that led to escalation."""
        
        limitations = []
        
        if ticket.reason == EscalationReason.HIGH_UNCERTAINTY:
            limitations.append("Insufficient knowledge base coverage")
        elif ticket.reason == EscalationReason.COMPLEX_QUERY:
            limitations.append("Multi-step reasoning required")
        elif ticket.reason == EscalationReason.SENSITIVE_TOPIC:
            limitations.append("Requires human judgment for sensitive matters")
        elif ticket.reason == EscalationReason.TECHNICAL_LIMITATION:
            limitations.append("System cannot process this request type")
        
        return limitations
    
    async def _analyze_customer_context(self, customer_context: CustomerContext) -> str:
        """Analyze customer context for agent insights."""
        
        insights = []
        
        if customer_context.customer_tier == "enterprise":
            insights.append("Enterprise customer - high priority")
        elif customer_context.customer_tier == "premium":
            insights.append("Premium customer - expedited service")
        
        if customer_context.previous_escalations > 2:
            insights.append("Frequent escalations - may need special attention")
        
        if customer_context.conversation_length > 10:
            insights.append("Long conversation - customer may be frustrated")
        
        return "; ".join(insights) if insights else "Standard customer interaction"
    
    async def _estimate_wait_time(self, priority: EscalationPriority) -> int:
        """Estimate wait time based on priority and agent availability."""
        
        base_time = self.escalation_settings["priority_response_times"][priority.value]
        
        # Adjust based on agent availability (simplified)
        available_agents = self.agent_availability.get("general", 1)
        if available_agents == 0:
            return base_time * 3  # No agents available
        elif available_agents < 3:
            return int(base_time * 1.5)  # Limited availability
        else:
            return base_time  # Normal availability

    async def assign_ticket(self, ticket_id: str, agent_id: str) -> bool:
        """Assign a ticket to a human agent."""

        try:
            if ticket_id not in self.active_tickets:
                self.logger.error(f"Ticket {ticket_id} not found")
                return False

            ticket = self.active_tickets[ticket_id]
            ticket.assigned_agent = agent_id
            ticket.assigned_at = datetime.now()
            ticket.status = EscalationStatus.ASSIGNED

            self.logger.info(f"Ticket {ticket_id} assigned to agent {agent_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to assign ticket {ticket_id}: {e}")
            return False

    async def update_ticket_status(self, ticket_id: str, status: EscalationStatus, notes: str = "") -> bool:
        """Update the status of an escalation ticket."""

        try:
            if ticket_id not in self.active_tickets:
                return False

            ticket = self.active_tickets[ticket_id]
            old_status = ticket.status
            ticket.status = status

            if notes:
                ticket.agent_notes.append(f"{datetime.now().isoformat()}: {notes}")

            # Handle status-specific updates
            if status == EscalationStatus.IN_PROGRESS and old_status == EscalationStatus.ASSIGNED:
                ticket.system_notes.append("Agent started working on ticket")
            elif status == EscalationStatus.RESOLVED:
                ticket.resolved_at = datetime.now()
                if ticket.assigned_at:
                    ticket.resolution_time = ticket.resolved_at - ticket.assigned_at

            self.logger.info(f"Ticket {ticket_id} status updated: {old_status.value} -> {status.value}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to update ticket {ticket_id}: {e}")
            return False

    async def resolve_ticket(self,
                           ticket_id: str,
                           resolution_summary: str,
                           customer_satisfaction: Optional[int] = None) -> bool:
        """Mark a ticket as resolved with summary."""

        try:
            if ticket_id not in self.active_tickets:
                return False

            ticket = self.active_tickets[ticket_id]
            ticket.status = EscalationStatus.RESOLVED
            ticket.resolved_at = datetime.now()
            ticket.resolution_summary = resolution_summary

            if customer_satisfaction is not None:
                ticket.customer_satisfaction = customer_satisfaction

            if ticket.assigned_at:
                ticket.resolution_time = ticket.resolved_at - ticket.assigned_at

            self.logger.info(f"Ticket {ticket_id} resolved: {resolution_summary}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to resolve ticket {ticket_id}: {e}")
            return False

    async def get_escalation_message(self, ticket: EscalationTicket) -> str:
        """Generate a message to inform the customer about escalation."""

        priority_messages = {
            EscalationPriority.CRITICAL: "This is being treated as a critical issue and will be addressed immediately.",
            EscalationPriority.URGENT: "This has been marked as urgent and will be prioritized.",
            EscalationPriority.HIGH: "This has been escalated to our support team for immediate attention.",
            EscalationPriority.MEDIUM: "I'm connecting you with a human agent who can better assist you.",
            EscalationPriority.LOW: "Let me transfer you to a human agent for further assistance."
        }

        base_message = priority_messages.get(ticket.priority,
                                           "I'm escalating this to a human agent for better assistance.")

        # Add estimated wait time if available
        wait_time = await self._estimate_wait_time(ticket.priority)
        if wait_time:
            if wait_time < 3600:  # Less than 1 hour
                minutes = wait_time // 60
                base_message += f" Estimated wait time: {minutes} minutes."
            else:  # More than 1 hour
                hours = wait_time // 3600
                base_message += f" Estimated wait time: {hours} hour(s)."

        # Add ticket reference
        base_message += f" Your ticket reference is: {ticket.ticket_id[:8]}"

        return base_message

    async def get_ticket_summary(self, ticket_id: str) -> Optional[Dict[str, Any]]:
        """Get a summary of an escalation ticket."""

        if ticket_id not in self.active_tickets:
            return None

        ticket = self.active_tickets[ticket_id]

        summary = {
            "ticket_id": ticket.ticket_id,
            "status": ticket.status.value,
            "reason": ticket.reason.value,
            "priority": ticket.priority.value,
            "original_query": ticket.original_query,
            "customer_tier": ticket.customer_context.customer_tier,
            "created_at": ticket.created_at.isoformat(),
            "assigned_agent": ticket.assigned_agent,
            "resolution_summary": ticket.resolution_summary,
            "customer_satisfaction": ticket.customer_satisfaction,
            "system_limitations": ticket.system_limitations,
            "conversation_length": len(ticket.conversation_context)
        }

        if ticket.assigned_at:
            summary["assigned_at"] = ticket.assigned_at.isoformat()
        if ticket.resolved_at:
            summary["resolved_at"] = ticket.resolved_at.isoformat()
        if ticket.resolution_time:
            summary["resolution_time_minutes"] = ticket.resolution_time.total_seconds() / 60

        return summary

    async def get_escalation_statistics(self) -> Dict[str, Any]:
        """Get statistics about escalations."""

        if not self.active_tickets:
            return {"message": "No escalation tickets found"}

        tickets = list(self.active_tickets.values())

        stats = {
            "total_tickets": len(tickets),
            "status_distribution": defaultdict(int),
            "reason_distribution": defaultdict(int),
            "priority_distribution": defaultdict(int),
            "average_resolution_time": 0.0,
            "customer_satisfaction_avg": 0.0,
            "escalation_rate_by_tier": defaultdict(int)
        }

        resolution_times = []
        satisfaction_scores = []

        for ticket in tickets:
            stats["status_distribution"][ticket.status.value] += 1
            stats["reason_distribution"][ticket.reason.value] += 1
            stats["priority_distribution"][ticket.priority.value] += 1
            stats["escalation_rate_by_tier"][ticket.customer_context.customer_tier] += 1

            if ticket.resolution_time:
                resolution_times.append(ticket.resolution_time.total_seconds() / 60)

            if ticket.customer_satisfaction:
                satisfaction_scores.append(ticket.customer_satisfaction)

        if resolution_times:
            stats["average_resolution_time"] = sum(resolution_times) / len(resolution_times)

        if satisfaction_scores:
            stats["customer_satisfaction_avg"] = sum(satisfaction_scores) / len(satisfaction_scores)

        return stats

    async def get_pending_tickets(self, priority_filter: Optional[EscalationPriority] = None) -> List[Dict[str, Any]]:
        """Get list of pending escalation tickets."""

        pending_tickets = []

        for ticket in self.active_tickets.values():
            if ticket.status in [EscalationStatus.PENDING, EscalationStatus.ASSIGNED]:
                if priority_filter is None or ticket.priority == priority_filter:
                    summary = await self.get_ticket_summary(ticket.ticket_id)
                    if summary:
                        pending_tickets.append(summary)

        # Sort by priority and creation time
        priority_order = {
            EscalationPriority.CRITICAL: 0,
            EscalationPriority.URGENT: 1,
            EscalationPriority.HIGH: 2,
            EscalationPriority.MEDIUM: 3,
            EscalationPriority.LOW: 4
        }

        pending_tickets.sort(key=lambda x: (
            priority_order.get(EscalationPriority(x["priority"]), 5),
            x["created_at"]
        ))

        return pending_tickets

    async def auto_escalate_if_needed(self,
                                    query: str,
                                    conversation_context: List[Dict[str, Any]],
                                    customer_context: CustomerContext,
                                    uncertainty_assessment: Optional[Dict[str, Any]] = None) -> Optional[EscalationTicket]:
        """Automatically escalate if conditions are met."""

        evaluation = await self.evaluate_escalation_need(
            query, conversation_context, customer_context, uncertainty_assessment
        )

        if evaluation["should_escalate"]:
            ticket = await self.create_escalation_ticket(
                query=query,
                reason=evaluation["reason"],
                priority=evaluation["priority"],
                conversation_context=conversation_context,
                customer_context=customer_context,
                uncertainty_assessment=uncertainty_assessment
            )

            self.logger.info(f"Auto-escalated ticket {ticket.ticket_id} - triggers: {evaluation['triggers_activated']}")
            return ticket

        return None

    async def export_ticket_data(self, ticket_id: str, file_path: str):
        """Export ticket data to JSON file."""

        try:
            if ticket_id not in self.active_tickets:
                raise ValueError(f"Ticket {ticket_id} not found")

            ticket = self.active_tickets[ticket_id]

            # Convert to serializable format
            export_data = {
                "ticket_id": ticket.ticket_id,
                "reason": ticket.reason.value,
                "priority": ticket.priority.value,
                "status": ticket.status.value,
                "original_query": ticket.original_query,
                "conversation_context": ticket.conversation_context,
                "customer_context": {
                    "customer_id": ticket.customer_context.customer_id,
                    "customer_tier": ticket.customer_context.customer_tier,
                    "session_id": ticket.customer_context.session_id,
                    "conversation_length": ticket.customer_context.conversation_length,
                    "previous_escalations": ticket.customer_context.previous_escalations,
                    "contact_method": ticket.customer_context.contact_method,
                    "language": ticket.customer_context.language
                },
                "uncertainty_assessment": ticket.uncertainty_assessment,
                "attempted_responses": ticket.attempted_responses,
                "system_limitations": ticket.system_limitations,
                "created_at": ticket.created_at.isoformat(),
                "assigned_at": ticket.assigned_at.isoformat() if ticket.assigned_at else None,
                "resolved_at": ticket.resolved_at.isoformat() if ticket.resolved_at else None,
                "assigned_agent": ticket.assigned_agent,
                "resolution_summary": ticket.resolution_summary,
                "customer_satisfaction": ticket.customer_satisfaction,
                "agent_notes": ticket.agent_notes,
                "system_notes": ticket.system_notes
            }

            with open(file_path, 'w') as f:
                json.dump(export_data, f, indent=2)

            self.logger.info(f"Ticket {ticket_id} exported to {file_path}")

        except Exception as e:
            self.logger.error(f"Failed to export ticket {ticket_id}: {e}")

    def cleanup_resolved_tickets(self, days_old: int = 30):
        """Clean up resolved tickets older than specified days."""

        cutoff_date = datetime.now() - timedelta(days=days_old)
        tickets_to_remove = []

        for ticket_id, ticket in self.active_tickets.items():
            if (ticket.status in [EscalationStatus.RESOLVED, EscalationStatus.CLOSED] and
                ticket.resolved_at and ticket.resolved_at < cutoff_date):
                tickets_to_remove.append(ticket_id)

        for ticket_id in tickets_to_remove:
            del self.active_tickets[ticket_id]

        self.logger.info(f"Cleaned up {len(tickets_to_remove)} resolved tickets older than {days_old} days")

from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import logging
import json
import re
from datetime import datetime
from langchain_openai import ChatOpenAI
from langchain.schema import BaseMessage, HumanMessage, SystemMessage


class ConfidenceLevel(Enum):
    """Confidence levels for generated content."""
    VERY_HIGH = "very_high"  # 0.9+
    HIGH = "high"           # 0.7-0.9
    MEDIUM = "medium"       # 0.5-0.7
    LOW = "low"            # 0.3-0.5
    VERY_LOW = "very_low"  # <0.3


@dataclass
class SourceCitation:
    """Represents a citation to a source document."""
    source_id: str
    source_title: str
    content_snippet: str
    relevance_score: float
    position_in_source: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class GroundedClaim:
    """Represents a claim with its source grounding."""
    claim_text: str
    supporting_sources: List[SourceCitation]
    confidence_score: float
    claim_type: str  # "factual", "procedural", "opinion", "definition"
    start_position: int = 0
    end_position: int = 0
    validation_status: str = "pending"  # "validated", "unverified", "conflicting"


@dataclass
class GroundedResponse:
    """Complete response with grounding information."""
    response_text: str
    grounded_claims: List[GroundedClaim]
    ungrounded_segments: List[str]
    overall_confidence: float
    source_coverage: float  # Percentage of response covered by sources
    citations: List[SourceCitation]
    generation_metadata: Dict[str, Any]
    validation_report: Dict[str, Any]


class GroundedGenerator:
    """
    Generates responses that are strictly grounded in provided source documents.
    Ensures all claims can be traced back to specific sources.
    """
    
    def __init__(self, 
                 model_name: str = "gpt-4o",
                 base_temperature: float = 0.1,
                 max_tokens: int = 1000):
        """
        Initialize the grounded generator.
        
        Args:
            model_name: LLM model to use
            base_temperature: Conservative temperature for factual accuracy
            max_tokens: Maximum tokens for generated response
        """
        self.model_name = model_name
        self.base_temperature = base_temperature
        self.max_tokens = max_tokens
        
        # Initialize LLM with conservative settings
        self.llm = ChatOpenAI(
            model=model_name,
            temperature=base_temperature,
            max_tokens=max_tokens
        )
        
        # Logger
        self.logger = logging.getLogger(__name__)
        
        # Claim type patterns for classification
        self.claim_patterns = {
            "factual": [r"\bis\b", r"\bare\b", r"\bhas\b", r"\bhave\b", r"contains", r"includes"],
            "procedural": [r"step", r"first", r"then", r"next", r"finally", r"to \w+", r"click", r"select"],
            "definition": [r"means", r"refers to", r"defined as", r"is a", r"represents"],
            "quantitative": [r"\d+", r"percent", r"minutes", r"hours", r"days", r"amount", r"cost"]
        }
        
        # Ungrounded indicators
        self.ungrounded_indicators = [
            "generally", "typically", "usually", "often", "sometimes",
            "it's known that", "as we know", "it's common",
            "in my experience", "based on common knowledge"
        ]
    
    async def generate_grounded_response(self,
                                       query: str,
                                       source_documents: List[Dict[str, Any]],
                                       query_type: str = "factual",
                                       user_profile: Optional[Dict[str, Any]] = None,
                                       conversation_context: str = "") -> GroundedResponse:
        """
        Generate a response that is strictly grounded in provided sources.
        
        Args:
            query: User query
            source_documents: List of source documents with content and metadata
            query_type: Type of query (factual, procedural, analytical, etc.)
            user_profile: Optional user profile for personalization
            conversation_context: Previous conversation context
            
        Returns:
            GroundedResponse with complete grounding information
        """
        try:
            self.logger.info(f"Generating grounded response for query: {query[:50]}...")
            
            # Step 1: Prepare source context with citations
            source_context, citation_map = self._prepare_source_context(source_documents)
            
            if not source_context.strip():
                return self._create_no_source_response(query)
            
            # Step 2: Generate response with strict grounding constraints
            response_text = await self._generate_source_constrained_response(
                query, source_context, query_type, conversation_context
            )
            
            # Step 3: Extract and validate claims
            grounded_claims = await self._extract_and_validate_claims(
                response_text, source_documents, citation_map
            )
            
            # Step 4: Identify ungrounded segments
            ungrounded_segments = self._identify_ungrounded_segments(response_text, grounded_claims)
            
            # Step 5: Calculate confidence and coverage metrics
            overall_confidence = self._calculate_overall_confidence(grounded_claims)
            source_coverage = self._calculate_source_coverage(response_text, grounded_claims)
            
            # Step 6: Generate citations
            citations = self._generate_citations(grounded_claims)
            
            # Step 7: Create validation report
            validation_report = self._create_validation_report(
                grounded_claims, ungrounded_segments, source_coverage
            )
            
            # Step 8: Create final grounded response
            grounded_response = GroundedResponse(
                response_text=response_text,
                grounded_claims=grounded_claims,
                ungrounded_segments=ungrounded_segments,
                overall_confidence=overall_confidence,
                source_coverage=source_coverage,
                citations=citations,
                generation_metadata={
                    "query": query,
                    "query_type": query_type,
                    "num_sources": len(source_documents),
                    "generation_temperature": self.base_temperature,
                    "timestamp": datetime.now().isoformat(),
                    "model_used": self.model_name
                },
                validation_report=validation_report
            )
            
            self.logger.info(f"Generated grounded response with {len(grounded_claims)} claims, "
                           f"{source_coverage:.2f} source coverage, "
                           f"{overall_confidence:.2f} confidence")
            
            return grounded_response
            
        except Exception as e:
            self.logger.error(f"Error generating grounded response: {e}")
            return self._create_error_response(query, str(e))
    
    def _prepare_source_context(self, 
                               source_documents: List[Dict[str, Any]]) -> Tuple[str, Dict[str, Dict[str, Any]]]:
        """
        Prepare source context with proper citation mapping.
        
        Returns:
            Tuple of (formatted_context, citation_map)
        """
        if not source_documents:
            return "", {}
        
        context_parts = []
        citation_map = {}
        
        for i, doc in enumerate(source_documents):
            source_id = f"[{i+1}]"
            title = doc.get("title", f"Document {i+1}")
            content = doc.get("content", "")
            
            # Clean and truncate content
            cleaned_content = self._clean_content(content)
            max_content_length = 800  # Limit per source to manage context size
            if len(cleaned_content) > max_content_length:
                cleaned_content = cleaned_content[:max_content_length] + "..."
            
            # Format source entry
            source_entry = f"{source_id} Title: {title}\nContent: {cleaned_content}\n"
            context_parts.append(source_entry)
            
            # Store in citation map
            citation_map[source_id] = {
                "id": doc.get("id", f"doc_{i}"),
                "title": title,
                "content": content,
                "metadata": doc.get("metadata", {}),
                "score": doc.get("score", 0.0)
            }
        
        formatted_context = "\n".join(context_parts)
        return formatted_context, citation_map
    
    def _clean_content(self, content: str) -> str:
        """Clean and normalize content text."""
        if not content:
            return ""
        
        # Remove excessive whitespace
        content = re.sub(r'\s+', ' ', content.strip())
        
        # Remove markdown formatting that might confuse the model
        content = re.sub(r'\*\*(.*?)\*\*', r'\1', content)  # Bold
        content = re.sub(r'\*(.*?)\*', r'\1', content)      # Italic
        content = re.sub(r'`(.*?)`', r'\1', content)        # Code
        
        return content
    
    async def _generate_source_constrained_response(self,
                                                  query: str,
                                                  source_context: str,
                                                  query_type: str,
                                                  conversation_context: str) -> str:
        """Generate response strictly constrained to source content."""
        
        # Create grounding-focused system prompt
        system_prompt = self._create_grounding_system_prompt(query_type)
        
        # Create user prompt with sources
        user_prompt = self._create_source_constrained_user_prompt(
            query, source_context, conversation_context
        )
        
        # Generate response with strict constraints
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        try:
            response = await self.llm.agenerate([messages])
            response_text = response.generations[0][0].text.strip()
            
            # Post-process to ensure grounding
            response_text = self._post_process_for_grounding(response_text)
            
            return response_text
            
        except Exception as e:
            self.logger.error(f"Error in LLM generation: {e}")
            return self._create_fallback_response(query, source_context)
    
    def _create_grounding_system_prompt(self, query_type: str) -> str:
        """Create system prompt focused on grounding requirements."""
        
        base_prompt = """You are a precision-focused AI assistant that provides information STRICTLY based on the provided source documents. 

CRITICAL GROUNDING RULES:
1. ONLY use information explicitly stated in the provided sources
2. Do NOT add any information from your training data or general knowledge
3. If information is not in the sources, explicitly state this limitation
4. Always cite sources using the provided [X] format
5. Never make assumptions or inferences beyond what sources explicitly state
6. If sources conflict, acknowledge the conflict explicitly

PROHIBITED BEHAVIORS:
- Adding context from general knowledge
- Making logical inferences not explicitly supported
- Using phrases like "generally", "typically", "usually" without source backing
- Providing information not found in the sources"""

        query_specific_additions = {
            "factual": "\nFor factual queries: Stick strictly to facts as stated in sources.",
            "procedural": "\nFor procedural queries: Only include steps explicitly mentioned in sources.",
            "analytical": "\nFor analytical queries: Base analysis only on comparisons/data in sources.",
            "definition": "\nFor definition queries: Use only definitions provided in sources."
        }
        
        return base_prompt + query_specific_additions.get(query_type, "")
    
    def _create_source_constrained_user_prompt(self,
                                             query: str,
                                             source_context: str,
                                             conversation_context: str) -> str:
        """Create user prompt with source constraints."""
        
        prompt = f"""QUERY: {query}

SOURCE DOCUMENTS:
{source_context}

{f"CONVERSATION CONTEXT: {conversation_context}" if conversation_context else ""}

INSTRUCTIONS:
1. Answer the query using ONLY the information from the source documents above
2. Cite sources using [X] format where X is the source number
3. If the sources don't contain sufficient information, say so explicitly
4. Do not add any external knowledge or assumptions
5. Structure your response clearly and directly address the query

RESPONSE:"""
        
        return prompt
    
    def _post_process_for_grounding(self, response_text: str) -> str:
        """Post-process response to ensure proper grounding."""
        
        # Remove common ungrounded phrases
        for indicator in self.ungrounded_indicators:
            response_text = response_text.replace(indicator, "")
        
        # Clean up extra whitespace
        response_text = re.sub(r'\s+', ' ', response_text.strip())
        
        # Ensure proper citation format
        response_text = re.sub(r'\[(\d+)\]', r'[\1]', response_text)
        
        return response_text
    
    def _create_fallback_response(self, query: str, source_context: str) -> str:
        """Create fallback response when LLM generation fails."""
        if not source_context:
            return f"I apologize, but I don't have sufficient information in the knowledge base to answer your query about '{query}'."
        
        # Extract first source snippet as fallback
        first_source_match = re.search(r'\[1\] Title: (.*?)\nContent: (.*?)(?:\n\[|$)', 
                                     source_context, re.DOTALL)
        if first_source_match:
            title, content = first_source_match.groups()
            content_snippet = content[:200] + ("..." if len(content) > 200 else "")
            return f"Based on the available information from '{title}': {content_snippet} [1]"
        
        return f"I found some relevant information but cannot provide a complete answer to '{query}' based on the available sources."
    
    async def _extract_and_validate_claims(self,
                                         response_text: str,
                                         source_documents: List[Dict[str, Any]],
                                         citation_map: Dict[str, Dict[str, Any]]) -> List[GroundedClaim]:
        """Extract claims from response and validate against sources."""
        
        # Split response into sentences/claims
        sentences = self._split_into_claims(response_text)
        grounded_claims = []
        
        for i, sentence in enumerate(sentences):
            if not sentence.strip():
                continue
                
            # Extract citations from sentence
            citations = self._extract_citations_from_text(sentence)
            
            # Classify claim type
            claim_type = self._classify_claim_type(sentence)
            
            # Validate against sources
            supporting_sources, confidence = await self._validate_claim_against_sources(
                sentence, citations, source_documents, citation_map
            )
            
            # Create grounded claim
            grounded_claim = GroundedClaim(
                claim_text=sentence,
                supporting_sources=supporting_sources,
                confidence_score=confidence,
                claim_type=claim_type,
                start_position=response_text.find(sentence),
                end_position=response_text.find(sentence) + len(sentence),
                validation_status="validated" if supporting_sources else "unverified"
            )
            
            grounded_claims.append(grounded_claim)
        
        return grounded_claims
    
    def _split_into_claims(self, text: str) -> List[str]:
        """Split text into individual claims/sentences."""
        # Simple sentence splitting (can be enhanced with more sophisticated NLP)
        sentences = re.split(r'[.!?]+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def _extract_citations_from_text(self, text: str) -> List[str]:
        """Extract citation markers from text."""
        citations = re.findall(r'\[(\d+)\]', text)
        return citations
    
    def _classify_claim_type(self, claim: str) -> str:
        """Classify the type of claim."""
        claim_lower = claim.lower()
        
        for claim_type, patterns in self.claim_patterns.items():
            for pattern in patterns:
                if re.search(pattern, claim_lower):
                    return claim_type
        
        return "general"
    
    async def _validate_claim_against_sources(self,
                                            claim: str,
                                            citations: List[str],
                                            source_documents: List[Dict[str, Any]],
                                            citation_map: Dict[str, Dict[str, Any]]) -> Tuple[List[SourceCitation], float]:
        """Validate a claim against its cited sources."""
        
        supporting_sources = []
        total_relevance = 0.0
        
        for citation_num in citations:
            citation_key = f"[{citation_num}]"
            
            if citation_key in citation_map:
                source_info = citation_map[citation_key]
                
                # Calculate relevance score
                relevance_score = self._calculate_claim_source_relevance(
                    claim, source_info["content"]
                )
                
                if relevance_score > 0.3:  # Threshold for considering it supporting
                    # Find best content snippet
                    snippet = self._find_best_supporting_snippet(
                        claim, source_info["content"]
                    )
                    
                    source_citation = SourceCitation(
                        source_id=source_info["id"],
                        source_title=source_info["title"],
                        content_snippet=snippet,
                        relevance_score=relevance_score,
                        metadata=source_info["metadata"]
                    )
                    
                    supporting_sources.append(source_citation)
                    total_relevance += relevance_score
        
        # Calculate confidence based on support
        confidence = min(1.0, total_relevance / max(1, len(citations))) if citations else 0.0
        
        return supporting_sources, confidence
    
    def _calculate_claim_source_relevance(self, claim: str, source_content: str) -> float:
        """Calculate how well a source supports a claim."""
        if not claim or not source_content:
            return 0.0
        
        claim_words = set(claim.lower().split())
        source_words = set(source_content.lower().split())
        
        # Simple word overlap metric (can be enhanced with semantic similarity)
        common_words = claim_words.intersection(source_words)
        relevance = len(common_words) / len(claim_words) if claim_words else 0.0
        
        return min(1.0, relevance)
    
    def _find_best_supporting_snippet(self, claim: str, source_content: str, max_length: int = 150) -> str:
        """Find the best snippet from source that supports the claim."""
        
        # Split source into sentences
        sentences = re.split(r'[.!?]+', source_content)
        best_sentence = ""
        best_score = 0.0
        
        for sentence in sentences:
            if not sentence.strip():
                continue
                
            score = self._calculate_claim_source_relevance(claim, sentence)
            if score > best_score:
                best_score = score
                best_sentence = sentence.strip()
        
        # Truncate if too long
        if len(best_sentence) > max_length:
            best_sentence = best_sentence[:max_length] + "..."
        
        return best_sentence or source_content[:max_length] + "..."
    
    def _identify_ungrounded_segments(self, 
                                    response_text: str, 
                                    grounded_claims: List[GroundedClaim]) -> List[str]:
        """Identify segments of response that are not grounded in sources."""
        
        ungrounded_segments = []
        
        # Check for ungrounded indicators
        for indicator in self.ungrounded_indicators:
            if indicator in response_text.lower():
                # Find surrounding context
                pattern = re.compile(re.escape(indicator), re.IGNORECASE)
                for match in pattern.finditer(response_text):
                    start = max(0, match.start() - 50)
                    end = min(len(response_text), match.end() + 50)
                    segment = response_text[start:end]
                    ungrounded_segments.append(segment)
        
        # Check for claims with no supporting sources
        for claim in grounded_claims:
            if not claim.supporting_sources and claim.confidence_score < 0.3:
                ungrounded_segments.append(claim.claim_text)
        
        return list(set(ungrounded_segments))  # Remove duplicates
    
    def _calculate_overall_confidence(self, grounded_claims: List[GroundedClaim]) -> float:
        """Calculate overall confidence score for the response."""
        if not grounded_claims:
            return 0.0
        
        total_confidence = sum(claim.confidence_score for claim in grounded_claims)
        return total_confidence / len(grounded_claims)
    
    def _calculate_source_coverage(self, 
                                 response_text: str, 
                                 grounded_claims: List[GroundedClaim]) -> float:
        """Calculate what percentage of response is covered by sources."""
        if not response_text or not grounded_claims:
            return 0.0
        
        grounded_chars = 0
        for claim in grounded_claims:
            if claim.supporting_sources:
                grounded_chars += len(claim.claim_text)
        
        return grounded_chars / len(response_text)
    
    def _generate_citations(self, grounded_claims: List[GroundedClaim]) -> List[SourceCitation]:
        """Generate consolidated list of citations."""
        all_citations = []
        seen_sources = set()
        
        for claim in grounded_claims:
            for source in claim.supporting_sources:
                if source.source_id not in seen_sources:
                    all_citations.append(source)
                    seen_sources.add(source.source_id)
        
        # Sort by relevance score
        return sorted(all_citations, key=lambda x: x.relevance_score, reverse=True)
    
    def _create_validation_report(self,
                                grounded_claims: List[GroundedClaim],
                                ungrounded_segments: List[str],
                                source_coverage: float) -> Dict[str, Any]:
        """Create comprehensive validation report."""
        
        total_claims = len(grounded_claims)
        grounded_claims_count = len([c for c in grounded_claims if c.supporting_sources])
        
        return {
            "total_claims": total_claims,
            "grounded_claims": grounded_claims_count,
            "ungrounded_claims": total_claims - grounded_claims_count,
            "grounding_percentage": (grounded_claims_count / total_claims * 100) if total_claims > 0 else 0,
            "source_coverage_percentage": source_coverage * 100,
            "ungrounded_segments_count": len(ungrounded_segments),
            "ungrounded_segments": ungrounded_segments,
            "claim_type_distribution": self._get_claim_type_distribution(grounded_claims),
            "confidence_distribution": self._get_confidence_distribution(grounded_claims),
            "validation_timestamp": datetime.now().isoformat()
        }
    
    def _get_claim_type_distribution(self, claims: List[GroundedClaim]) -> Dict[str, int]:
        """Get distribution of claim types."""
        distribution = {}
        for claim in claims:
            claim_type = claim.claim_type
            distribution[claim_type] = distribution.get(claim_type, 0) + 1
        return distribution
    
    def _get_confidence_distribution(self, claims: List[GroundedClaim]) -> Dict[str, int]:
        """Get distribution of confidence levels."""
        distribution = {"very_high": 0, "high": 0, "medium": 0, "low": 0, "very_low": 0}
        
        for claim in claims:
            if claim.confidence_score >= 0.9:
                distribution["very_high"] += 1
            elif claim.confidence_score >= 0.7:
                distribution["high"] += 1
            elif claim.confidence_score >= 0.5:
                distribution["medium"] += 1
            elif claim.confidence_score >= 0.3:
                distribution["low"] += 1
            else:
                distribution["very_low"] += 1
        
        return distribution
    
    def _create_no_source_response(self, query: str) -> GroundedResponse:
        """Create response when no sources are available."""
        response_text = f"I don't have sufficient information in the knowledge base to answer your query about '{query}'. Please provide more specific details or check if the information is available in our system."
        
        return GroundedResponse(
            response_text=response_text,
            grounded_claims=[],
            ungrounded_segments=[],
            overall_confidence=0.0,
            source_coverage=0.0,
            citations=[],
            generation_metadata={
                "query": query,
                "error": "no_sources_available",
                "timestamp": datetime.now().isoformat()
            },
            validation_report={
                "total_claims": 0,
                "grounded_claims": 0,
                "grounding_percentage": 0,
                "source_coverage_percentage": 0,
                "status": "no_sources"
            }
        )
    
    def _create_error_response(self, query: str, error: str) -> GroundedResponse:
        """Create error response."""
        response_text = f"I encountered an error while processing your query: '{query}'. Please try again or contact support."
        
        return GroundedResponse(
            response_text=response_text,
            grounded_claims=[],
            ungrounded_segments=[response_text],
            overall_confidence=0.0,
            source_coverage=0.0,  
            citations=[],
            generation_metadata={
                "query": query,
                "error": error,
                "timestamp": datetime.now().isoformat()
            },
            validation_report={
                "total_claims": 0,
                "grounded_claims": 0,
                "grounding_percentage": 0,
                "source_coverage_percentage": 0,
                "status": "error",
                "error_details": error
            }
        )

    def get_grounding_statistics(self) -> Dict[str, Any]:
        """Get statistics about grounding performance."""
        # This would track statistics over time in a real implementation
        return {
            "message": "Grounding statistics tracking not yet implemented",
            "suggestions": [
                "Implement response history tracking",
                "Add grounding quality metrics over time",
                "Track most common ungrounded patterns"
            ]
        }
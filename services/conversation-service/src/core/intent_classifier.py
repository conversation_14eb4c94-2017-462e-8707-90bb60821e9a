"""
Intent Classifier
Customer support specific intent detection and classification
"""

from typing import Dict, List, Any, Optional, Tuple
import logging
import re
import asyncio
from dataclasses import dataclass, field
from enum import Enum
import json
from collections import defaultdict, Counter
import numpy as np

# ML libraries for classification
try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.naive_bayes import MultinomialNB
    from sklearn.linear_model import LogisticRegression
    from sklearn.pipeline import Pipeline
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import classification_report, accuracy_score
    import joblib
    ML_LIBS_AVAILABLE = True
except ImportError:
    ML_LIBS_AVAILABLE = False

@dataclass
class IntentPrediction:
    """Intent prediction result with confidence and metadata."""
    
    intent: str
    confidence: float
    sub_intent: Optional[str] = None
    intent_category: str = "general"
    
    # Supporting evidence
    matching_patterns: List[str] = field(default_factory=list)
    key_indicators: List[str] = field(default_factory=list)
    context_clues: List[str] = field(default_factory=list)
    
    # Alternative predictions
    alternative_intents: List[Tuple[str, float]] = field(default_factory=list)
    
    # Metadata
    processing_method: str = "rule_based"
    requires_clarification: bool = False
    clarification_questions: List[str] = field(default_factory=list)

@dataclass
class IntentTrainingData:
    """Training data for intent classification."""
    
    text: str
    intent: str
    sub_intent: Optional[str] = None
    category: str = "general"
    confidence: float = 1.0
    source: str = "manual"

class CustomerSupportIntent(Enum):
    """Customer support specific intents."""
    
    # Account Management
    ACCOUNT_LOGIN = "account_login"
    ACCOUNT_REGISTRATION = "account_registration"
    ACCOUNT_SETTINGS = "account_settings"
    PASSWORD_RESET = "password_reset"
    ACCOUNT_DELETION = "account_deletion"
    
    # Billing & Payments
    BILLING_INQUIRY = "billing_inquiry"
    PAYMENT_ISSUE = "payment_issue"
    REFUND_REQUEST = "refund_request"
    SUBSCRIPTION_CHANGE = "subscription_change"
    INVOICE_REQUEST = "invoice_request"
    
    # Technical Support
    TECHNICAL_ISSUE = "technical_issue"
    BUG_REPORT = "bug_report"
    FEATURE_REQUEST = "feature_request"
    INTEGRATION_HELP = "integration_help"
    API_SUPPORT = "api_support"
    
    # Product Information
    PRODUCT_INQUIRY = "product_inquiry"
    FEATURE_EXPLANATION = "feature_explanation"
    PRICING_INQUIRY = "pricing_inquiry"
    COMPARISON_REQUEST = "comparison_request"
    DEMO_REQUEST = "demo_request"
    
    # General Support
    GENERAL_QUESTION = "general_question"
    COMPLAINT = "complaint"
    COMPLIMENT = "compliment"
    FEEDBACK = "feedback"
    CONTACT_REQUEST = "contact_request"
    
    # Process & Procedures
    HOW_TO_GUIDE = "how_to_guide"
    SETUP_ASSISTANCE = "setup_assistance"
    TROUBLESHOOTING = "troubleshooting"
    BEST_PRACTICES = "best_practices"
    
    # Sales & Business
    SALES_INQUIRY = "sales_inquiry"
    PARTNERSHIP_INQUIRY = "partnership_inquiry"
    ENTERPRISE_INQUIRY = "enterprise_inquiry"
    TRIAL_REQUEST = "trial_request"

class IntentCategory(Enum):
    """High-level intent categories."""
    
    ACCOUNT = "account"
    BILLING = "billing"
    TECHNICAL = "technical"
    PRODUCT = "product"
    SUPPORT = "support"
    PROCESS = "process"
    SALES = "sales"

class IntentClassifier:
    """Advanced intent classification for customer support queries."""
    
    def __init__(self, model_path: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        
        # Classification models
        self.ml_classifier = None
        self.tfidf_vectorizer = None
        self.is_trained = False
        
        # Rule-based patterns
        self.intent_patterns = {}
        self.category_patterns = {}
        
        # Training data
        self.training_data: List[IntentTrainingData] = []
        
        # Performance tracking
        self.classification_stats = defaultdict(int)
        self.confidence_threshold = 0.6
        
        # Initialize components
        self._initialize_patterns()
        self._load_training_data()
        
        # Load or train model
        if model_path and self._load_model(model_path):
            self.logger.info("Loaded pre-trained intent classification model")
        elif ML_LIBS_AVAILABLE:
            asyncio.create_task(self._train_initial_model())

    def _initialize_patterns(self):
        """Initialize rule-based intent patterns."""
        
        self.intent_patterns = {
            # Account Management
            CustomerSupportIntent.ACCOUNT_LOGIN.value: [
                r'\b(login|log in|sign in|signin|access|authenticate)\b',
                r'\b(can\'t|cannot|unable to).*(login|log in|sign in|access)\b',
                r'\b(forgot|lost).*(password|credentials)\b',
                r'\b(locked out|account locked)\b'
            ],
            
            CustomerSupportIntent.ACCOUNT_REGISTRATION.value: [
                r'\b(register|registration|sign up|signup|create account)\b',
                r'\b(new account|account creation)\b',
                r'\b(how to.*(register|sign up|create account))\b'
            ],
            
            CustomerSupportIntent.PASSWORD_RESET.value: [
                r'\b(reset|change|update).*(password|credentials)\b',
                r'\b(forgot|forgotten|lost).*(password|credentials)\b',
                r'\b(password.*(reset|recovery|change))\b'
            ],
            
            # Billing & Payments
            CustomerSupportIntent.BILLING_INQUIRY.value: [
                r'\b(billing|invoice|payment|charge|subscription)\b',
                r'\b(bill|invoice).*(question|inquiry|issue)\b',
                r'\b(payment.*(history|status|method))\b'
            ],
            
            CustomerSupportIntent.REFUND_REQUEST.value: [
                r'\b(refund|money back|return|cancel.*charge)\b',
                r'\b(want.*(refund|money back))\b',
                r'\b(charged.*mistake|wrong charge)\b'
            ],
            
            CustomerSupportIntent.PAYMENT_ISSUE.value: [
                r'\b(payment.*(failed|declined|error|issue|problem))\b',
                r'\b(card.*(declined|rejected|not working))\b',
                r'\b(billing.*(error|issue|problem))\b'
            ],
            
            # Technical Support
            CustomerSupportIntent.TECHNICAL_ISSUE.value: [
                r'\b(error|bug|issue|problem|broken|not working)\b',
                r'\b(technical.*(issue|problem|support))\b',
                r'\b(something.*(wrong|broken|not working))\b'
            ],
            
            CustomerSupportIntent.BUG_REPORT.value: [
                r'\b(bug|glitch|error|malfunction)\b',
                r'\b(report.*(bug|issue|problem))\b',
                r'\b(found.*(bug|error|issue))\b'
            ],
            
            CustomerSupportIntent.FEATURE_REQUEST.value: [
                r'\b(feature.*(request|suggestion|idea))\b',
                r'\b(add.*(feature|functionality))\b',
                r'\b(would like.*(feature|option))\b',
                r'\b(suggestion|idea|enhancement)\b'
            ],
            
            CustomerSupportIntent.API_SUPPORT.value: [
                r'\b(api|endpoint|integration|webhook)\b',
                r'\b(api.*(help|support|issue|problem))\b',
                r'\b(integrate|integration)\b'
            ],
            
            # Product Information
            CustomerSupportIntent.PRODUCT_INQUIRY.value: [
                r'\b(what is|tell me about|explain)\b',
                r'\b(product.*(information|details|features))\b',
                r'\b(how does.*(work|function))\b'
            ],
            
            CustomerSupportIntent.PRICING_INQUIRY.value: [
                r'\b(price|pricing|cost|fee|rate)\b',
                r'\b(how much|what.*cost)\b',
                r'\b(pricing.*(plan|tier|option))\b'
            ],
            
            CustomerSupportIntent.DEMO_REQUEST.value: [
                r'\b(demo|demonstration|trial|test)\b',
                r'\b(show me|can I see)\b',
                r'\b(try.*(product|service|feature))\b'
            ],
            
            # Process & Procedures
            CustomerSupportIntent.HOW_TO_GUIDE.value: [
                r'\b(how to|how do I|how can I)\b',
                r'\b(step by step|guide|tutorial|instructions)\b',
                r'\b(walk me through|show me how)\b'
            ],
            
            CustomerSupportIntent.SETUP_ASSISTANCE.value: [
                r'\b(setup|set up|configure|install)\b',
                r'\b(getting started|initial setup)\b',
                r'\b(help.*(setup|configure|install))\b'
            ],
            
            CustomerSupportIntent.TROUBLESHOOTING.value: [
                r'\b(troubleshoot|diagnose|fix|solve)\b',
                r'\b(not working|broken|issue)\b',
                r'\b(help.*(fix|solve|resolve))\b'
            ],
            
            # General Support
            CustomerSupportIntent.GENERAL_QUESTION.value: [
                r'\b(question|ask|inquiry|wonder)\b',
                r'\b(can you|could you|would you)\b',
                r'\b(need help|need assistance)\b'
            ],
            
            CustomerSupportIntent.COMPLAINT.value: [
                r'\b(complain|complaint|unhappy|dissatisfied)\b',
                r'\b(terrible|awful|horrible|worst)\b',
                r'\b(angry|frustrated|upset)\b'
            ],
            
            CustomerSupportIntent.COMPLIMENT.value: [
                r'\b(great|excellent|amazing|wonderful|love)\b',
                r'\b(thank you|thanks|appreciate)\b',
                r'\b(compliment|praise|positive)\b'
            ],
            
            CustomerSupportIntent.CONTACT_REQUEST.value: [
                r'\b(contact|reach|speak to|talk to)\b',
                r'\b(phone number|email|address)\b',
                r'\b(human|person|representative)\b'
            ],
            
            # Sales & Business
            CustomerSupportIntent.SALES_INQUIRY.value: [
                r'\b(sales|purchase|buy|buying)\b',
                r'\b(interested in|want to buy)\b',
                r'\b(sales.*(team|rep|representative))\b'
            ],
            
            CustomerSupportIntent.ENTERPRISE_INQUIRY.value: [
                r'\b(enterprise|business|corporate|company)\b',
                r'\b(bulk|volume|large scale)\b',
                r'\b(enterprise.*(plan|solution|pricing))\b'
            ]
        }
        
        # Category patterns
        self.category_patterns = {
            IntentCategory.ACCOUNT.value: [
                r'\b(account|login|password|profile|user)\b'
            ],
            IntentCategory.BILLING.value: [
                r'\b(billing|payment|invoice|subscription|refund|charge)\b'
            ],
            IntentCategory.TECHNICAL.value: [
                r'\b(error|bug|technical|api|integration|code)\b'
            ],
            IntentCategory.PRODUCT.value: [
                r'\b(product|feature|functionality|pricing|demo)\b'
            ],
            IntentCategory.SUPPORT.value: [
                r'\b(help|support|assistance|question|issue)\b'
            ],
            IntentCategory.PROCESS.value: [
                r'\b(how to|setup|configure|guide|tutorial|troubleshoot)\b'
            ],
            IntentCategory.SALES.value: [
                r'\b(sales|purchase|buy|enterprise|business|trial)\b'
            ]
        }

    def _load_training_data(self):
        """Load initial training data for the classifier."""
        
        # Sample training data - in production, this would come from a database
        sample_data = [
            # Account Management
            ("I can't log into my account", CustomerSupportIntent.ACCOUNT_LOGIN.value, IntentCategory.ACCOUNT.value),
            ("How do I reset my password?", CustomerSupportIntent.PASSWORD_RESET.value, IntentCategory.ACCOUNT.value),
            ("I want to create a new account", CustomerSupportIntent.ACCOUNT_REGISTRATION.value, IntentCategory.ACCOUNT.value),
            ("My account is locked", CustomerSupportIntent.ACCOUNT_LOGIN.value, IntentCategory.ACCOUNT.value),
            
            # Billing
            ("I need a refund for my last payment", CustomerSupportIntent.REFUND_REQUEST.value, IntentCategory.BILLING.value),
            ("My payment failed", CustomerSupportIntent.PAYMENT_ISSUE.value, IntentCategory.BILLING.value),
            ("Can I see my billing history?", CustomerSupportIntent.BILLING_INQUIRY.value, IntentCategory.BILLING.value),
            ("I was charged twice", CustomerSupportIntent.PAYMENT_ISSUE.value, IntentCategory.BILLING.value),
            
            # Technical
            ("I found a bug in your software", CustomerSupportIntent.BUG_REPORT.value, IntentCategory.TECHNICAL.value),
            ("The API is not working", CustomerSupportIntent.API_SUPPORT.value, IntentCategory.TECHNICAL.value),
            ("I'm having technical issues", CustomerSupportIntent.TECHNICAL_ISSUE.value, IntentCategory.TECHNICAL.value),
            ("Can you add this feature?", CustomerSupportIntent.FEATURE_REQUEST.value, IntentCategory.TECHNICAL.value),
            
            # Product
            ("What does your product do?", CustomerSupportIntent.PRODUCT_INQUIRY.value, IntentCategory.PRODUCT.value),
            ("How much does it cost?", CustomerSupportIntent.PRICING_INQUIRY.value, IntentCategory.PRODUCT.value),
            ("Can I get a demo?", CustomerSupportIntent.DEMO_REQUEST.value, IntentCategory.PRODUCT.value),
            ("What's the difference between plans?", CustomerSupportIntent.COMPARISON_REQUEST.value, IntentCategory.PRODUCT.value),
            
            # Process
            ("How do I set up the integration?", CustomerSupportIntent.SETUP_ASSISTANCE.value, IntentCategory.PROCESS.value),
            ("Can you walk me through the process?", CustomerSupportIntent.HOW_TO_GUIDE.value, IntentCategory.PROCESS.value),
            ("I need help troubleshooting", CustomerSupportIntent.TROUBLESHOOTING.value, IntentCategory.PROCESS.value),
            
            # General
            ("I have a question", CustomerSupportIntent.GENERAL_QUESTION.value, IntentCategory.SUPPORT.value),
            ("I'm very unhappy with the service", CustomerSupportIntent.COMPLAINT.value, IntentCategory.SUPPORT.value),
            ("Your product is amazing!", CustomerSupportIntent.COMPLIMENT.value, IntentCategory.SUPPORT.value),
            ("I need to speak to someone", CustomerSupportIntent.CONTACT_REQUEST.value, IntentCategory.SUPPORT.value),
            
            # Sales
            ("I want to buy your product", CustomerSupportIntent.SALES_INQUIRY.value, IntentCategory.SALES.value),
            ("Do you have enterprise pricing?", CustomerSupportIntent.ENTERPRISE_INQUIRY.value, IntentCategory.SALES.value),
            ("Can I start a trial?", CustomerSupportIntent.TRIAL_REQUEST.value, IntentCategory.SALES.value)
        ]
        
        for text, intent, category in sample_data:
            self.training_data.append(IntentTrainingData(
                text=text,
                intent=intent,
                category=category,
                source="initial_data"
            ))
        
        self.logger.info(f"Loaded {len(self.training_data)} training examples")

    async def classify_intent(self, 
                            query: str,
                            context: Optional[Dict[str, Any]] = None) -> IntentPrediction:
        """Classify intent of a customer support query."""
        
        try:
            # Try ML classification first if available
            if self.is_trained and self.ml_classifier:
                ml_prediction = await self._ml_classify(query)
                if ml_prediction.confidence >= self.confidence_threshold:
                    return ml_prediction
            
            # Fallback to rule-based classification
            rule_prediction = await self._rule_based_classify(query, context)
            
            # Combine predictions if both are available
            if self.is_trained and self.ml_classifier:
                combined_prediction = await self._combine_predictions(ml_prediction, rule_prediction)
                return combined_prediction
            
            return rule_prediction
            
        except Exception as e:
            self.logger.error(f"Intent classification failed: {e}")
            return self._create_fallback_prediction(query)

    async def _ml_classify(self, query: str) -> IntentPrediction:
        """Classify using machine learning model."""
        
        if not self.ml_classifier or not self.tfidf_vectorizer:
            raise ValueError("ML classifier not available")
        
        try:
            # Vectorize query
            query_vector = self.tfidf_vectorizer.transform([query])
            
            # Get prediction and probabilities
            prediction = self.ml_classifier.predict(query_vector)[0]
            probabilities = self.ml_classifier.predict_proba(query_vector)[0]
            
            # Get class labels
            classes = self.ml_classifier.classes_
            
            # Find confidence and alternatives
            max_prob_idx = np.argmax(probabilities)
            confidence = probabilities[max_prob_idx]
            
            # Get alternative predictions
            alternatives = []
            for i, (class_label, prob) in enumerate(zip(classes, probabilities)):
                if i != max_prob_idx and prob > 0.1:  # Only include alternatives with >10% probability
                    alternatives.append((class_label, float(prob)))
            
            # Sort alternatives by probability
            alternatives.sort(key=lambda x: x[1], reverse=True)
            
            # Determine category
            category = self._get_intent_category(prediction)
            
            return IntentPrediction(
                intent=prediction,
                confidence=float(confidence),
                intent_category=category,
                alternative_intents=alternatives[:3],  # Top 3 alternatives
                processing_method="ml_classification"
            )
            
        except Exception as e:
            self.logger.error(f"ML classification failed: {e}")
            raise

    async def _rule_based_classify(self, 
                                 query: str,
                                 context: Optional[Dict[str, Any]] = None) -> IntentPrediction:
        """Classify using rule-based patterns."""
        
        query_lower = query.lower()
        intent_scores = defaultdict(float)
        matching_patterns = defaultdict(list)
        
        # Score each intent based on pattern matches
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, query_lower)
                if matches:
                    # Weight by pattern specificity and match count
                    pattern_weight = len(pattern) / 100.0  # Longer patterns are more specific
                    match_weight = len(matches) * pattern_weight
                    intent_scores[intent] += match_weight
                    matching_patterns[intent].append(pattern)
        
        # Apply context boosting if available
        if context:
            intent_scores = self._apply_context_boosting(intent_scores, context)
        
        # Find best intent
        if intent_scores:
            best_intent = max(intent_scores, key=intent_scores.get)
            confidence = min(intent_scores[best_intent], 1.0)  # Cap at 1.0
            
            # Get alternatives
            alternatives = []
            for intent, score in intent_scores.items():
                if intent != best_intent and score > 0.1:
                    alternatives.append((intent, score))
            alternatives.sort(key=lambda x: x[1], reverse=True)
            
            # Extract key indicators
            key_indicators = self._extract_key_indicators(query_lower, best_intent)
            
            # Determine if clarification is needed
            requires_clarification = confidence < 0.5 or len(alternatives) > 2
            clarification_questions = []
            if requires_clarification:
                clarification_questions = self._generate_clarification_questions(
                    best_intent, alternatives[:2]
                )
            
            return IntentPrediction(
                intent=best_intent,
                confidence=confidence,
                intent_category=self._get_intent_category(best_intent),
                matching_patterns=matching_patterns[best_intent],
                key_indicators=key_indicators,
                alternative_intents=alternatives[:3],
                processing_method="rule_based",
                requires_clarification=requires_clarification,
                clarification_questions=clarification_questions
            )
        
        # No clear intent found
        return IntentPrediction(
            intent=CustomerSupportIntent.GENERAL_QUESTION.value,
            confidence=0.3,
            intent_category=IntentCategory.SUPPORT.value,
            processing_method="rule_based_fallback",
            requires_clarification=True,
            clarification_questions=["Could you provide more details about what you need help with?"]
        )

    def _apply_context_boosting(self, 
                              intent_scores: Dict[str, float],
                              context: Dict[str, Any]) -> Dict[str, float]:
        """Apply context-based boosting to intent scores."""
        
        boosted_scores = intent_scores.copy()
        
        # Boost based on conversation history
        if 'previous_intents' in context:
            previous_intents = context['previous_intents']
            for intent in boosted_scores:
                if intent in previous_intents:
                    boosted_scores[intent] *= 1.2  # 20% boost for related intents
        
        # Boost based on user profile
        if 'user_profile' in context:
            profile = context['user_profile']
            
            # Boost technical intents for technical users
            if profile.get('expertise_level') == 'advanced':
                technical_intents = [
                    CustomerSupportIntent.API_SUPPORT.value,
                    CustomerSupportIntent.INTEGRATION_HELP.value,
                    CustomerSupportIntent.BUG_REPORT.value
                ]
                for intent in technical_intents:
                    if intent in boosted_scores:
                        boosted_scores[intent] *= 1.3
            
            # Boost billing intents for paying customers
            if profile.get('subscription_status') == 'active':
                billing_intents = [
                    CustomerSupportIntent.BILLING_INQUIRY.value,
                    CustomerSupportIntent.SUBSCRIPTION_CHANGE.value
                ]
                for intent in billing_intents:
                    if intent in boosted_scores:
                        boosted_scores[intent] *= 1.2
        
        return boosted_scores

    def _extract_key_indicators(self, query: str, intent: str) -> List[str]:
        """Extract key words/phrases that led to the intent classification."""
        
        indicators = []
        
        if intent in self.intent_patterns:
            patterns = self.intent_patterns[intent]
            for pattern in patterns:
                matches = re.findall(pattern, query)
                indicators.extend(matches)
        
        # Remove duplicates and empty strings
        return list(set([indicator for indicator in indicators if indicator.strip()]))

    def _generate_clarification_questions(self, 
                                        primary_intent: str,
                                        alternatives: List[Tuple[str, float]]) -> List[str]:
        """Generate clarification questions when intent is ambiguous."""
        
        questions = []
        
        # Intent-specific clarification questions
        clarification_map = {
            CustomerSupportIntent.TECHNICAL_ISSUE.value: [
                "What specific error or issue are you experiencing?",
                "Can you describe what you were trying to do when the problem occurred?"
            ],
            CustomerSupportIntent.BILLING_INQUIRY.value: [
                "Are you asking about a specific charge or your billing in general?",
                "Do you need help with a payment issue or billing information?"
            ],
            CustomerSupportIntent.ACCOUNT_LOGIN.value: [
                "Are you having trouble logging in or resetting your password?",
                "What happens when you try to access your account?"
            ],
            CustomerSupportIntent.PRODUCT_INQUIRY.value: [
                "Are you looking for information about features, pricing, or something else?",
                "What specific aspect of our product interests you?"
            ]
        }
        
        if primary_intent in clarification_map:
            questions.extend(clarification_map[primary_intent])
        
        # Add questions based on alternatives
        if alternatives:
            alt_intents = [alt[0] for alt in alternatives]
            if CustomerSupportIntent.BILLING_INQUIRY.value in alt_intents:
                questions.append("Is this related to billing or payments?")
            if CustomerSupportIntent.TECHNICAL_ISSUE.value in alt_intents:
                questions.append("Are you experiencing a technical problem?")
        
        return questions[:2]  # Limit to 2 questions

    async def _combine_predictions(self, 
                                 ml_prediction: IntentPrediction,
                                 rule_prediction: IntentPrediction) -> IntentPrediction:
        """Combine ML and rule-based predictions."""
        
        # If ML prediction is confident, use it
        if ml_prediction.confidence >= 0.8:
            return ml_prediction
        
        # If rule-based prediction is confident, use it
        if rule_prediction.confidence >= 0.8:
            return rule_prediction
        
        # Combine predictions with weighted average
        ml_weight = 0.6
        rule_weight = 0.4
        
        # If both predict the same intent, boost confidence
        if ml_prediction.intent == rule_prediction.intent:
            combined_confidence = min(
                ml_prediction.confidence * ml_weight + rule_prediction.confidence * rule_weight + 0.2,
                1.0
            )
            
            return IntentPrediction(
                intent=ml_prediction.intent,
                confidence=combined_confidence,
                intent_category=ml_prediction.intent_category,
                matching_patterns=rule_prediction.matching_patterns,
                key_indicators=rule_prediction.key_indicators,
                alternative_intents=ml_prediction.alternative_intents,
                processing_method="combined_ml_rule"
            )
        
        # Different predictions - return the more confident one
        if ml_prediction.confidence > rule_prediction.confidence:
            return ml_prediction
        else:
            return rule_prediction

    def _get_intent_category(self, intent: str) -> str:
        """Get the category for a given intent."""
        
        # Map intents to categories
        intent_category_map = {
            # Account
            CustomerSupportIntent.ACCOUNT_LOGIN.value: IntentCategory.ACCOUNT.value,
            CustomerSupportIntent.ACCOUNT_REGISTRATION.value: IntentCategory.ACCOUNT.value,
            CustomerSupportIntent.ACCOUNT_SETTINGS.value: IntentCategory.ACCOUNT.value,
            CustomerSupportIntent.PASSWORD_RESET.value: IntentCategory.ACCOUNT.value,
            CustomerSupportIntent.ACCOUNT_DELETION.value: IntentCategory.ACCOUNT.value,
            
            # Billing
            CustomerSupportIntent.BILLING_INQUIRY.value: IntentCategory.BILLING.value,
            CustomerSupportIntent.PAYMENT_ISSUE.value: IntentCategory.BILLING.value,
            CustomerSupportIntent.REFUND_REQUEST.value: IntentCategory.BILLING.value,
            CustomerSupportIntent.SUBSCRIPTION_CHANGE.value: IntentCategory.BILLING.value,
            CustomerSupportIntent.INVOICE_REQUEST.value: IntentCategory.BILLING.value,
            
            # Technical
            CustomerSupportIntent.TECHNICAL_ISSUE.value: IntentCategory.TECHNICAL.value,
            CustomerSupportIntent.BUG_REPORT.value: IntentCategory.TECHNICAL.value,
            CustomerSupportIntent.FEATURE_REQUEST.value: IntentCategory.TECHNICAL.value,
            CustomerSupportIntent.INTEGRATION_HELP.value: IntentCategory.TECHNICAL.value,
            CustomerSupportIntent.API_SUPPORT.value: IntentCategory.TECHNICAL.value,
            
            # Product
            CustomerSupportIntent.PRODUCT_INQUIRY.value: IntentCategory.PRODUCT.value,
            CustomerSupportIntent.FEATURE_EXPLANATION.value: IntentCategory.PRODUCT.value,
            CustomerSupportIntent.PRICING_INQUIRY.value: IntentCategory.PRODUCT.value,
            CustomerSupportIntent.COMPARISON_REQUEST.value: IntentCategory.PRODUCT.value,
            CustomerSupportIntent.DEMO_REQUEST.value: IntentCategory.PRODUCT.value,
            
            # Support
            CustomerSupportIntent.GENERAL_QUESTION.value: IntentCategory.SUPPORT.value,
            CustomerSupportIntent.COMPLAINT.value: IntentCategory.SUPPORT.value,
            CustomerSupportIntent.COMPLIMENT.value: IntentCategory.SUPPORT.value,
            CustomerSupportIntent.FEEDBACK.value: IntentCategory.SUPPORT.value,
            CustomerSupportIntent.CONTACT_REQUEST.value: IntentCategory.SUPPORT.value,
            
            # Process
            CustomerSupportIntent.HOW_TO_GUIDE.value: IntentCategory.PROCESS.value,
            CustomerSupportIntent.SETUP_ASSISTANCE.value: IntentCategory.PROCESS.value,
            CustomerSupportIntent.TROUBLESHOOTING.value: IntentCategory.PROCESS.value,
            CustomerSupportIntent.BEST_PRACTICES.value: IntentCategory.PROCESS.value,
            
            # Sales
            CustomerSupportIntent.SALES_INQUIRY.value: IntentCategory.SALES.value,
            CustomerSupportIntent.PARTNERSHIP_INQUIRY.value: IntentCategory.SALES.value,
            CustomerSupportIntent.ENTERPRISE_INQUIRY.value: IntentCategory.SALES.value,
            CustomerSupportIntent.TRIAL_REQUEST.value: IntentCategory.SALES.value
        }
        
        return intent_category_map.get(intent, IntentCategory.SUPPORT.value)

    def _create_fallback_prediction(self, query: str) -> IntentPrediction:
        """Create a fallback prediction when classification fails."""
        
        return IntentPrediction(
            intent=CustomerSupportIntent.GENERAL_QUESTION.value,
            confidence=0.2,
            intent_category=IntentCategory.SUPPORT.value,
            processing_method="fallback",
            requires_clarification=True,
            clarification_questions=[
                "I'd be happy to help! Could you provide more details about what you need assistance with?"
            ]
        )

    async def _train_initial_model(self):
        """Train initial ML model with available training data."""
        
        if not ML_LIBS_AVAILABLE or len(self.training_data) < 10:
            self.logger.warning("Insufficient data or libraries for ML training")
            return
        
        try:
            # Prepare training data
            texts = [data.text for data in self.training_data]
            labels = [data.intent for data in self.training_data]
            
            # Create pipeline
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=5000,
                stop_words='english',
                ngram_range=(1, 2),
                lowercase=True
            )
            
            self.ml_classifier = LogisticRegression(
                random_state=42,
                max_iter=1000
            )
            
            # Train model
            X = self.tfidf_vectorizer.fit_transform(texts)
            self.ml_classifier.fit(X, labels)
            
            self.is_trained = True
            self.logger.info(f"Trained ML classifier with {len(texts)} examples")
            
        except Exception as e:
            self.logger.error(f"ML model training failed: {e}")

    def add_training_example(self, 
                           text: str,
                           intent: str,
                           category: Optional[str] = None,
                           confidence: float = 1.0):
        """Add a new training example and retrain if needed."""
        
        training_example = IntentTrainingData(
            text=text,
            intent=intent,
            category=category or self._get_intent_category(intent),
            confidence=confidence,
            source="user_feedback"
        )
        
        self.training_data.append(training_example)
        
        # Retrain model if we have enough examples
        if len(self.training_data) % 50 == 0:  # Retrain every 50 examples
            asyncio.create_task(self._train_initial_model())

    def _load_model(self, model_path: str) -> bool:
        """Load pre-trained model from file."""
        
        try:
            import os
            if os.path.exists(model_path):
                model_data = joblib.load(model_path)
                self.ml_classifier = model_data['classifier']
                self.tfidf_vectorizer = model_data['vectorizer']
                self.is_trained = True
                return True
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
        
        return False

    def save_model(self, model_path: str) -> bool:
        """Save trained model to file."""
        
        if not self.is_trained:
            self.logger.warning("No trained model to save")
            return False
        
        try:
            model_data = {
                'classifier': self.ml_classifier,
                'vectorizer': self.tfidf_vectorizer,
                'training_data_size': len(self.training_data)
            }
            joblib.dump(model_data, model_path)
            self.logger.info(f"Model saved to {model_path}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to save model: {e}")
            return False

    async def get_classification_stats(self) -> Dict[str, Any]:
        """Get classification performance statistics."""
        
        stats = {
            'total_classifications': self.classification_stats['total'],
            'ml_classifications': self.classification_stats['ml'],
            'rule_based_classifications': self.classification_stats['rule_based'],
            'combined_classifications': self.classification_stats['combined'],
            'training_examples': len(self.training_data),
            'is_ml_trained': self.is_trained
        }
        
        if self.is_trained and len(self.training_data) > 0:
            # Calculate intent distribution
            intent_counts = Counter([data.intent for data in self.training_data])
            stats['intent_distribution'] = dict(intent_counts.most_common(10))
        
        return stats

    async def get_supported_intents(self) -> List[Dict[str, Any]]:
        """Get list of all supported intents with descriptions."""
        
        intent_descriptions = {
            CustomerSupportIntent.ACCOUNT_LOGIN.value: "User having trouble logging into their account",
            CustomerSupportIntent.ACCOUNT_REGISTRATION.value: "User wants to create a new account",
            CustomerSupportIntent.PASSWORD_RESET.value: "User needs to reset or change their password",
            CustomerSupportIntent.BILLING_INQUIRY.value: "Questions about billing, invoices, or payments",
            CustomerSupportIntent.REFUND_REQUEST.value: "User requesting a refund or money back",
            CustomerSupportIntent.PAYMENT_ISSUE.value: "Problems with payment processing or billing",
            CustomerSupportIntent.TECHNICAL_ISSUE.value: "General technical problems or errors",
            CustomerSupportIntent.BUG_REPORT.value: "User reporting a bug or software issue",
            CustomerSupportIntent.FEATURE_REQUEST.value: "User suggesting new features or improvements",
            CustomerSupportIntent.API_SUPPORT.value: "Help with API integration or technical implementation",
            CustomerSupportIntent.PRODUCT_INQUIRY.value: "Questions about product features or capabilities",
            CustomerSupportIntent.PRICING_INQUIRY.value: "Questions about pricing, plans, or costs",
            CustomerSupportIntent.DEMO_REQUEST.value: "User wants to see a demo or trial",
            CustomerSupportIntent.HOW_TO_GUIDE.value: "User needs step-by-step instructions",
            CustomerSupportIntent.SETUP_ASSISTANCE.value: "Help with initial setup or configuration",
            CustomerSupportIntent.TROUBLESHOOTING.value: "Help diagnosing and fixing problems",
            CustomerSupportIntent.GENERAL_QUESTION.value: "General questions or inquiries",
            CustomerSupportIntent.COMPLAINT.value: "User expressing dissatisfaction or complaints",
            CustomerSupportIntent.COMPLIMENT.value: "User expressing satisfaction or praise",
            CustomerSupportIntent.CONTACT_REQUEST.value: "User wants to contact support or sales",
            CustomerSupportIntent.SALES_INQUIRY.value: "Questions about purchasing or sales",
            CustomerSupportIntent.ENTERPRISE_INQUIRY.value: "Enterprise or business-specific inquiries"
        }
        
        supported_intents = []
        for intent in CustomerSupportIntent:
            supported_intents.append({
                'intent': intent.value,
                'category': self._get_intent_category(intent.value),
                'description': intent_descriptions.get(intent.value, "No description available")
            })
        
        return supported_intents
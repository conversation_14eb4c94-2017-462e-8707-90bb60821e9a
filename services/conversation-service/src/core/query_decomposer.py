"""
Multi-Hop Query Decomposition System
Breaks complex queries into manageable sub-questions for iterative processing.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import asyncio
import openai
from ..models.query_analysis import QueryAnalysis, QueryType

class DecompositionStrategy(Enum):
    SEQUENTIAL = "sequential"  # Step-by-step breakdown
    PARALLEL = "parallel"     # Independent sub-questions
    HIERARCHICAL = "hierarchical"  # Main question with supporting questions
    CONDITIONAL = "conditional"    # Dependent on previous answers

@dataclass
class SubQuery:
    """Individual sub-question in a decomposed query."""
    id: str
    question: str
    strategy: DecompositionStrategy
    dependencies: List[str]  # IDs of sub-queries this depends on
    priority: int  # Execution priority (1 = highest)
    context_needed: bool = True
    estimated_complexity: float = 1.0

@dataclass
class DecompositionResult:
    """Result of query decomposition."""
    original_query: str
    sub_queries: List[SubQuery]
    execution_strategy: DecompositionStrategy
    estimated_total_time: float
    requires_synthesis: bool = True

class QueryDecomposer:
    """Decomposes complex queries into manageable sub-questions."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.openai_client = openai.AsyncOpenAI(api_key=config.openai_api_key)
        
        # Decomposition prompts
        self.decomposition_prompt = """
You are an expert at breaking down complex customer support queries into simpler sub-questions.

Analyze this query and determine if it needs decomposition:
Query: "{query}"
Query Type: {query_type}

If the query is complex and would benefit from decomposition, break it into 2-5 sub-questions that:
1. Are simpler and more focused than the original
2. Together cover all aspects of the original query
3. Can be answered independently or with minimal dependencies

Respond in JSON format:
{{
    "needs_decomposition": true/false,
    "reasoning": "explanation of why decomposition is/isn't needed",
    "sub_queries": [
        {{
            "question": "specific sub-question",
            "strategy": "sequential|parallel|hierarchical|conditional",
            "dependencies": ["id1", "id2"],
            "priority": 1-5,
            "estimated_complexity": 0.1-2.0
        }}
    ],
    "execution_strategy": "overall strategy",
    "requires_synthesis": true/false
}}
"""

    async def should_decompose(self, query: str, query_analysis: QueryAnalysis) -> bool:
        """Determine if a query needs decomposition."""
        
        # Simple heuristics first
        if len(query.split()) < 10:
            return False
            
        # Check for complexity indicators
        complexity_indicators = [
            "and", "also", "additionally", "furthermore", "moreover",
            "compare", "difference", "versus", "vs", "both",
            "step by step", "how to", "process", "workflow",
            "first", "then", "next", "finally", "after",
            "multiple", "several", "various", "different"
        ]
        
        query_lower = query.lower()
        indicator_count = sum(1 for indicator in complexity_indicators if indicator in query_lower)
        
        # Auto-decompose if high complexity indicators
        if indicator_count >= 3:
            return True
            
        # Check query type
        complex_types = [QueryType.COMPARISON, QueryType.PROCESS, QueryType.TROUBLESHOOTING]
        if query_analysis.query_type in complex_types and len(query.split()) > 15:
            return True
            
        return False

    async def decompose_query(
        self, 
        query: str, 
        query_analysis: QueryAnalysis,
        conversation_context: Optional[Dict] = None
    ) -> DecompositionResult:
        """Decompose a complex query into sub-questions."""
        
        try:
            # Check if decomposition is needed
            if not await self.should_decompose(query, query_analysis):
                return DecompositionResult(
                    original_query=query,
                    sub_queries=[],
                    execution_strategy=DecompositionStrategy.SEQUENTIAL,
                    estimated_total_time=1.0,
                    requires_synthesis=False
                )
            
            # Use LLM for intelligent decomposition
            prompt = self.decomposition_prompt.format(
                query=query,
                query_type=query_analysis.query_type.value
            )
            
            response = await self.openai_client.chat.completions.create(
                model=self.config.openai_model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=1000
            )
            
            # Parse LLM response
            import json
            result_data = json.loads(response.choices[0].message.content)
            
            if not result_data.get("needs_decomposition", False):
                return DecompositionResult(
                    original_query=query,
                    sub_queries=[],
                    execution_strategy=DecompositionStrategy.SEQUENTIAL,
                    estimated_total_time=1.0,
                    requires_synthesis=False
                )
            
            # Create sub-queries
            sub_queries = []
            for i, sq_data in enumerate(result_data.get("sub_queries", [])):
                sub_query = SubQuery(
                    id=f"sq_{i+1}",
                    question=sq_data["question"],
                    strategy=DecompositionStrategy(sq_data.get("strategy", "sequential")),
                    dependencies=sq_data.get("dependencies", []),
                    priority=sq_data.get("priority", i+1),
                    estimated_complexity=sq_data.get("estimated_complexity", 1.0)
                )
                sub_queries.append(sub_query)
            
            # Sort by priority
            sub_queries.sort(key=lambda x: x.priority)
            
            return DecompositionResult(
                original_query=query,
                sub_queries=sub_queries,
                execution_strategy=DecompositionStrategy(result_data.get("execution_strategy", "sequential")),
                estimated_total_time=sum(sq.estimated_complexity for sq in sub_queries) * 2.0,
                requires_synthesis=result_data.get("requires_synthesis", True)
            )
            
        except Exception as e:
            self.logger.error(f"Query decomposition failed: {e}")
            # Fallback: no decomposition
            return DecompositionResult(
                original_query=query,
                sub_queries=[],
                execution_strategy=DecompositionStrategy.SEQUENTIAL,
                estimated_total_time=1.0,
                requires_synthesis=False
            )

    def optimize_execution_order(self, sub_queries: List[SubQuery]) -> List[List[SubQuery]]:
        """Optimize execution order considering dependencies."""
        
        # Group by execution batches considering dependencies
        batches = []
        remaining = sub_queries.copy()
        
        while remaining:
            # Find queries with no unresolved dependencies
            current_batch = []
            resolved_ids = set()
            
            # Add IDs from previous batches
            for batch in batches:
                resolved_ids.update(sq.id for sq in batch)
            
            for sq in remaining[:]:
                if all(dep in resolved_ids for dep in sq.dependencies):
                    current_batch.append(sq)
                    remaining.remove(sq)
            
            if not current_batch:
                # Break circular dependencies by taking highest priority
                current_batch = [min(remaining, key=lambda x: x.priority)]
                remaining.remove(current_batch[0])
            
            batches.append(current_batch)
        
        return batches
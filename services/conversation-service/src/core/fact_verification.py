"""
Fact Verification System
Advanced fact-checking and hallucination detection for RAG responses
"""

from typing import Dict, List, Any, Optional, Tuple
import logging
import re
import asyncio
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import json

@dataclass
class FactClaim:
    """Individual factual claim extracted from text."""
    
    claim_id: str
    claim_text: str
    claim_type: str  # factual, numerical, temporal, procedural
    confidence: float
    source_context: str
    start_position: int
    end_position: int
    entities: List[str] = field(default_factory=list)
    
@dataclass
class VerificationResult:
    """Result of fact verification process."""
    
    claim: FactClaim
    verification_status: str  # verified, contradicted, uncertain, unsupported
    confidence_score: float
    supporting_sources: List[str]
    contradicting_sources: List[str]
    evidence_summary: str
    verification_method: str

@dataclass
class HallucinationDetectionResult:
    """Result of hallucination detection analysis."""
    
    text: str
    hallucination_risk: str  # low, medium, high
    risk_score: float  # 0.0 to 1.0
    detected_issues: List[Dict[str, Any]]
    confidence_indicators: Dict[str, float]
    recommendations: List[str]

class ClaimType(Enum):
    """Types of factual claims."""
    
    FACTUAL = "factual"          # General facts
    NUMERICAL = "numerical"      # Numbers, statistics
    TEMPORAL = "temporal"        # Dates, time periods
    PROCEDURAL = "procedural"    # Steps, processes
    TECHNICAL = "technical"      # Technical specifications
    COMPARATIVE = "comparative"  # Comparisons between entities

class VerificationStatus(Enum):
    """Verification status options."""
    
    VERIFIED = "verified"
    CONTRADICTED = "contradicted"
    UNCERTAIN = "uncertain"
    UNSUPPORTED = "unsupported"

class FactVerificationSystem:
    """Advanced fact verification and hallucination detection."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Verification settings
        self.verification_settings = {
            "min_confidence_threshold": 0.7,
            "max_claims_per_verification": 20,
            "source_agreement_threshold": 0.6,
            "hallucination_risk_threshold": 0.4,
            "enable_cross_verification": True,
            "require_multiple_sources": True
        }
        
        # Claim extraction patterns
        self.claim_patterns = self._initialize_claim_patterns()
        
        # Hallucination indicators
        self.hallucination_indicators = self._initialize_hallucination_indicators()
        
        # Entity recognizer
        self.entity_recognizer = EntityRecognizer()
        
        # Source validator
        self.source_validator = SourceValidator()

    def _initialize_claim_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for extracting different types of claims."""
        
        return {
            ClaimType.FACTUAL.value: [
                r'(.+) is (.+)',
                r'(.+) are (.+)',
                r'(.+) has (.+)',
                r'(.+) can (.+)',
                r'(.+) will (.+)',
                r'(.+) does (.+)'
            ],
            
            ClaimType.NUMERICAL.value: [
                r'(\d+(?:\.\d+)?)\s*(%|percent|dollars?|euros?|pounds?)',
                r'(\d+(?:,\d{3})*(?:\.\d+)?)\s*(users?|customers?|people|items?)',
                r'(approximately|about|around|over|under|more than|less than)\s*(\d+)',
                r'(\d+)\s*(times?|fold|percent|%)\s*(faster|slower|better|worse|more|less)'
            ],
            
            ClaimType.TEMPORAL.value: [
                r'(in|on|during|since|before|after)\s*(\d{4}|\w+\s+\d{1,2},?\s*\d{4})',
                r'(\d+)\s*(years?|months?|weeks?|days?|hours?|minutes?)\s*(ago|later|before|after)',
                r'(January|February|March|April|May|June|July|August|September|October|November|December)\s*\d{1,2},?\s*\d{4}'
            ],
            
            ClaimType.PROCEDURAL.value: [
                r'(first|then|next|finally|lastly),?\s*(.+)',
                r'step\s*\d+:?\s*(.+)',
                r'to\s+(.+),\s*(you\s+)?(must|should|need to|have to)\s*(.+)'
            ],
            
            ClaimType.TECHNICAL.value: [
                r'(API|HTTP|JSON|XML|SSL|TLS|OAuth|JWT)\s+(.+)',
                r'(version|v\.?)\s*(\d+(?:\.\d+)*)',
                r'(supports?|requires?|uses?|implements?)\s+(.+)',
                r'(compatible with|works with|integrates with)\s+(.+)'
            ],
            
            ClaimType.COMPARATIVE.value: [
                r'(.+)\s+(is|are)\s+(better|worse|faster|slower|more|less)\s+(than)\s+(.+)',
                r'(.+)\s+(vs\.?|versus|compared to)\s+(.+)',
                r'(unlike|similar to|different from)\s+(.+),\s*(.+)'
            ]
        }

    def _initialize_hallucination_indicators(self) -> Dict[str, Dict[str, Any]]:
        """Initialize indicators for hallucination detection."""
        
        return {
            "confidence_markers": {
                "high_confidence": ["definitely", "certainly", "absolutely", "guaranteed", "always"],
                "low_confidence": ["might", "could", "possibly", "perhaps", "maybe", "sometimes"],
                "hedging": ["generally", "typically", "usually", "often", "frequently"]
            },
            
            "specificity_indicators": {
                "overly_specific": [
                    r'\d+\.\d{3,}',  # Very precise numbers
                    r'exactly \d+',   # Exact claims
                    r'precisely \d+'  # Precise claims
                ],
                "vague_claims": [
                    "many", "several", "numerous", "various", "multiple",
                    "some", "few", "most", "majority", "minority"
                ]
            },
            
            "source_indicators": {
                "unsourced_claims": [
                    "it is known that", "studies show", "research indicates",
                    "experts say", "it has been proven", "according to sources"
                ],
                "authoritative_claims": [
                    "according to", "as stated in", "documented in",
                    "published by", "reported by", "confirmed by"
                ]
            },
            
            "temporal_inconsistencies": [
                r'(in|during|since)\s+(\d{4})\s+.+\s+(in|during|since)\s+(\d{4})',
                r'(before|after)\s+.+\s+(before|after)',
                r'(recently|lately|currently)\s+.+\s+(years? ago|decades? ago)'
            ]
        }

    async def verify_response_facts(self,
                                  response_text: str,
                                  source_documents: List[Dict[str, Any]],
                                  context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Verify factual claims in response against source documents."""
        
        try:
            # Extract factual claims from response
            claims = await self._extract_factual_claims(response_text)
            
            # Verify each claim against sources
            verification_results = []
            for claim in claims:
                result = await self._verify_single_claim(claim, source_documents, context)
                verification_results.append(result)
            
            # Analyze overall verification status
            overall_analysis = self._analyze_verification_results(verification_results)
            
            # Detect potential hallucinations
            hallucination_analysis = await self._detect_hallucinations(
                response_text, source_documents, verification_results
            )
            
            return {
                "total_claims": len(claims),
                "verified_claims": len([r for r in verification_results if r.verification_status == VerificationStatus.VERIFIED.value]),
                "contradicted_claims": len([r for r in verification_results if r.verification_status == VerificationStatus.CONTRADICTED.value]),
                "uncertain_claims": len([r for r in verification_results if r.verification_status == VerificationStatus.UNCERTAIN.value]),
                "unsupported_claims": len([r for r in verification_results if r.verification_status == VerificationStatus.UNSUPPORTED.value]),
                "verification_results": [self._serialize_verification_result(r) for r in verification_results],
                "overall_confidence": overall_analysis["confidence"],
                "factual_accuracy_score": overall_analysis["accuracy_score"],
                "hallucination_analysis": self._serialize_hallucination_result(hallucination_analysis),
                "recommendations": overall_analysis["recommendations"]
            }
            
        except Exception as e:
            self.logger.error(f"Fact verification failed: {e}")
            return self._get_fallback_verification_result(response_text)

    async def _extract_factual_claims(self, text: str) -> List[FactClaim]:
        """Extract factual claims from text."""
        
        claims = []
        claim_id_counter = 0
        
        # Split text into sentences
        sentences = self._split_into_sentences(text)
        
        for sentence in sentences:
            sentence_claims = await self._extract_claims_from_sentence(
                sentence, claim_id_counter
            )
            claims.extend(sentence_claims)
            claim_id_counter += len(sentence_claims)
        
        # Filter and deduplicate claims
        filtered_claims = self._filter_and_deduplicate_claims(claims)
        
        return filtered_claims[:self.verification_settings["max_claims_per_verification"]]

    async def _extract_claims_from_sentence(self, 
                                          sentence: Dict[str, Any], 
                                          start_id: int) -> List[FactClaim]:
        """Extract claims from a single sentence."""
        
        claims = []
        sentence_text = sentence["text"]
        
        for claim_type, patterns in self.claim_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, sentence_text, re.IGNORECASE)
                
                for match in matches:
                    # Extract entities from the claim
                    entities = await self.entity_recognizer.extract_entities(match.group(0))
                    
                    claim = FactClaim(
                        claim_id=f"claim_{start_id + len(claims)}",
                        claim_text=match.group(0),
                        claim_type=claim_type,
                        confidence=self._calculate_claim_confidence(match.group(0), claim_type),
                        source_context=sentence_text,
                        start_position=sentence["start"] + match.start(),
                        end_position=sentence["start"] + match.end(),
                        entities=entities
                    )
                    
                    claims.append(claim)
        
        return claims

    def _split_into_sentences(self, text: str) -> List[Dict[str, Any]]:
        """Split text into sentences with position tracking."""
        
        sentence_pattern = r'[.!?]+\s+'
        sentences = []
        
        current_pos = 0
        for match in re.finditer(sentence_pattern, text):
            sentence_text = text[current_pos:match.end()].strip()
            if sentence_text:
                sentences.append({
                    "text": sentence_text,
                    "start": current_pos,
                    "end": match.end()
                })
            current_pos = match.end()
        
        # Add final sentence if no ending punctuation
        if current_pos < len(text):
            final_text = text[current_pos:].strip()
            if final_text:
                sentences.append({
                    "text": final_text,
                    "start": current_pos,
                    "end": len(text)
                })
        
        return sentences

    def _calculate_claim_confidence(self, claim_text: str, claim_type: str) -> float:
        """Calculate confidence score for a claim."""
        
        confidence = 0.5  # Base confidence
        
        # Adjust based on claim type
        type_confidence = {
            ClaimType.FACTUAL.value: 0.7,
            ClaimType.NUMERICAL.value: 0.8,
            ClaimType.TEMPORAL.value: 0.9,
            ClaimType.PROCEDURAL.value: 0.6,
            ClaimType.TECHNICAL.value: 0.8,
            ClaimType.COMPARATIVE.value: 0.5
        }
        
        confidence = type_confidence.get(claim_type, 0.5)
        
        # Adjust based on confidence markers
        claim_lower = claim_text.lower()
        
        # High confidence markers increase score
        high_confidence_markers = self.hallucination_indicators["confidence_markers"]["high_confidence"]
        if any(marker in claim_lower for marker in high_confidence_markers):
            confidence = min(confidence + 0.2, 1.0)
        
        # Low confidence markers decrease score
        low_confidence_markers = self.hallucination_indicators["confidence_markers"]["low_confidence"]
        if any(marker in claim_lower for marker in low_confidence_markers):
            confidence = max(confidence - 0.2, 0.1)
        
        # Hedging markers slightly decrease score
        hedging_markers = self.hallucination_indicators["confidence_markers"]["hedging"]
        if any(marker in claim_lower for marker in hedging_markers):
            confidence = max(confidence - 0.1, 0.1)
        
        return confidence

    def _filter_and_deduplicate_claims(self, claims: List[FactClaim]) -> List[FactClaim]:
        """Filter and deduplicate extracted claims."""
        
        # Remove very short claims
        filtered_claims = [claim for claim in claims if len(claim.claim_text.split()) >= 3]
        
        # Remove duplicates based on text similarity
        unique_claims = []
        for claim in filtered_claims:
            is_duplicate = False
            for existing_claim in unique_claims:
                if self._calculate_text_similarity(claim.claim_text, existing_claim.claim_text) > 0.8:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_claims.append(claim)
        
        # Sort by confidence
        unique_claims.sort(key=lambda x: x.confidence, reverse=True)
        
        return unique_claims

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two text strings."""
        
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)

    async def _verify_single_claim(self,
                                 claim: FactClaim,
                                 source_documents: List[Dict[str, Any]],
                                 context: Optional[Dict[str, Any]]) -> VerificationResult:
        """Verify a single factual claim against source documents."""
        
        supporting_sources = []
        contradicting_sources = []
        evidence_pieces = []
        
        for source in source_documents:
            source_content = source.get("content", "")
            source_id = source.get("id", "unknown")
            
            # Check if source supports or contradicts the claim
            support_score = await self._calculate_claim_support(claim, source_content)
            
            if support_score > 0.7:
                supporting_sources.append(source_id)
                evidence_pieces.append({
                    "source_id": source_id,
                    "support_type": "supporting",
                    "confidence": support_score,
                    "evidence_text": self._extract_relevant_evidence(claim, source_content)
                })
            elif support_score < -0.7:
                contradicting_sources.append(source_id)
                evidence_pieces.append({
                    "source_id": source_id,
                    "support_type": "contradicting",
                    "confidence": abs(support_score),
                    "evidence_text": self._extract_relevant_evidence(claim, source_content)
                })
        
        # Determine verification status
        verification_status = self._determine_verification_status(
            supporting_sources, contradicting_sources, claim
        )
        
        # Calculate confidence score
        confidence_score = self._calculate_verification_confidence(
            supporting_sources, contradicting_sources, evidence_pieces
        )
        
        # Generate evidence summary
        evidence_summary = self._generate_evidence_summary(evidence_pieces)
        
        return VerificationResult(
            claim=claim,
            verification_status=verification_status,
            confidence_score=confidence_score,
            supporting_sources=supporting_sources,
            contradicting_sources=contradicting_sources,
            evidence_summary=evidence_summary,
            verification_method="source_comparison"
        )

    async def _calculate_claim_support(self, claim: FactClaim, source_content: str) -> float:
        """Calculate how much a source supports or contradicts a claim."""
        
        claim_text = claim.claim_text.lower()
        source_text = source_content.lower()
        
        # Simple keyword-based support calculation
        # In production, this could use more sophisticated NLP
        
        claim_words = set(claim_text.split())
        source_words = set(source_text.split())
        
        # Calculate word overlap
        overlap = claim_words.intersection(source_words)
        overlap_ratio = len(overlap) / len(claim_words) if claim_words else 0
        
        # Look for explicit contradictions
        contradiction_indicators = [
            "not", "never", "no", "false", "incorrect", "wrong",
            "contrary", "opposite", "different", "unlike"
        ]
        
        # Check for contradictions near claim keywords
        for word in claim_words:
            if word in source_text:
                # Look for contradiction indicators near this word
                word_context = self._get_word_context(source_text, word, window=10)
                if any(indicator in word_context for indicator in contradiction_indicators):
                    return -overlap_ratio  # Negative score for contradiction
        
        return overlap_ratio

    def _get_word_context(self, text: str, word: str, window: int = 10) -> str:
        """Get context around a word in text."""
        
        words = text.split()
        try:
            word_index = words.index(word)
            start = max(0, word_index - window)
            end = min(len(words), word_index + window + 1)
            return " ".join(words[start:end])
        except ValueError:
            return ""

    def _extract_relevant_evidence(self, claim: FactClaim, source_content: str) -> str:
        """Extract relevant evidence text from source."""
        
        # Find sentences in source that contain claim keywords
        claim_words = set(claim.claim_text.lower().split())
        source_sentences = source_content.split('.')
        
        relevant_sentences = []
        for sentence in source_sentences:
            sentence_words = set(sentence.lower().split())
            if len(claim_words.intersection(sentence_words)) >= 2:
                relevant_sentences.append(sentence.strip())
        
        return ". ".join(relevant_sentences[:2])  # Return up to 2 most relevant sentences

    def _determine_verification_status(self,
                                     supporting_sources: List[str],
                                     contradicting_sources: List[str],
                                     claim: FactClaim) -> str:
        """Determine verification status based on source analysis."""
        
        support_count = len(supporting_sources)
        contradiction_count = len(contradicting_sources)
        
        if support_count > 0 and contradiction_count == 0:
            if support_count >= 2 or not self.verification_settings["require_multiple_sources"]:
                return VerificationStatus.VERIFIED.value
            else:
                return VerificationStatus.UNCERTAIN.value
        
        elif contradiction_count > 0 and support_count == 0:
            return VerificationStatus.CONTRADICTED.value
        
        elif support_count > 0 and contradiction_count > 0:
            # Conflicting evidence
            if support_count > contradiction_count:
                return VerificationStatus.UNCERTAIN.value
            else:
                return VerificationStatus.CONTRADICTED.value
        
        else:
            # No supporting or contradicting evidence found
            return VerificationStatus.UNSUPPORTED.value

    def _calculate_verification_confidence(self,
                                         supporting_sources: List[str],
                                         contradicting_sources: List[str],
                                         evidence_pieces: List[Dict[str, Any]]) -> float:
        """Calculate confidence in verification result."""
        
        if not evidence_pieces:
            return 0.0
        
        # Calculate average evidence confidence
        avg_confidence = sum(piece["confidence"] for piece in evidence_pieces) / len(evidence_pieces)
        
        # Adjust based on source agreement
        total_sources = len(supporting_sources) + len(contradicting_sources)
        if total_sources > 0:
            agreement_ratio = max(len(supporting_sources), len(contradicting_sources)) / total_sources
            confidence = avg_confidence * agreement_ratio
        else:
            confidence = 0.0
        
        return min(confidence, 1.0)

    def _generate_evidence_summary(self, evidence_pieces: List[Dict[str, Any]]) -> str:
        """Generate summary of evidence for verification."""
        
        if not evidence_pieces:
            return "No evidence found in source documents."
        
        supporting_count = len([e for e in evidence_pieces if e["support_type"] == "supporting"])
        contradicting_count = len([e for e in evidence_pieces if e["support_type"] == "contradicting"])
        
        summary_parts = []
        
        if supporting_count > 0:
            summary_parts.append(f"{supporting_count} source(s) support this claim")
        
        if contradicting_count > 0:
            summary_parts.append(f"{contradicting_count} source(s) contradict this claim")
        
        # Add sample evidence
        if evidence_pieces:
            best_evidence = max(evidence_pieces, key=lambda x: x["confidence"])
            if best_evidence["evidence_text"]:
                summary_parts.append(f"Key evidence: {best_evidence['evidence_text'][:100]}...")
        
        return ". ".join(summary_parts)

    async def _detect_hallucinations(self,
                                   response_text: str,
                                   source_documents: List[Dict[str, Any]],
                                   verification_results: List[VerificationResult]) -> HallucinationDetectionResult:
        """Detect potential hallucinations in response."""
        
        detected_issues = []
        confidence_indicators = {}
        
        # Analyze confidence markers
        confidence_analysis = self._analyze_confidence_markers(response_text)
        confidence_indicators.update(confidence_analysis)
        
        # Check for overly specific claims
        specificity_issues = self._detect_overly_specific_claims(response_text)
        detected_issues.extend(specificity_issues)
        
        # Check for unsourced authoritative claims
        unsourced_issues = self._detect_unsourced_claims(response_text, source_documents)
        detected_issues.extend(unsourced_issues)
        
        # Analyze verification results
        verification_issues = self._analyze_verification_issues(verification_results)
        detected_issues.extend(verification_issues)
        
        # Calculate overall risk score
        risk_score = self._calculate_hallucination_risk_score(
            detected_issues, confidence_indicators, verification_results
        )
        
        # Determine risk level
        if risk_score >= 0.7:
            risk_level = "high"
        elif risk_score >= 0.4:
            risk_level = "medium"
        else:
            risk_level = "low"
        
        # Generate recommendations
        recommendations = self._generate_hallucination_recommendations(
            detected_issues, risk_level
        )
        
        return HallucinationDetectionResult(
            text=response_text,
            hallucination_risk=risk_level,
            risk_score=risk_score,
            detected_issues=detected_issues,
            confidence_indicators=confidence_indicators,
            recommendations=recommendations
        )

    def _analyze_confidence_markers(self, text: str) -> Dict[str, float]:
        """Analyze confidence markers in text."""
        
        text_lower = text.lower()
        indicators = self.hallucination_indicators["confidence_markers"]
        
        high_confidence_count = sum(1 for marker in indicators["high_confidence"] if marker in text_lower)
        low_confidence_count = sum(1 for marker in indicators["low_confidence"] if marker in text_lower)
        hedging_count = sum(1 for marker in indicators["hedging"] if marker in text_lower)
        
        total_words = len(text.split())
        
        return {
            "high_confidence_ratio": high_confidence_count / total_words if total_words > 0 else 0,
            "low_confidence_ratio": low_confidence_count / total_words if total_words > 0 else 0,
            "hedging_ratio": hedging_count / total_words if total_words > 0 else 0,
            "confidence_balance": (high_confidence_count - low_confidence_count) / max(total_words, 1)
        }

    def _detect_overly_specific_claims(self, text: str) -> List[Dict[str, Any]]:
        """Detect overly specific claims that might be hallucinated."""
        
        issues = []
        specificity_patterns = self.hallucination_indicators["specificity_indicators"]["overly_specific"]
        
        for pattern in specificity_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                issues.append({
                    "type": "overly_specific",
                    "text": match.group(0),
                    "position": match.start(),
                    "severity": "medium",
                    "description": "Overly specific claim that may not be verifiable"
                })
        
        return issues

    def _detect_unsourced_claims(self, text: str, source_documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect unsourced authoritative claims."""
        
        issues = []
        unsourced_patterns = self.hallucination_indicators["source_indicators"]["unsourced_claims"]
        
        text_lower = text.lower()
        for pattern in unsourced_patterns:
            if pattern in text_lower:
                issues.append({
                    "type": "unsourced_claim",
                    "text": pattern,
                    "position": text_lower.find(pattern),
                    "severity": "high",
                    "description": f"Authoritative claim '{pattern}' without clear source attribution"
                })
        
        return issues

    def _analyze_verification_issues(self, verification_results: List[VerificationResult]) -> List[Dict[str, Any]]:
        """Analyze verification results for potential issues."""
        
        issues = []
        
        for result in verification_results:
            if result.verification_status == VerificationStatus.CONTRADICTED.value:
                issues.append({
                    "type": "contradicted_claim",
                    "text": result.claim.claim_text,
                    "position": result.claim.start_position,
                    "severity": "high",
                    "description": f"Claim contradicted by {len(result.contradicting_sources)} source(s)"
                })
            
            elif result.verification_status == VerificationStatus.UNSUPPORTED.value:
                issues.append({
                    "type": "unsupported_claim",
                    "text": result.claim.claim_text,
                    "position": result.claim.start_position,
                    "severity": "medium",
                    "description": "Claim not supported by available sources"
                })
        
        return issues

    def _calculate_hallucination_risk_score(self,
                                          detected_issues: List[Dict[str, Any]],
                                          confidence_indicators: Dict[str, float],
                                          verification_results: List[VerificationResult]) -> float:
        """Calculate overall hallucination risk score."""
        
        risk_score = 0.0
        
        # Issue-based risk
        high_severity_issues = len([i for i in detected_issues if i["severity"] == "high"])
        medium_severity_issues = len([i for i in detected_issues if i["severity"] == "medium"])
        
        issue_risk = (high_severity_issues * 0.3 + medium_severity_issues * 0.1)
        risk_score += min(issue_risk, 0.5)
        
        # Confidence marker risk
        confidence_balance = confidence_indicators.get("confidence_balance", 0)
        if confidence_balance > 0.02:  # Too many high confidence markers
            risk_score += 0.2
        
        # Verification-based risk
        if verification_results:
            contradicted_ratio = len([r for r in verification_results if r.verification_status == VerificationStatus.CONTRADICTED.value]) / len(verification_results)
            unsupported_ratio = len([r for r in verification_results if r.verification_status == VerificationStatus.UNSUPPORTED.value]) / len(verification_results)
            
            verification_risk = contradicted_ratio * 0.4 + unsupported_ratio * 0.2
            risk_score += verification_risk
        
        return min(risk_score, 1.0)

    def _generate_hallucination_recommendations(self,
                                              detected_issues: List[Dict[str, Any]],
                                              risk_level: str) -> List[str]:
        """Generate recommendations based on hallucination analysis."""
        
        recommendations = []
        
        if risk_level == "high":
            recommendations.append("Consider regenerating response with stricter source adherence")
            recommendations.append("Review and verify all factual claims before presenting to user")
        
        if any(issue["type"] == "contradicted_claim" for issue in detected_issues):
            recommendations.append("Remove or correct claims that contradict source documents")
        
        if any(issue["type"] == "unsourced_claim" for issue in detected_issues):
            recommendations.append("Add proper source attribution for authoritative claims")
        
        if any(issue["type"] == "overly_specific" for issue in detected_issues):
            recommendations.append("Consider using more general language for unverifiable specific claims")
        
        if not recommendations:
            recommendations.append("Response appears factually sound based on available sources")
        
        return recommendations

    def _analyze_verification_results(self, verification_results: List[VerificationResult]) -> Dict[str, Any]:
        """Analyze overall verification results."""
        
        if not verification_results:
            return {
                "confidence": 0.0,
                "accuracy_score": 0.0,
                "recommendations": ["No factual claims detected for verification"]
            }
        
        verified_count = len([r for r in verification_results if r.verification_status == VerificationStatus.VERIFIED.value])
        total_count = len(verification_results)
        
        accuracy_score = verified_count / total_count
        
        avg_confidence = sum(r.confidence_score for r in verification_results) / total_count
        
        recommendations = []
        if accuracy_score < 0.7:
            recommendations.append("Consider revising response to improve factual accuracy")
        if avg_confidence < 0.6:
            recommendations.append("Add more authoritative sources to support claims")
        
        return {
            "confidence": avg_confidence,
            "accuracy_score": accuracy_score,
            "recommendations": recommendations
        }

    def _serialize_verification_result(self, result: VerificationResult) -> Dict[str, Any]:
        """Serialize verification result for JSON output."""
        
        return {
            "claim_id": result.claim.claim_id,
            "claim_text": result.claim.claim_text,
            "claim_type": result.claim.claim_type,
            "verification_status": result.verification_status,
            "confidence_score": result.confidence_score,
            "supporting_sources": result.supporting_sources,
            "contradicting_sources": result.contradicting_sources,
            "evidence_summary": result.evidence_summary,
            "verification_method": result.verification_method
        }

    def _serialize_hallucination_result(self, result: HallucinationDetectionResult) -> Dict[str, Any]:
        """Serialize hallucination detection result for JSON output."""
        
        return {
            "hallucination_risk": result.hallucination_risk,
            "risk_score": result.risk_score,
            "detected_issues": result.detected_issues,
            "confidence_indicators": result.confidence_indicators,
            "recommendations": result.recommendations
        }

    def _get_fallback_verification_result(self, response_text: str) -> Dict[str, Any]:
        """Get fallback verification result when main verification fails."""
        
        return {
            "total_claims": 0,
            "verified_claims": 0,
            "contradicted_claims": 0,
            "uncertain_claims": 0,
            "unsupported_claims": 0,
            "verification_results": [],
            "overall_confidence": 0.0,
            "factual_accuracy_score": 0.0,
            "hallucination_analysis": {
                "hallucination_risk": "unknown",
                "risk_score": 0.5,
                "detected_issues": [],
                "confidence_indicators": {},
                "recommendations": ["Verification system unavailable - manual review recommended"]
            },
            "recommendations": ["System error occurred during fact verification"]
        }

    async def quick_hallucination_check(self, text: str) -> Dict[str, Any]:
        """Quick hallucination check without full fact verification."""
        
        try:
            # Analyze confidence markers
            confidence_analysis = self._analyze_confidence_markers(text)
            
            # Check for overly specific claims
            specificity_issues = self._detect_overly_specific_claims(text)
            
            # Check for unsourced claims
            unsourced_issues = self._detect_unsourced_claims(text, [])
            
            all_issues = specificity_issues + unsourced_issues
            
            # Calculate quick risk score
            risk_score = len(all_issues) * 0.1 + confidence_analysis.get("confidence_balance", 0) * 2
            risk_score = min(risk_score, 1.0)
            
            risk_level = "high" if risk_score >= 0.7 else "medium" if risk_score >= 0.4 else "low"
            
            return {
                "hallucination_risk": risk_level,
                "risk_score": risk_score,
                "detected_issues": all_issues,
                "confidence_indicators": confidence_analysis,
                "quick_check": True
            }
            
        except Exception as e:
            self.logger.error(f"Quick hallucination check failed: {e}")
            return {
                "hallucination_risk": "unknown",
                "risk_score": 0.5,
                "detected_issues": [],
                "confidence_indicators": {},
                "quick_check": True,
                "error": str(e)
            }


class EntityRecognizer:
    """Extract entities from text for fact verification."""
    
    def __init__(self):
        self.entity_patterns = {
            "PERSON": [
                r'\b[A-Z][a-z]+ [A-Z][a-z]+\b',  # First Last
                r'\b(?:Dr|Mr|Ms|Mrs|Prof)\. [A-Z][a-z]+\b'  # Title Name
            ],
            "ORGANIZATION": [
                r'\b[A-Z][A-Z0-9&\s]+(?:Inc|Corp|LLC|Ltd)\b',
                r'\b(?:Google|Microsoft|Apple|Amazon|Facebook|Meta)\b'
            ],
            "DATE": [
                r'\b\d{1,2}/\d{1,2}/\d{4}\b',
                r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}\b'
            ],
            "NUMBER": [
                r'\b\d+(?:,\d{3})*(?:\.\d+)?\b',
                r'\b\d+(?:\.\d+)?%\b'
            ],
            "LOCATION": [
                r'\b[A-Z][a-z]+,\s+[A-Z]{2}\b',  # City, State
                r'\b(?:United States|USA|UK|Canada|Germany|France|Japan|China)\b'
            ]
        }
    
    async def extract_entities(self, text: str) -> List[str]:
        """Extract entities from text."""
        
        entities = []
        
        for entity_type, patterns in self.entity_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    entities.append(f"{entity_type}:{match}")
        
        return list(set(entities))  # Remove duplicates


class SourceValidator:
    """Validate source documents for fact verification."""
    
    def __init__(self):
        self.validation_criteria = {
            "min_content_length": 50,
            "required_fields": ["content", "id"],
            "max_age_days": 365,
            "trusted_domains": [
                "wikipedia.org", "gov", "edu", "nature.com",
                "science.org", "ieee.org", "acm.org"
            ]
        }
    
    def validate_sources(self, sources: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate and filter source documents."""
        
        valid_sources = []
        
        for source in sources:
            if self._is_valid_source(source):
                valid_sources.append(source)
        
        return valid_sources
    
    def _is_valid_source(self, source: Dict[str, Any]) -> bool:
        """Check if a source is valid for fact verification."""
        
        # Check required fields
        for field in self.validation_criteria["required_fields"]:
            if field not in source:
                return False
        
        # Check content length
        content = source.get("content", "")
        if len(content) < self.validation_criteria["min_content_length"]:
            return False
        
        # Check source age if timestamp available
        if "timestamp" in source:
            try:
                source_date = datetime.fromisoformat(source["timestamp"])
                age_days = (datetime.now() - source_date).days
                if age_days > self.validation_criteria["max_age_days"]:
                    return False
            except (ValueError, TypeError):
                pass  # Skip age check if timestamp is invalid
        
        return True
    
    def calculate_source_authority(self, source: Dict[str, Any]) -> float:
        """Calculate authority score for a source."""
        
        authority_score = 0.5  # Base score
        
        # Check domain authority
        url = source.get("url", "")
        for trusted_domain in self.validation_criteria["trusted_domains"]:
            if trusted_domain in url:
                authority_score += 0.3
                break
        
        # Check for academic indicators
        content = source.get("content", "").lower()
        academic_indicators = ["doi:", "pmid:", "isbn:", "peer-reviewed", "journal"]
        if any(indicator in content for indicator in academic_indicators):
            authority_score += 0.2
        
        # Check content quality indicators
        if len(content) > 1000:  # Substantial content
            authority_score += 0.1
        
        if source.get("citations", 0) > 0:  # Has citations
            authority_score += 0.1
        
        return min(authority_score, 1.0)

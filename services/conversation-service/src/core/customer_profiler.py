"""
Customer Profiler for RAG Pipeline

This module provides sophisticated customer profiling capabilities including:
- Dynamic user profile building
- Behavioral pattern analysis
- Preference learning and adaptation
- Expertise level assessment
"""

from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import json
import logging
from collections import defaultdict, Counter

from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate

logger = logging.getLogger(__name__)


class ExpertiseLevel(Enum):
    """User expertise levels."""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate" 
    ADVANCED = "advanced"
    EXPERT = "expert"


class CommunicationStyle(Enum):
    """Preferred communication styles."""
    CONCISE = "concise"
    DETAILED = "detailed"
    TECHNICAL = "technical"
    CONVERSATIONAL = "conversational"
    VISUAL = "visual"


class LearningStyle(Enum):
    """User learning preferences."""
    VISUAL = "visual"
    AUDITORY = "auditory"
    KINESTHETIC = "kinesthetic"
    READING_WRITING = "reading_writing"
    MIXED = "mixed"


@dataclass
class InteractionPattern:
    """Tracks user interaction patterns."""
    query_complexity_history: List[str] = field(default_factory=list)
    response_length_preferences: List[int] = field(default_factory=list)
    topic_frequency: Dict[str, int] = field(default_factory=dict)
    time_patterns: Dict[str, int] = field(default_factory=dict)
    feedback_scores: List[float] = field(default_factory=list)
    follow_up_patterns: List[bool] = field(default_factory=list)


@dataclass
class CustomerProfile:
    """Comprehensive customer profile."""
    customer_id: str
    name: Optional[str] = None
    expertise_level: ExpertiseLevel = ExpertiseLevel.INTERMEDIATE
    communication_style: CommunicationStyle = CommunicationStyle.DETAILED
    learning_style: LearningStyle = LearningStyle.MIXED
    
    # Preferences
    preferred_response_length: str = "medium"  # short, medium, long
    preferred_examples: bool = True
    preferred_code_style: Optional[str] = None
    language_preference: str = "en"
    
    # Behavioral patterns
    interaction_patterns: InteractionPattern = field(default_factory=InteractionPattern)
    primary_interests: List[str] = field(default_factory=list)
    domain_expertise: Dict[str, float] = field(default_factory=dict)
    
    # Context preferences
    context_depth_preference: float = 0.7
    historical_context_importance: float = 0.5
    example_preference_score: float = 0.8
    
    # Session info
    created_at: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)
    total_interactions: int = 0
    satisfaction_score: float = 0.7
    
    # Metadata
    metadata: Dict[str, Any] = field(default_factory=dict)


class CustomerProfiler:
    """Advanced customer profiling system."""
    
    def __init__(self, model: str = "gpt-4o-mini"):
        self.profiles: Dict[str, CustomerProfile] = {}
        self.llm = ChatOpenAI(model=model, temperature=0.1)
        
        # Profile analysis prompts
        self.expertise_analyzer = self._create_expertise_analyzer()
        self.style_analyzer = self._create_style_analyzer()
        self.preference_analyzer = self._create_preference_analyzer()
        self.pattern_analyzer = self._create_pattern_analyzer()
        
        # Thresholds for profile updates
        self.min_interactions_for_analysis = 3
        self.profile_update_threshold = 5
        
    def _create_expertise_analyzer(self) -> ChatPromptTemplate:
        """Create expertise level analysis prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """Analyze the user's expertise level based on their queries and interactions.
            
Consider:
- Technical vocabulary used
- Complexity of questions asked
- Depth of follow-up questions
- Understanding shown in responses

Return a JSON object:
{
    "expertise_level": "beginner|intermediate|advanced|expert",
    "confidence": 0.8,
    "indicators": ["specific evidence for the assessment"],
    "domain_expertise": {"domain1": 0.7, "domain2": 0.9},
    "reasoning": "explanation of assessment"
}"""),
            ("human", """User Query History:
{query_history}

User Feedback History:
{feedback_history}

Response Patterns:
{response_patterns}

Analyze expertise level:""")
        ])
    
    def _create_style_analyzer(self) -> ChatPromptTemplate:
        """Create communication style analysis prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """Analyze the user's preferred communication style based on their interactions.

Consider:
- Length and detail of questions
- Response to different answer formats
- Preference for examples vs. theory
- Technical vs. conversational language

Return a JSON object:
{
    "communication_style": "concise|detailed|technical|conversational|visual",
    "response_length_preference": "short|medium|long",
    "example_preference": true,
    "technical_depth_preference": 0.7,
    "confidence": 0.8,
    "reasoning": "explanation of assessment"
}"""),
            ("human", """User Queries:
{user_queries}

Response Engagement Patterns:
{engagement_patterns}

Feedback on Different Response Types:
{response_feedback}

Analyze communication style:""")
        ])
    
    def _create_preference_analyzer(self) -> ChatPromptTemplate:
        """Create user preference analysis prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """Analyze user preferences for content and interaction style.

Consider:
- Topics they engage with most
- Time patterns of interactions
- Feedback on different content types
- Learning preferences shown

Return a JSON object:
{
    "primary_interests": ["topic1", "topic2"],
    "learning_style": "visual|auditory|kinesthetic|reading_writing|mixed",
    "context_preferences": {
        "depth": 0.7,
        "historical_context": 0.5,
        "examples": 0.8
    },
    "time_preferences": ["morning", "afternoon"],
    "confidence": 0.8
}"""),
            ("human", """User Interaction History:
{interaction_history}

Topic Engagement Scores:
{topic_scores}

Time Patterns:
{time_patterns}

Analyze preferences:""")
        ])
    
    def _create_pattern_analyzer(self) -> ChatPromptTemplate:
        """Create behavioral pattern analysis prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """Analyze user behavioral patterns and predict future needs.

Look for:
- Query progression patterns
- Follow-up question tendencies
- Session length preferences
- Problem-solving approaches

Return a JSON object:
{
    "query_patterns": ["pattern1", "pattern2"],
    "follow_up_tendency": 0.7,
    "session_length_preference": "short|medium|long",
    "problem_solving_style": "systematic|exploratory|direct",
    "predicted_needs": ["need1", "need2"],
    "confidence": 0.8
}"""),
            ("human", """Behavioral Data:
{behavioral_data}

Session Patterns:
{session_patterns}

Query Progression:
{query_progression}

Analyze patterns:""")
        ])
    
    def get_or_create_profile(self, customer_id: str, initial_data: Optional[Dict[str, Any]] = None) -> CustomerProfile:
        """Get existing profile or create new one."""
        if customer_id not in self.profiles:
            profile = CustomerProfile(customer_id=customer_id)
            
            # Apply initial data if provided
            if initial_data:
                if "name" in initial_data:
                    profile.name = initial_data["name"]
                if "language" in initial_data:
                    profile.language_preference = initial_data["language"]
                if "expertise_hints" in initial_data:
                    profile.metadata["initial_expertise_hints"] = initial_data["expertise_hints"]
            
            self.profiles[customer_id] = profile
            logger.info(f"Created new customer profile for {customer_id}")
        
        return self.profiles[customer_id]
    
    def update_profile_from_interaction(
        self, 
        customer_id: str, 
        query: str, 
        response_quality: float,
        query_complexity: str,
        topics: List[str],
        user_feedback: Optional[float] = None,
        response_length: Optional[int] = None
    ) -> None:
        """Update profile based on single interaction."""
        profile = self.get_or_create_profile(customer_id)
        
        # Update interaction count
        profile.total_interactions += 1
        profile.last_updated = datetime.now()
        
        # Update interaction patterns
        patterns = profile.interaction_patterns
        patterns.query_complexity_history.append(query_complexity)
        
        if response_length:
            patterns.response_length_preferences.append(response_length)
        
        # Update topic frequency
        for topic in topics:
            patterns.topic_frequency[topic] = patterns.topic_frequency.get(topic, 0) + 1
        
        # Update time patterns
        current_hour = datetime.now().hour
        time_period = self._get_time_period(current_hour)
        patterns.time_patterns[time_period] = patterns.time_patterns.get(time_period, 0) + 1
        
        # Update feedback
        if user_feedback is not None:
            patterns.feedback_scores.append(user_feedback)
            # Update satisfaction score (weighted average)
            if patterns.feedback_scores:
                profile.satisfaction_score = sum(patterns.feedback_scores) / len(patterns.feedback_scores)
        
        # Update primary interests based on frequency
        if patterns.topic_frequency:
            sorted_topics = sorted(patterns.topic_frequency.items(), key=lambda x: x[1], reverse=True)
            profile.primary_interests = [topic for topic, _ in sorted_topics[:5]]
        
        # Trigger comprehensive analysis if threshold reached
        if profile.total_interactions % self.profile_update_threshold == 0:
            asyncio.create_task(self._comprehensive_profile_analysis(customer_id))
    
    async def _comprehensive_profile_analysis(self, customer_id: str) -> None:
        """Perform comprehensive profile analysis using LLM."""
        profile = self.profiles[customer_id]
        patterns = profile.interaction_patterns
        
        if profile.total_interactions < self.min_interactions_for_analysis:
            return
        
        try:
            # Prepare data for analysis
            query_history = patterns.query_complexity_history[-10:]
            feedback_history = patterns.feedback_scores[-10:]
            response_patterns = {
                "avg_response_length": sum(patterns.response_length_preferences[-10:]) / len(patterns.response_length_preferences[-10:]) if patterns.response_length_preferences else 0,
                "follow_up_rate": sum(patterns.follow_up_patterns[-10:]) / len(patterns.follow_up_patterns[-10:]) if patterns.follow_up_patterns else 0
            }
            
            # Analyze expertise level
            expertise_result = await self._analyze_expertise(query_history, feedback_history, response_patterns)
            if expertise_result:
                profile.expertise_level = ExpertiseLevel(expertise_result["expertise_level"])
                profile.domain_expertise.update(expertise_result.get("domain_expertise", {}))
            
            # Analyze communication style
            style_result = await self._analyze_communication_style(profile)
            if style_result:
                profile.communication_style = CommunicationStyle(style_result["communication_style"])
                profile.preferred_response_length = style_result["response_length_preference"]
                profile.preferred_examples = style_result["example_preference"]
            
            # Analyze preferences
            preference_result = await self._analyze_preferences(profile)
            if preference_result:
                profile.learning_style = LearningStyle(preference_result["learning_style"])
                context_prefs = preference_result.get("context_preferences", {})
                profile.context_depth_preference = context_prefs.get("depth", profile.context_depth_preference)
                profile.historical_context_importance = context_prefs.get("historical_context", profile.historical_context_importance)
                profile.example_preference_score = context_prefs.get("examples", profile.example_preference_score)
            
            logger.info(f"Updated comprehensive profile for customer {customer_id}")
            
        except Exception as e:
            logger.error(f"Error in comprehensive profile analysis for {customer_id}: {e}")
    
    async def _analyze_expertise(self, query_history: List[str], feedback_history: List[float], response_patterns: Dict) -> Optional[Dict]:
        """Analyze user expertise level."""
        try:
            response = await self.expertise_analyzer.ainvoke({
                "query_history": json.dumps(query_history),
                "feedback_history": json.dumps(feedback_history),
                "response_patterns": json.dumps(response_patterns)
            })
            return json.loads(response.content)
        except Exception as e:
            logger.error(f"Error analyzing expertise: {e}")
            return None
    
    async def _analyze_communication_style(self, profile: CustomerProfile) -> Optional[Dict]:
        """Analyze communication style preferences."""
        try:
            patterns = profile.interaction_patterns
            user_queries = patterns.query_complexity_history[-10:]
            engagement_patterns = {
                "avg_feedback": sum(patterns.feedback_scores) / len(patterns.feedback_scores) if patterns.feedback_scores else 0,
                "interaction_frequency": profile.total_interactions / max(1, (datetime.now() - profile.created_at).days)
            }
            response_feedback = patterns.feedback_scores[-5:]
            
            response = await self.style_analyzer.ainvoke({
                "user_queries": json.dumps(user_queries),
                "engagement_patterns": json.dumps(engagement_patterns),
                "response_feedback": json.dumps(response_feedback)
            })
            return json.loads(response.content)
        except Exception as e:
            logger.error(f"Error analyzing communication style: {e}")
            return None
    
    async def _analyze_preferences(self, profile: CustomerProfile) -> Optional[Dict]:
        """Analyze user preferences."""
        try:
            patterns = profile.interaction_patterns
            interaction_history = {
                "total_interactions": profile.total_interactions,
                "satisfaction_score": profile.satisfaction_score,
                "time_patterns": patterns.time_patterns
            }
            
            response = await self.preference_analyzer.ainvoke({
                "interaction_history": json.dumps(interaction_history),
                "topic_scores": json.dumps(patterns.topic_frequency),
                "time_patterns": json.dumps(patterns.time_patterns)
            })
            return json.loads(response.content)
        except Exception as e:
            logger.error(f"Error analyzing preferences: {e}")
            return None
    
    def _get_time_period(self, hour: int) -> str:
        """Get time period from hour."""
        if 6 <= hour < 12:
            return "morning"
        elif 12 <= hour < 18:
            return "afternoon"
        elif 18 <= hour < 22:
            return "evening"
        else:
            return "night"
    
    def get_context_preferences(self, customer_id: str) -> Dict[str, Any]:
        """Get context preferences for customer."""
        profile = self.get_or_create_profile(customer_id)
        
        return {
            "expertise_level": profile.expertise_level.value,
            "communication_style": profile.communication_style.value,
            "response_length": profile.preferred_response_length,
            "include_examples": profile.preferred_examples,
            "context_depth": profile.context_depth_preference,
            "historical_context_weight": profile.historical_context_importance,
            "primary_interests": profile.primary_interests,
            "domain_expertise": profile.domain_expertise,
            "learning_style": profile.learning_style.value
        }
    
    def get_personalization_hints(self, customer_id: str) -> Dict[str, Any]:
        """Get personalization hints for response generation."""
        profile = self.get_or_create_profile(customer_id)
        patterns = profile.interaction_patterns
        
        # Calculate trends
        recent_complexity = patterns.query_complexity_history[-5:] if patterns.query_complexity_history else []
        complexity_trend = self._analyze_trend(recent_complexity)
        
        recent_feedback = patterns.feedback_scores[-5:] if patterns.feedback_scores else []
        satisfaction_trend = self._analyze_numeric_trend(recent_feedback)
        
        return {
            "profile_maturity": min(profile.total_interactions / 20.0, 1.0),
            "satisfaction_trend": satisfaction_trend,
            "complexity_trend": complexity_trend,
            "preferred_topics": profile.primary_interests[:3],
            "expertise_domains": profile.domain_expertise,
            "response_style_hints": {
                "length": profile.preferred_response_length,
                "technical_level": profile.expertise_level.value,
                "include_examples": profile.preferred_examples,
                "communication_style": profile.communication_style.value
            },
            "context_adaptation": {
                "depth_preference": profile.context_depth_preference,
                "historical_importance": profile.historical_context_importance,
                "example_importance": profile.example_preference_score
            }
        }
    
    def _analyze_trend(self, values: List[str]) -> str:
        """Analyze trend in categorical values."""
        if len(values) < 3:
            return "stable"
        
        complexity_scores = {"simple": 1, "moderate": 2, "complex": 3, "advanced": 4}
        numeric_values = [complexity_scores.get(v, 2) for v in values]
        
        return self._analyze_numeric_trend(numeric_values)
    
    def _analyze_numeric_trend(self, values: List[float]) -> str:
        """Analyze trend in numeric values."""
        if len(values) < 3:
            return "stable"
        
        # Simple trend analysis
        first_half = sum(values[:len(values)//2]) / (len(values)//2)
        second_half = sum(values[len(values)//2:]) / (len(values) - len(values)//2)
        
        if second_half > first_half * 1.1:
            return "increasing"
        elif second_half < first_half * 0.9:
            return "decreasing"
        else:
            return "stable"
    
    def update_profile_from_feedback(self, customer_id: str, feedback_score: float, feedback_text: Optional[str] = None) -> None:
        """Update profile based on explicit user feedback."""
        profile = self.get_or_create_profile(customer_id)
        patterns = profile.interaction_patterns
        
        patterns.feedback_scores.append(feedback_score)
        
        # Update satisfaction score
        if patterns.feedback_scores:
            profile.satisfaction_score = sum(patterns.feedback_scores) / len(patterns.feedback_scores)
        
        # Analyze feedback text for insights
        if feedback_text and feedback_score < 0.6:
            self._analyze_negative_feedback(profile, feedback_text, feedback_score)
        
        profile.last_updated = datetime.now()
    
    def _analyze_negative_feedback(self, profile: CustomerProfile, feedback_text: str, score: float) -> None:
        """Analyze negative feedback for profile adjustments."""
        feedback_lower = feedback_text.lower()
        
        # Adjust preferences based on common feedback patterns
        if any(word in feedback_lower for word in ["too long", "verbose", "wordy"]):
            if profile.preferred_response_length == "long":
                profile.preferred_response_length = "medium"
            elif profile.preferred_response_length == "medium":
                profile.preferred_response_length = "short"
        
        elif any(word in feedback_lower for word in ["too short", "brief", "more detail"]):
            if profile.preferred_response_length == "short":
                profile.preferred_response_length = "medium"
            elif profile.preferred_response_length == "medium":
                profile.preferred_response_length = "long"
        
        elif any(word in feedback_lower for word in ["too technical", "complex", "jargon"]):
            if profile.expertise_level == ExpertiseLevel.ADVANCED:
                profile.expertise_level = ExpertiseLevel.INTERMEDIATE
            elif profile.expertise_level == ExpertiseLevel.EXPERT:
                profile.expertise_level = ExpertiseLevel.ADVANCED
        
        elif any(word in feedback_lower for word in ["too simple", "basic", "more advanced"]):
            if profile.expertise_level == ExpertiseLevel.BEGINNER:
                profile.expertise_level = ExpertiseLevel.INTERMEDIATE
            elif profile.expertise_level == ExpertiseLevel.INTERMEDIATE:
                profile.expertise_level = ExpertiseLevel.ADVANCED
    
    def get_profile_summary(self, customer_id: str) -> Dict[str, Any]:
        """Get summary of customer profile."""
        if customer_id not in self.profiles:
            return {"error": "Profile not found"}
        
        profile = self.profiles[customer_id]
        patterns = profile.interaction_patterns
        
        return {
            "customer_id": customer_id,
            "name": profile.name,
            "expertise_level": profile.expertise_level.value,
            "communication_style": profile.communication_style.value,
            "learning_style": profile.learning_style.value,
            "total_interactions": profile.total_interactions,
            "satisfaction_score": round(profile.satisfaction_score, 2),
            "primary_interests": profile.primary_interests,
            "domain_expertise": profile.domain_expertise,
            "preferred_response_length": profile.preferred_response_length,
            "created_at": profile.created_at.isoformat(),
            "last_updated": profile.last_updated.isoformat(),
            "profile_insights": {
                "most_active_time": max(patterns.time_patterns.items(), key=lambda x: x[1])[0] if patterns.time_patterns else "unknown",
                "avg_feedback_score": round(sum(patterns.feedback_scores) / len(patterns.feedback_scores), 2) if patterns.feedback_scores else None,
                "interaction_consistency": self._calculate_consistency_score(patterns)
            }
        }
    
    def _calculate_consistency_score(self, patterns: InteractionPattern) -> float:
        """Calculate how consistent the user's interaction patterns are."""
        if patterns.feedback_scores:
            variance = sum((score - sum(patterns.feedback_scores) / len(patterns.feedback_scores))**2 for score in patterns.feedback_scores) / len(patterns.feedback_scores)
            return max(0, 1 - variance)
        return 0.5
    
    def export_profile(self, customer_id: str) -> Dict[str, Any]:
        """Export complete profile data."""
        if customer_id not in self.profiles:
            return {"error": "Profile not found"}
        
        profile = self.profiles[customer_id]
        
        return {
            "customer_id": profile.customer_id,
            "name": profile.name,
            "expertise_level": profile.expertise_level.value,
            "communication_style": profile.communication_style.value,
            "learning_style": profile.learning_style.value,
            "preferred_response_length": profile.preferred_response_length,
            "preferred_examples": profile.preferred_examples,
            "language_preference": profile.language_preference,
            "context_depth_preference": profile.context_depth_preference,
            "historical_context_importance": profile.historical_context_importance,
            "example_preference_score": profile.example_preference_score,
            "primary_interests": profile.primary_interests,
            "domain_expertise": profile.domain_expertise,
            "total_interactions": profile.total_interactions,
            "satisfaction_score": profile.satisfaction_score,
            "created_at": profile.created_at.isoformat(),
            "last_updated": profile.last_updated.isoformat(),
            "interaction_patterns": {
                "query_complexity_history": profile.interaction_patterns.query_complexity_history,
                "topic_frequency": profile.interaction_patterns.topic_frequency,
                "time_patterns": profile.interaction_patterns.time_patterns,
                "feedback_scores": profile.interaction_patterns.feedback_scores,
                "response_length_preferences": profile.interaction_patterns.response_length_preferences
            },
            "metadata": profile.metadata
        }
    
    def import_profile(self, profile_data: Dict[str, Any]) -> bool:
        """Import profile data."""
        try:
            customer_id = profile_data["customer_id"]
            profile = CustomerProfile(
                customer_id=customer_id,
                name=profile_data.get("name"),
                expertise_level=ExpertiseLevel(profile_data["expertise_level"]),
                communication_style=CommunicationStyle(profile_data["communication_style"]),
                learning_style=LearningStyle(profile_data["learning_style"]),
                preferred_response_length=profile_data["preferred_response_length"],
                preferred_examples=profile_data["preferred_examples"],
                language_preference=profile_data["language_preference"],
                context_depth_preference=profile_data["context_depth_preference"],
                historical_context_importance=profile_data["historical_context_importance"],
                example_preference_score=profile_data["example_preference_score"],
                primary_interests=profile_data["primary_interests"],
                domain_expertise=profile_data["domain_expertise"],
                total_interactions=profile_data["total_interactions"],
                satisfaction_score=profile_data["satisfaction_score"],
                created_at=datetime.fromisoformat(profile_data["created_at"]),
                last_updated=datetime.fromisoformat(profile_data["last_updated"]),
                metadata=profile_data.get("metadata", {})
            )
            
            # Import interaction patterns
            patterns_data = profile_data.get("interaction_patterns", {})
            profile.interaction_patterns = InteractionPattern(
                query_complexity_history=patterns_data.get("query_complexity_history", []),
                response_length_preferences=patterns_data.get("response_length_preferences", []),
                topic_frequency=patterns_data.get("topic_frequency", {}),
                time_patterns=patterns_data.get("time_patterns", {}),
                feedback_scores=patterns_data.get("feedback_scores", [])
            )
            
            self.profiles[customer_id] = profile
            return True
            
        except Exception as e:
            logger.error(f"Error importing profile: {e}")
            return False


# Import asyncio at the top for the async task creation
import asyncio
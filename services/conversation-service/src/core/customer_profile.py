"""
Customer Profile and Context Management System
Manages customer profiles, preferences, and interaction history for personalized responses.
"""

import logging
from typing import List, Dict, Any, Optional, Set
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import json
import asyncio
from collections import defaultdict

class CustomerTier(Enum):
    BASIC = "basic"
    PREMIUM = "premium"
    ENTERPRISE = "enterprise"
    VIP = "vip"

class PreferredTone(Enum):
    FORMAL = "formal"
    CASUAL = "casual"
    TECHNICAL = "technical"
    EMPATHETIC = "empathetic"
    CONCISE = "concise"

class ExpertiseLevel(Enum):
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"

@dataclass
class CustomerPreferences:
    """Customer communication and content preferences."""
    preferred_tone: PreferredTone = PreferredTone.FORMAL
    expertise_level: ExpertiseLevel = ExpertiseLevel.INTERMEDIATE
    preferred_response_length: str = "medium"  # short, medium, long
    include_code_examples: bool = True
    include_step_by_step: bool = True
    preferred_language: str = "en"
    timezone: str = "UTC"
    notification_preferences: Dict[str, bool] = field(default_factory=lambda: {
        "email_summaries": True,
        "follow_up_reminders": False,
        "product_updates": True
    })

@dataclass
class InteractionHistory:
    """Customer interaction history and patterns."""
    total_conversations: int = 0
    successful_resolutions: int = 0
    escalations_to_human: int = 0
    average_satisfaction_score: float = 0.0
    common_topics: List[str] = field(default_factory=list)
    recent_queries: List[Dict[str, Any]] = field(default_factory=list)
    last_interaction: Optional[datetime] = None
    interaction_patterns: Dict[str, Any] = field(default_factory=dict)

@dataclass
class CustomerProfile:
    """Comprehensive customer profile."""
    customer_id: str
    tier: CustomerTier
    preferences: CustomerPreferences
    interaction_history: InteractionHistory
    product_context: Dict[str, Any] = field(default_factory=dict)
    custom_attributes: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)

class CustomerProfileManager:
    """Manages customer profiles and context."""
    
    def __init__(self, config, database_manager=None, cache_manager=None):
        self.config = config
        self.database = database_manager
        self.cache = cache_manager
        self.logger = logging.getLogger(__name__)
        
        # Profile cache settings
        self.profile_cache_ttl = getattr(config, 'profile_cache_ttl', 3600)  # 1 hour
        self.interaction_history_limit = getattr(config, 'interaction_history_limit', 50)
        
        # In-memory cache for active profiles
        self.active_profiles = {}
        self.profile_locks = defaultdict(asyncio.Lock)

    async def get_customer_profile(self, customer_id: str) -> CustomerProfile:
        """Get or create customer profile."""
        
        async with self.profile_locks[customer_id]:
            try:
                # Check in-memory cache first
                if customer_id in self.active_profiles:
                    profile = self.active_profiles[customer_id]
                    if (datetime.utcnow() - profile.updated_at).seconds < 300:  # 5 min freshness
                        return profile
                
                # Check Redis cache
                if self.cache:
                    cached_profile = await self.cache.get(f"profile:{customer_id}")
                    if cached_profile:
                        profile_data = json.loads(cached_profile)
                        profile = self._deserialize_profile(profile_data)
                        self.active_profiles[customer_id] = profile
                        return profile
                
                # Load from database
                if self.database:
                    profile = await self._load_profile_from_db(customer_id)
                else:
                    # Create default profile
                    profile = self._create_default_profile(customer_id)
                
                # Cache the profile
                await self._cache_profile(profile)
                self.active_profiles[customer_id] = profile
                
                return profile
                
            except Exception as e:
                self.logger.error(f"Failed to get customer profile for {customer_id}: {e}")
                return self._create_default_profile(customer_id)

    async def update_customer_profile(
        self, 
        customer_id: str, 
        updates: Dict[str, Any]
    ) -> CustomerProfile:
        """Update customer profile with new information."""
        
        async with self.profile_locks[customer_id]:
            try:
                profile = await self.get_customer_profile(customer_id)
                
                # Apply updates
                for key, value in updates.items():
                    if hasattr(profile, key):
                        setattr(profile, key, value)
                    elif hasattr(profile.preferences, key):
                        setattr(profile.preferences, key, value)
                    elif hasattr(profile.interaction_history, key):
                        setattr(profile.interaction_history, key, value)
                    else:
                        profile.custom_attributes[key] = value
                
                profile.updated_at = datetime.utcnow()
                
                # Save to database and cache
                await self._save_profile(profile)
                await self._cache_profile(profile)
                self.active_profiles[customer_id] = profile
                
                return profile
                
            except Exception as e:
                self.logger.error(f"Failed to update customer profile for {customer_id}: {e}")
                return await self.get_customer_profile(customer_id)

    async def record_interaction(
        self,
        customer_id: str,
        query: str,
        response: str,
        satisfaction_score: Optional[float] = None,
        resolution_status: str = "resolved",
        topics: List[str] = None
    ):
        """Record a customer interaction."""
        
        try:
            profile = await self.get_customer_profile(customer_id)
            
            # Update interaction history
            interaction_record = {
                "timestamp": datetime.utcnow().isoformat(),
                "query": query[:500],  # Truncate for storage
                "response_length": len(response),
                "satisfaction_score": satisfaction_score,
                "resolution_status": resolution_status,
                "topics": topics or []
            }
            
            # Add to recent queries (keep limited history)
            profile.interaction_history.recent_queries.append(interaction_record)
            if len(profile.interaction_history.recent_queries) > self.interaction_history_limit:
                profile.interaction_history.recent_queries = profile.interaction_history.recent_queries[-self.interaction_history_limit:]
            
            # Update statistics
            profile.interaction_history.total_conversations += 1
            profile.interaction_history.last_interaction = datetime.utcnow()
            
            if resolution_status == "resolved":
                profile.interaction_history.successful_resolutions += 1
            elif resolution_status == "escalated":
                profile.interaction_history.escalations_to_human += 1
            
            # Update satisfaction score
            if satisfaction_score is not None:
                current_avg = profile.interaction_history.average_satisfaction_score
                total_conversations = profile.interaction_history.total_conversations
                new_avg = ((current_avg * (total_conversations - 1)) + satisfaction_score) / total_conversations
                profile.interaction_history.average_satisfaction_score = new_avg
            
            # Update common topics
            if topics:
                topic_counts = defaultdict(int)
                for topic in profile.interaction_history.common_topics:
                    topic_counts[topic] += 1
                for topic in topics:
                    topic_counts[topic] += 1
                
                # Keep top 10 most common topics
                sorted_topics = sorted(topic_counts.items(), key=lambda x: x[1], reverse=True)
                profile.interaction_history.common_topics = [topic for topic, count in sorted_topics[:10]]
            
            # Save updated profile
            await self.update_customer_profile(customer_id, {})
            
        except Exception as e:
            self.logger.error(f"Failed to record interaction for {customer_id}: {e}")

    async def get_contextual_preferences(
        self, 
        customer_id: str, 
        query_type: str = None,
        current_topic: str = None
    ) -> Dict[str, Any]:
        """Get contextual preferences for response generation."""
        
        try:
            profile = await self.get_customer_profile(customer_id)
            
            # Base preferences
            context = {
                "customer_tier": profile.tier.value,
                "preferred_tone": profile.preferences.preferred_tone.value,
                "expertise_level": profile.preferences.expertise_level.value,
                "response_length": profile.preferences.preferred_response_length,
                "include_code_examples": profile.preferences.include_code_examples,
                "include_step_by_step": profile.preferences.include_step_by_step,
                "language": profile.preferences.preferred_language
            }
            
            # Add interaction history context
            context["interaction_context"] = {
                "total_conversations": profile.interaction_history.total_conversations,
                "success_rate": (
                    profile.interaction_history.successful_resolutions / 
                    max(profile.interaction_history.total_conversations, 1)
                ),
                "average_satisfaction": profile.interaction_history.average_satisfaction_score,
                "common_topics": profile.interaction_history.common_topics[:5],
                "is_returning_customer": profile.interaction_history.total_conversations > 1
            }
            
            # Add recent context
            if profile.interaction_history.recent_queries:
                recent_topics = []
                for interaction in profile.interaction_history.recent_queries[-5:]:
                    recent_topics.extend(interaction.get("topics", []))
                context["recent_topics"] = list(set(recent_topics))
            
            # Adaptive preferences based on history
            if profile.interaction_history.escalations_to_human > 2:
                context["needs_extra_care"] = True
                context["preferred_tone"] = "empathetic"
            
            if profile.interaction_history.average_satisfaction_score < 3.0:
                context["needs_improvement"] = True
                context["include_step_by_step"] = True
            
            # Topic-specific adjustments
            if current_topic in profile.interaction_history.common_topics:
                context["familiar_topic"] = True
                context["can_skip_basics"] = profile.preferences.expertise_level in [ExpertiseLevel.ADVANCED, ExpertiseLevel.EXPERT]
            
            return context
            
        except Exception as e:
            self.logger.error(f"Failed to get contextual preferences for {customer_id}: {e}")
            return self._get_default_context()

    def _create_default_profile(self, customer_id: str) -> CustomerProfile:
        """Create a default customer profile."""
        return CustomerProfile(
            customer_id=customer_id,
            tier=CustomerTier.BASIC,
            preferences=CustomerPreferences(),
            interaction_history=InteractionHistory()
        )

    def _get_default_context(self) -> Dict[str, Any]:
        """Get default context when profile loading fails."""
        return {
            "customer_tier": "basic",
            "preferred_tone": "formal",
            "expertise_level": "intermediate",
            "response_length": "medium",
            "include_code_examples": True,
            "include_step_by_step": True,
            "language": "en",
            "interaction_context": {
                "total_conversations": 0,
                "success_rate": 0.0,
                "average_satisfaction": 0.0,
                "common_topics": [],
                "is_returning_customer": False
            }
        }

    async def _cache_profile(self, profile: CustomerProfile):
        """Cache customer profile."""
        if self.cache:
            try:
                profile_data = self._serialize_profile(profile)
                await self.cache.set(
                    f"profile:{profile.customer_id}",
                    json.dumps(profile_data),
                    ttl=self.profile_cache_ttl
                )
            except Exception as e:
                self.logger.error(f"Failed to cache profile: {e}")

    def _serialize_profile(self, profile: CustomerProfile) -> Dict[str, Any]:
        """Serialize profile for storage."""
        return {
            "customer_id": profile.customer_id,
            "tier": profile.tier.value,
            "preferences": {
                "preferred_tone": profile.preferences.preferred_tone.value,
                "expertise_level": profile.preferences.expertise_level.value,
                "preferred_response_length": profile.preferences.preferred_response_length,
                "include_code_examples": profile.preferences.include_code_examples,
                "include_step_by_step": profile.preferences.include_step_by_step,
                "preferred_language": profile.preferences.preferred_language,
                "timezone": profile.preferences.timezone,
                "notification_preferences": profile.preferences.notification_preferences
            },
            "interaction_history": {
                "total_conversations": profile.interaction_history.total_conversations,
                "successful_resolutions": profile.interaction_history.successful_resolutions,
                "escalations_to_human": profile.interaction_history.escalations_to_human,
                "average_satisfaction_score": profile.interaction_history.average_satisfaction_score,
                "common_topics": profile.interaction_history.common_topics,
                "recent_queries": profile.interaction_history.recent_queries,
                "last_interaction": profile.interaction_history.last_interaction.isoformat() if profile.interaction_history.last_interaction else None,
                "interaction_patterns": profile.interaction_history.interaction_patterns
            },
            "product_context": profile.product_context,
            "custom_attributes": profile.custom_attributes,
            "created_at": profile.created_at.isoformat(),
            "updated_at": profile.updated_at.isoformat()
        }

    def _deserialize_profile(self, data: Dict[str, Any]) -> CustomerProfile:
        """Deserialize profile from storage."""
        preferences = CustomerPreferences(
            preferred_tone=PreferredTone(data["preferences"]["preferred_tone"]),
            expertise_level=ExpertiseLevel(data["preferences"]["expertise_level"]),
            preferred_response_length=data["preferences"]["preferred_response_length"],
            include_code_examples=data["preferences"]["include_code_examples"],
            include_step_by_step=data["preferences"]["include_step_by_step"],
            preferred_language=data["preferences"]["preferred_language"],
            timezone=data["preferences"]["timezone"],
            notification_preferences=data["preferences"]["notification_preferences"]
        )
        
        interaction_history = InteractionHistory(
            total_conversations=data["interaction_history"]["total_conversations"],
            successful_resolutions=data["interaction_history"]["successful_resolutions"],
            escalations_to_human=data["interaction_history"]["escalations_to_human"],
            average_satisfaction_score=data["interaction_history"]["average_satisfaction_score"],
            common_topics=data["interaction_history"]["common_topics"],
            recent_queries=data["interaction_history"]["recent_queries"],
            last_interaction=datetime.fromisoformat(data["interaction_history"]["last_interaction"]) if data["interaction_history"]["last_interaction"] else None,
            interaction_patterns=data["interaction_history"]["interaction_patterns"]
        )
        
        return CustomerProfile(
            customer_id=data["customer_id"],
            tier=CustomerTier(data["tier"]),
            preferences=preferences,
            interaction_history=interaction_history,
            product_context=data["product_context"],
            custom_attributes=data["custom_attributes"],
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"])
        )

    async def _load_profile_from_db(self, customer_id: str) -> CustomerProfile:
        """Load profile from database (placeholder for actual DB implementation)."""
        # This would implement actual database loading
        return self._create_default_profile(customer_id)

    async def _save_profile(self, profile: CustomerProfile):
        """Save profile to database (placeholder for actual DB implementation)."""
        # This would implement actual database saving
        pass
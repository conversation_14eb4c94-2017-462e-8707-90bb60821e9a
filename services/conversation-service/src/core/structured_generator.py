"""
Structured Response Generator
Generates well-structured, formatted responses with consistent organization
"""

from typing import Dict, List, Any, Optional, Union
import logging
import re
from dataclasses import dataclass, field
from enum import Enum
import asyncio
from datetime import datetime

@dataclass
class ResponseSection:
    """Individual section of a structured response."""
    
    title: str
    content: str
    section_type: str  # summary, steps, explanation, warning, etc.
    priority: int = 1  # 1=high, 2=medium, 3=low
    formatting: Dict[str, Any] = field(default_factory=dict)

@dataclass
class StructuredResponse:
    """Complete structured response with metadata."""
    
    sections: List[ResponseSection]
    response_type: str
    total_length: int
    estimated_read_time: int  # seconds
    complexity_level: str
    sources_count: int = 0
    
    def to_text(self) -> str:
        """Convert structured response to formatted text."""
        text_parts = []
        
        for section in sorted(self.sections, key=lambda x: x.priority):
            if section.title:
                text_parts.append(f"## {section.title}\n")
            text_parts.append(section.content)
            text_parts.append("")  # Add spacing
        
        return "\n".join(text_parts).strip()

class ResponseType(Enum):
    """Types of structured responses."""
    
    STEP_BY_STEP = "step_by_step"
    TROUBLESHOOTING = "troubleshooting"
    EXPLANATION = "explanation"
    COMPARISON = "comparison"
    FAQ_STYLE = "faq_style"
    QUICK_ANSWER = "quick_answer"
    COMPREHENSIVE = "comprehensive"
    PROCEDURAL = "procedural"

class StructuredGenerator:
    """Generates structured, well-formatted responses."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Response templates by type
        self.response_templates = self._initialize_response_templates()
        
        # Formatting rules
        self.formatting_rules = self._initialize_formatting_rules()
        
        # Content organization patterns
        self.organization_patterns = self._initialize_organization_patterns()

    def _initialize_response_templates(self) -> Dict[str, Dict[str, Any]]:
        """Initialize response structure templates."""
        
        return {
            ResponseType.STEP_BY_STEP.value: {
                "sections": [
                    {"title": "Overview", "type": "summary", "priority": 1},
                    {"title": "Prerequisites", "type": "requirements", "priority": 2},
                    {"title": "Step-by-Step Instructions", "type": "steps", "priority": 1},
                    {"title": "Verification", "type": "validation", "priority": 2},
                    {"title": "Next Steps", "type": "follow_up", "priority": 3}
                ],
                "max_sections": 5,
                "preferred_length": "medium"
            },
            
            ResponseType.TROUBLESHOOTING.value: {
                "sections": [
                    {"title": "Problem Summary", "type": "summary", "priority": 1},
                    {"title": "Quick Fixes", "type": "quick_solutions", "priority": 1},
                    {"title": "Detailed Diagnosis", "type": "diagnosis", "priority": 2},
                    {"title": "Advanced Solutions", "type": "advanced_solutions", "priority": 2},
                    {"title": "Prevention Tips", "type": "prevention", "priority": 3},
                    {"title": "When to Escalate", "type": "escalation", "priority": 3}
                ],
                "max_sections": 6,
                "preferred_length": "long"
            },
            
            ResponseType.EXPLANATION.value: {
                "sections": [
                    {"title": "Key Concept", "type": "definition", "priority": 1},
                    {"title": "How It Works", "type": "explanation", "priority": 1},
                    {"title": "Practical Examples", "type": "examples", "priority": 2},
                    {"title": "Common Use Cases", "type": "use_cases", "priority": 2},
                    {"title": "Related Topics", "type": "related", "priority": 3}
                ],
                "max_sections": 5,
                "preferred_length": "medium"
            },
            
            ResponseType.QUICK_ANSWER.value: {
                "sections": [
                    {"title": "", "type": "direct_answer", "priority": 1},
                    {"title": "Additional Context", "type": "context", "priority": 2}
                ],
                "max_sections": 2,
                "preferred_length": "short"
            },
            
            ResponseType.COMPARISON.value: {
                "sections": [
                    {"title": "Overview", "type": "summary", "priority": 1},
                    {"title": "Key Differences", "type": "comparison_table", "priority": 1},
                    {"title": "Pros and Cons", "type": "pros_cons", "priority": 2},
                    {"title": "Recommendations", "type": "recommendations", "priority": 1},
                    {"title": "Additional Considerations", "type": "considerations", "priority": 3}
                ],
                "max_sections": 5,
                "preferred_length": "medium"
            },
            
            ResponseType.FAQ_STYLE.value: {
                "sections": [
                    {"title": "Quick Answer", "type": "direct_answer", "priority": 1},
                    {"title": "Detailed Explanation", "type": "explanation", "priority": 2},
                    {"title": "Related Questions", "type": "related_faqs", "priority": 3}
                ],
                "max_sections": 3,
                "preferred_length": "medium"
            },
            
            ResponseType.COMPREHENSIVE.value: {
                "sections": [
                    {"title": "Executive Summary", "type": "summary", "priority": 1},
                    {"title": "Background", "type": "background", "priority": 2},
                    {"title": "Detailed Analysis", "type": "analysis", "priority": 1},
                    {"title": "Implementation Guide", "type": "implementation", "priority": 1},
                    {"title": "Best Practices", "type": "best_practices", "priority": 2},
                    {"title": "Troubleshooting", "type": "troubleshooting", "priority": 2},
                    {"title": "Additional Resources", "type": "resources", "priority": 3}
                ],
                "max_sections": 7,
                "preferred_length": "long"
            },
            
            ResponseType.PROCEDURAL.value: {
                "sections": [
                    {"title": "Before You Begin", "type": "prerequisites", "priority": 1},
                    {"title": "Procedure", "type": "procedure", "priority": 1},
                    {"title": "Verification Steps", "type": "verification", "priority": 2},
                    {"title": "Troubleshooting Common Issues", "type": "troubleshooting", "priority": 2},
                    {"title": "What's Next", "type": "next_steps", "priority": 3}
                ],
                "max_sections": 5,
                "preferred_length": "medium"
            }
        }

    def _initialize_formatting_rules(self) -> Dict[str, Dict[str, Any]]:
        """Initialize formatting rules for different content types."""
        
        return {
            "steps": {
                "numbering": True,
                "bullet_style": "numbered",
                "indent_substeps": True,
                "highlight_actions": True
            },
            
            "quick_solutions": {
                "numbering": True,
                "bullet_style": "numbered",
                "emphasize_first": True,
                "time_estimates": True
            },
            
            "comparison_table": {
                "use_table": True,
                "highlight_differences": True,
                "include_scores": True
            },
            
            "pros_cons": {
                "use_columns": True,
                "bullet_style": "bullet",
                "separate_sections": True
            },
            
            "code_examples": {
                "code_blocks": True,
                "syntax_highlighting": True,
                "include_comments": True
            },
            
            "warnings": {
                "highlight": True,
                "icon": "⚠️",
                "border": True
            },
            
            "tips": {
                "highlight": True,
                "icon": "💡",
                "emphasis": "italic"
            }
        }

    def _initialize_organization_patterns(self) -> Dict[str, List[str]]:
        """Initialize content organization patterns."""
        
        return {
            "logical_flow": [
                "problem_identification",
                "solution_overview", 
                "detailed_steps",
                "verification",
                "troubleshooting"
            ],
            
            "complexity_progression": [
                "simple_explanation",
                "basic_implementation",
                "advanced_features",
                "expert_tips"
            ],
            
            "user_journey": [
                "getting_started",
                "basic_usage",
                "common_scenarios",
                "advanced_usage",
                "optimization"
            ],
            
            "problem_solving": [
                "symptom_identification",
                "root_cause_analysis",
                "solution_options",
                "implementation",
                "prevention"
            ]
        }

    async def generate_structured_response(self,
                                         content: str,
                                         response_type: str,
                                         context: Optional[Dict[str, Any]] = None,
                                         sources: Optional[List[Dict[str, Any]]] = None) -> StructuredResponse:
        """Generate a structured response from raw content."""
        
        try:
            # Determine response type if not specified
            if not response_type:
                response_type = await self._detect_response_type(content, context)
            
            # Get template for response type
            template = self.response_templates.get(response_type, 
                                                 self.response_templates[ResponseType.EXPLANATION.value])
            
            # Analyze content structure
            content_analysis = await self._analyze_content_structure(content)
            
            # Generate sections
            sections = await self._generate_sections(
                content, template, content_analysis, context, sources
            )
            
            # Calculate metadata
            total_length = sum(len(section.content) for section in sections)
            estimated_read_time = max(total_length // 200, 30)  # ~200 chars per second
            complexity_level = self._assess_complexity_level(content_analysis)
            sources_count = len(sources) if sources else 0
            
            return StructuredResponse(
                sections=sections,
                response_type=response_type,
                total_length=total_length,
                estimated_read_time=estimated_read_time,
                complexity_level=complexity_level,
                sources_count=sources_count
            )
            
        except Exception as e:
            self.logger.error(f"Structured response generation failed: {e}")
            # Fallback to simple structure
            return self._create_fallback_response(content, sources)

    async def _detect_response_type(self, 
                                  content: str, 
                                  context: Optional[Dict[str, Any]]) -> str:
        """Detect the most appropriate response type."""
        
        content_lower = content.lower()
        
        # Check for step-by-step indicators
        step_indicators = [
            r'\bstep\s+\d+\b', r'\bfirst\b.*\bthen\b', r'\bnext\b.*\bafter\b',
            r'\bfollow these steps\b', r'\bprocedure\b'
        ]
        
        if any(re.search(pattern, content_lower) for pattern in step_indicators):
            return ResponseType.STEP_BY_STEP.value
        
        # Check for troubleshooting indicators
        troubleshoot_indicators = [
            r'\berror\b', r'\bproblem\b', r'\bissue\b', r'\bfix\b',
            r'\btroubleshoot\b', r'\bnot working\b', r'\bfailed\b'
        ]
        
        if any(re.search(pattern, content_lower) for pattern in troubleshoot_indicators):
            return ResponseType.TROUBLESHOOTING.value
        
        # Check for comparison indicators
        comparison_indicators = [
            r'\bvs\b', r'\bversus\b', r'\bcompare\b', r'\bdifference\b',
            r'\bbetter\b.*\bthan\b', r'\balternative\b'
        ]
        
        if any(re.search(pattern, content_lower) for pattern in comparison_indicators):
            return ResponseType.COMPARISON.value
        
        # Check for explanation indicators
        explanation_indicators = [
            r'\bwhat is\b', r'\bhow does\b', r'\bexplain\b', r'\bdefine\b',
            r'\bunderstand\b', r'\bconcept\b'
        ]
        
        if any(re.search(pattern, content_lower) for pattern in explanation_indicators):
            return ResponseType.EXPLANATION.value
        
        # Check context for additional clues
        if context:
            query_type = context.get("query_type", "")
            if query_type == "PROCEDURAL":
                return ResponseType.PROCEDURAL.value
            elif query_type == "FACTUAL" and len(content.split()) < 100:
                return ResponseType.QUICK_ANSWER.value
        
        # Default to explanation
        return ResponseType.EXPLANATION.value

    async def _analyze_content_structure(self, content: str) -> Dict[str, Any]:
        """Analyze the structure and characteristics of content."""
        
        analysis = {
            "word_count": len(content.split()),
            "sentence_count": len(re.findall(r'[.!?]+', content)),
            "paragraph_count": len([p for p in content.split('\n\n') if p.strip()]),
            "has_lists": bool(re.search(r'^\s*[-*•]\s+', content, re.MULTILINE)),
            "has_numbers": bool(re.search(r'\b\d+\b', content)),
            "has_code": bool(re.search(r'`[^`]+`|```[^`]+```', content)),
            "has_urls": bool(re.search(r'https?://\S+', content)),
            "technical_terms": self._count_technical_terms(content),
            "action_words": self._count_action_words(content),
            "question_words": self._count_question_words(content)
        }
        
        # Determine content characteristics
        analysis["is_technical"] = analysis["technical_terms"] > 3
        analysis["is_procedural"] = analysis["action_words"] > 5
        analysis["is_explanatory"] = analysis["question_words"] > 2
        analysis["complexity"] = self._calculate_content_complexity(analysis)
        
        return analysis

    def _count_technical_terms(self, content: str) -> int:
        """Count technical terms in content."""
        
        technical_patterns = [
            r'\bAPI\b', r'\bHTTP\b', r'\bJSON\b', r'\bXML\b', r'\bSSL\b',
            r'\bdatabase\b', r'\bserver\b', r'\bconfiguration\b', r'\bintegration\b',
            r'\bauthentication\b', r'\bencryption\b', r'\bwebhook\b', r'\bendpoint\b'
        ]
        
        return sum(len(re.findall(pattern, content, re.IGNORECASE)) 
                  for pattern in technical_patterns)

    def _count_action_words(self, content: str) -> int:
        """Count action/procedural words in content."""
        
        action_patterns = [
            r'\bclick\b', r'\bselect\b', r'\benter\b', r'\btype\b', r'\bopen\b',
            r'\bclose\b', r'\bsave\b', r'\binstall\b', r'\bconfigure\b', r'\bsetup\b',
            r'\bcreate\b', r'\bdelete\b', r'\bupdate\b', r'\bmodify\b'
        ]
        
        return sum(len(re.findall(pattern, content, re.IGNORECASE)) 
                  for pattern in action_patterns)

    def _count_question_words(self, content: str) -> int:
        """Count question/explanatory words in content."""
        
        question_patterns = [
            r'\bwhat\b', r'\bhow\b', r'\bwhy\b', r'\bwhen\b', r'\bwhere\b',
            r'\bwhich\b', r'\bexplain\b', r'\bdefine\b', r'\bdescribe\b'
        ]
        
        return sum(len(re.findall(pattern, content, re.IGNORECASE)) 
                  for pattern in question_patterns)

    def _calculate_content_complexity(self, analysis: Dict[str, Any]) -> str:
        """Calculate overall content complexity."""
        
        complexity_score = 0
        
        # Word count factor
        if analysis["word_count"] > 500:
            complexity_score += 2
        elif analysis["word_count"] > 200:
            complexity_score += 1
        
        # Technical terms factor
        if analysis["technical_terms"] > 5:
            complexity_score += 2
        elif analysis["technical_terms"] > 2:
            complexity_score += 1
        
        # Structure complexity
        if analysis["has_code"]:
            complexity_score += 1
        if analysis["paragraph_count"] > 5:
            complexity_score += 1
        
        if complexity_score >= 4:
            return "high"
        elif complexity_score >= 2:
            return "medium"
        else:
            return "low"

    async def _generate_sections(self,
                               content: str,
                               template: Dict[str, Any],
                               content_analysis: Dict[str, Any],
                               context: Optional[Dict[str, Any]],
                               sources: Optional[List[Dict[str, Any]]]) -> List[ResponseSection]:
        """Generate structured sections based on template and content."""
        
        sections = []
        content_parts = self._split_content_by_topics(content)
        
        for section_template in template["sections"]:
            section_content = await self._generate_section_content(
                section_template, content_parts, content_analysis, context, sources
            )
            
            if section_content:  # Only add non-empty sections
                section = ResponseSection(
                    title=section_template["title"],
                    content=section_content,
                    section_type=section_template["type"],
                    priority=section_template["priority"],
                    formatting=self.formatting_rules.get(section_template["type"], {})
                )
                sections.append(section)
        
        return sections

    def _split_content_by_topics(self, content: str) -> Dict[str, str]:
        """Split content into logical topic-based parts."""
        
        # Simple topic splitting based on paragraphs and keywords
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        content_parts = {
            "overview": "",
            "steps": "",
            "examples": "",
            "troubleshooting": "",
            "technical_details": "",
            "warnings": "",
            "tips": "",
            "general": ""
        }
        
        for paragraph in paragraphs:
            paragraph_lower = paragraph.lower()
            
            # Classify paragraph by content
            if any(word in paragraph_lower for word in ["step", "first", "then", "next", "finally"]):
                content_parts["steps"] += paragraph + "\n\n"
            elif any(word in paragraph_lower for word in ["example", "instance", "case", "sample"]):
                content_parts["examples"] += paragraph + "\n\n"
            elif any(word in paragraph_lower for word in ["error", "problem", "issue", "troubleshoot", "fix"]):
                content_parts["troubleshooting"] += paragraph + "\n\n"
            elif any(word in paragraph_lower for word in ["warning", "caution", "important", "note"]):
                content_parts["warnings"] += paragraph + "\n\n"
            elif any(word in paragraph_lower for word in ["tip", "hint", "suggestion", "recommend"]):
                content_parts["tips"] += paragraph + "\n\n"
            elif any(word in paragraph_lower for word in ["api", "technical", "code", "configuration"]):
                content_parts["technical_details"] += paragraph + "\n\n"
            elif len(content_parts["overview"]) < 200:  # First content goes to overview
                content_parts["overview"] += paragraph + "\n\n"
            else:
                content_parts["general"] += paragraph + "\n\n"
        
        return content_parts

    async def _generate_section_content(self,
                                      section_template: Dict[str, Any],
                                      content_parts: Dict[str, str],
                                      content_analysis: Dict[str, Any],
                                      context: Optional[Dict[str, Any]],
                                      sources: Optional[List[Dict[str, Any]]]) -> str:
        """Generate content for a specific section."""
        
        section_type = section_template["type"]
        
        # Map section types to content parts
        content_mapping = {
            "summary": content_parts.get("overview", ""),
            "steps": content_parts.get("steps", ""),
            "examples": content_parts.get("examples", ""),
            "troubleshooting": content_parts.get("troubleshooting", ""),
            "technical_details": content_parts.get("technical_details", ""),
            "warnings": content_parts.get("warnings", ""),
            "tips": content_parts.get("tips", ""),
            "direct_answer": content_parts.get("overview", "")[:300],  # Short answer
            "explanation": content_parts.get("general", "") or content_parts.get("overview", ""),
            "prerequisites": self._extract_prerequisites(content_parts),
            "verification": self._extract_verification_steps(content_parts),
            "next_steps": self._generate_next_steps(content_parts, context)
        }
        
        base_content = content_mapping.get(section_type, "")
        
        if not base_content and section_type in ["related", "resources", "follow_up"]:
            # Generate supplementary content
            base_content = self._generate_supplementary_content(section_type, context, sources)
        
        # Apply formatting
        if base_content:
            formatted_content = self._apply_section_formatting(
                base_content, section_template, content_analysis
            )
            return formatted_content
        
        return ""

    def _extract_prerequisites(self, content_parts: Dict[str, str]) -> str:
        """Extract or generate prerequisites section."""
        
        # Look for prerequisite indicators
        all_content = " ".join(content_parts.values())
        prereq_patterns = [
            r'before you begin[^.]*\.?[^.]*\.',
            r'prerequisites?[^.]*\.?[^.]*\.',
            r'you will need[^.]*\.?[^.]*\.',
            r'requirements?[^.]*\.?[^.]*\.'
        ]
        
        for pattern in prereq_patterns:
            match = re.search(pattern, all_content, re.IGNORECASE | re.DOTALL)
            if match:
                return match.group(0)
        
        # Generate basic prerequisites if none found
        if "steps" in content_parts and content_parts["steps"]:
            return "Ensure you have the necessary permissions and access to complete these steps."
        
        return ""

    def _extract_verification_steps(self, content_parts: Dict[str, str]) -> str:
        """Extract or generate verification steps."""
        
        all_content = " ".join(content_parts.values())
        verification_patterns = [
            r'to verify[^.]*\.?[^.]*\.',
            r'check that[^.]*\.?[^.]*\.',
            r'confirm[^.]*\.?[^.]*\.',
            r'test[^.]*\.?[^.]*\.'
        ]
        
        for pattern in verification_patterns:
            match = re.search(pattern, all_content, re.IGNORECASE | re.DOTALL)
            if match:
                return match.group(0)
        
        # Generate basic verification
        if "steps" in content_parts and content_parts["steps"]:
            return "Verify that the changes have been applied successfully and are working as expected."
        
        return ""

    def _generate_next_steps(self, 
                           content_parts: Dict[str, str], 
                           context: Optional[Dict[str, Any]]) -> str:
        """Generate next steps section."""
        
        next_steps = []
        
        # Context-based next steps
        if context:
            intent = context.get("intent", "")
            if "setup" in intent:
                next_steps.append("Test your configuration with a simple example")
                next_steps.append("Review the documentation for advanced features")
            elif "troubleshooting" in intent:
                next_steps.append("Monitor the system to ensure the issue doesn't recur")
                next_steps.append("Document the solution for future reference")
            elif "learning" in intent:
                next_steps.append("Practice with additional examples")
                next_steps.append("Explore related topics and advanced concepts")
        
        # Generic next steps
        if not next_steps:
            next_steps = [
                "Review the implementation to ensure it meets your requirements",
                "Consider any additional customizations needed for your use case",
                "Reach out if you need further assistance or have questions"
            ]
        
        return "\n".join(f"• {step}" for step in next_steps)

    def _generate_supplementary_content(self,
                                      section_type: str,
                                      context: Optional[Dict[str, Any]],
                                      sources: Optional[List[Dict[str, Any]]]) -> str:
        """Generate supplementary content for additional sections."""
        
        if section_type == "resources" and sources:
            resource_list = []
            for source in sources[:5]:  # Limit to top 5 sources
                title = source.get("title", "Related Document")
                resource_list.append(f"• {title}")
            return "\n".join(resource_list)
        
        elif section_type == "related":
            return "For additional information, consider exploring related topics in our knowledge base."
        
        elif section_type == "follow_up":
            return "If you need further assistance or have additional questions, please don't hesitate to ask."
        
        return ""

    def _apply_section_formatting(self,
                                content: str,
                                section_template: Dict[str, Any],
                                content_analysis: Dict[str, Any]) -> str:
        """Apply formatting rules to section content."""
        
        section_type = section_template["type"]
        formatting = self.formatting_rules.get(section_type, {})
        
        formatted_content = content
        
        # Apply numbering for steps
        if formatting.get("numbering") and section_type in ["steps", "quick_solutions"]:
            formatted_content = self._add_step_numbering(formatted_content)
        
        # Apply bullet points
        elif formatting.get("bullet_style") == "bullet":
            formatted_content = self._add_bullet_points(formatted_content)
        
        # Highlight warnings
        if section_type == "warnings" and formatting.get("highlight"):
            icon = formatting.get("icon", "⚠️")
            formatted_content = f"{icon} **Important:** {formatted_content}"
        
        # Highlight tips
        elif section_type == "tips" and formatting.get("highlight"):
            icon = formatting.get("icon", "💡")
            formatted_content = f"{icon} **Tip:** {formatted_content}"
        
        return formatted_content.strip()

    def _add_step_numbering(self, content: str) -> str:
        """Add step numbering to content."""
        
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        numbered_lines = []
        
        step_number = 1
        for line in lines:
            if line and not line.startswith(('•', '-', '*', str(step_number))):
                numbered_lines.append(f"{step_number}. {line}")
                step_number += 1
            else:
                numbered_lines.append(line)
        
        return '\n'.join(numbered_lines)

    def _add_bullet_points(self, content: str) -> str:
        """Add bullet points to content."""
        
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        bulleted_lines = []
        
        for line in lines:
            if line and not line.startswith(('•', '-', '*')):
                bulleted_lines.append(f"• {line}")
            else:
                bulleted_lines.append(line)
        
        return '\n'.join(bulleted_lines)

    def _assess_complexity_level(self, content_analysis: Dict[str, Any]) -> str:
        """Assess the complexity level of the response."""
        
        return content_analysis.get("complexity", "medium")

    def _create_fallback_response(self, 
                                content: str, 
                                sources: Optional[List[Dict[str, Any]]]) -> StructuredResponse:
        """Create a simple fallback response structure."""
        
        sections = [
            ResponseSection(
                title="",
                content=content,
                section_type="general",
                priority=1
            )
        ]
        
        return StructuredResponse(
            sections=sections,
            response_type=ResponseType.EXPLANATION.value,
            total_length=len(content),
            estimated_read_time=max(len(content) // 200, 30),
            complexity_level="medium",
            sources_count=len(sources) if sources else 0
        )

    async def optimize_response_structure(self, 
                                        response: StructuredResponse,
                                        user_preferences: Optional[Dict[str, Any]] = None) -> StructuredResponse:
        """Optimize response structure based on user preferences."""
        
        if not user_preferences:
            return response
        
        # Adjust based on preferred length
        preferred_length = user_preferences.get("preferred_length", "medium")
        if preferred_length == "short":
            # Keep only high priority sections
            response.sections = [s for s in response.sections if s.priority == 1]
        elif preferred_length == "long":
            # Keep all sections
            pass
        
        # Adjust based on technical level
        technical_level = user_preferences.get("technical_level", 0.5)
        if technical_level < 0.3:
            # Simplify technical content
            for section in response.sections:
                section.content = self._simplify_technical_content(section.content)
        
        return response

    def _simplify_technical_content(self, content: str) -> str:
        """Simplify technical content for non-technical users."""
        
        # Technical term replacements
        replacements = {
            r'\bAPI\b': 'application interface',
            r'\bendpoint\b': 'connection point',
            r'\bauthentication\b': 'login verification',
            r'\bJSON\b': 'data format',
            r'\bHTTP\b': 'web protocol'
        }
        
        simplified_content = content
        for technical, simple in replacements.items():
            simplified_content = re.sub(technical, simple, simplified_content, flags=re.IGNORECASE)
        
        return simplified_content

    async def get_structure_analytics(self, 
                                    response: StructuredResponse) -> Dict[str, Any]:
        """Get analytics about response structure."""
        
        return {
            "response_type": response.response_type,
            "section_count": len(response.sections),
            "total_length": response.total_length,
            "estimated_read_time": response.estimated_read_time,
            "complexity_level": response.complexity_level,
            "sections_by_priority": {
                "high": len([s for s in response.sections if s.priority == 1]),
                "medium": len([s for s in response.sections if s.priority == 2]),
                "low": len([s for s in response.sections if s.priority == 3])
            },
            "section_types": [s.section_type for s in response.sections],
            "has_sources": response.sources_count > 0,
            "sources_count": response.sources_count
        }
"""
Tone Adapter
Dynamic tone adjustment for customer support responses
"""

from typing import Dict, List, Any, Optional, Tuple
import logging
import re
from dataclasses import dataclass, field
from enum import Enum
import asyncio

@dataclass
class ToneProfile:
    """Customer tone profile and preferences."""
    
    primary_tone: str
    formality_level: float  # 0.0 = very informal, 1.0 = very formal
    empathy_level: float   # 0.0 = neutral, 1.0 = highly empathetic
    technical_level: float # 0.0 = simple, 1.0 = technical
    urgency_level: float   # 0.0 = relaxed, 1.0 = urgent
    
    # Context factors
    customer_tier: str = "standard"
    interaction_history: List[str] = field(default_factory=list)
    current_sentiment: str = "neutral"
    
    # Preferences
    preferred_length: str = "medium"  # short, medium, long
    include_examples: bool = True
    include_next_steps: bool = True

class CustomerSupportTone(Enum):
    """Customer support tone categories."""
    
    PROFESSIONAL = "professional"
    EMPATHETIC = "empathetic" 
    TECHNICAL = "technical"
    FRIENDLY = "friendly"
    URGENT = "urgent"
    APOLOGETIC = "apologetic"
    EDUCATIONAL = "educational"
    REASSURING = "reassuring"

class ToneAdapter:
    """Adapts response tone based on customer context and query type."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Tone detection patterns
        self.tone_indicators = self._initialize_tone_indicators()
        
        # Response templates by tone
        self.tone_templates = self._initialize_tone_templates()
        
        # Tone transition rules
        self.tone_transitions = self._initialize_tone_transitions()

    def _initialize_tone_indicators(self) -> Dict[str, List[str]]:
        """Initialize patterns for detecting customer tone/sentiment."""
        
        return {
            "frustrated": [
                r'\b(frustrated|annoyed|angry|upset|mad)\b',
                r'\b(terrible|awful|horrible|worst)\b',
                r'\b(not working|broken|useless)\b',
                r'[!]{2,}',  # Multiple exclamation marks
                r'\b(why|how).*(not|never|can\'t|won\'t)\b'
            ],
            
            "urgent": [
                r'\b(urgent|asap|immediately|emergency|critical)\b',
                r'\b(need.*(now|today|right away))\b',
                r'\b(deadline|time sensitive|running out of time)\b'
            ],
            
            "confused": [
                r'\b(confused|don\'t understand|unclear|lost)\b',
                r'\b(what does.*mean|how does.*work)\b',
                r'\b(can you explain|help me understand)\b'
            ],
            
            "technical": [
                r'\b(api|endpoint|integration|webhook|json|xml)\b',
                r'\b(code|programming|development|technical)\b',
                r'\b(error code|stack trace|debugging)\b'
            ],
            
            "polite": [
                r'\b(please|thank you|thanks|appreciate)\b',
                r'\b(could you|would you|if possible)\b',
                r'\b(sorry to bother|excuse me)\b'
            ],
            
            "casual": [
                r'\b(hey|hi|hello|sup)\b',
                r'\b(gonna|wanna|kinda|sorta)\b',
                r'\b(cool|awesome|great|nice)\b'
            ]
        }

    def _initialize_tone_templates(self) -> Dict[str, Dict[str, str]]:
        """Initialize response templates for different tones."""
        
        return {
            CustomerSupportTone.PROFESSIONAL.value: {
                "greeting": "Thank you for contacting our support team.",
                "acknowledgment": "I understand your inquiry regarding {topic}.",
                "solution_intro": "Based on your request, here's the recommended solution:",
                "closing": "Please don't hesitate to reach out if you need further assistance.",
                "escalation": "I'll escalate this to our specialized team for immediate attention."
            },
            
            CustomerSupportTone.EMPATHETIC.value: {
                "greeting": "I'm sorry to hear you're experiencing this issue.",
                "acknowledgment": "I completely understand how frustrating this must be for you.",
                "solution_intro": "Let me help you resolve this right away:",
                "closing": "I hope this helps! I'm here if you need anything else.",
                "escalation": "I want to make sure you get the best possible help, so I'm connecting you with a specialist."
            },
            
            CustomerSupportTone.TECHNICAL.value: {
                "greeting": "I'll help you troubleshoot this technical issue.",
                "acknowledgment": "Based on the error details you've provided:",
                "solution_intro": "Here's the technical solution with implementation steps:",
                "closing": "Let me know if you need clarification on any of these technical steps.",
                "escalation": "This requires our engineering team's expertise. I'm routing this to them now."
            },
            
            CustomerSupportTone.FRIENDLY.value: {
                "greeting": "Hi there! Happy to help you today.",
                "acknowledgment": "I see what you're looking for - that's a great question!",
                "solution_intro": "Here's what I'd recommend:",
                "closing": "Hope that helps! Feel free to ask if you have any other questions.",
                "escalation": "Let me get one of our experts to give you the best answer possible."
            },
            
            CustomerSupportTone.URGENT.value: {
                "greeting": "I understand this is urgent and I'm prioritizing your request.",
                "acknowledgment": "I see the critical nature of this issue.",
                "solution_intro": "Here's the immediate action plan:",
                "closing": "I'll monitor this closely. Contact me immediately if issues persist.",
                "escalation": "Given the urgency, I'm immediately escalating this to our priority support team."
            },
            
            CustomerSupportTone.APOLOGETIC.value: {
                "greeting": "I sincerely apologize for the inconvenience you've experienced.",
                "acknowledgment": "This should not have happened, and I take full responsibility for resolving it.",
                "solution_intro": "Here's how we'll make this right:",
                "closing": "Again, I apologize for this experience. We're committed to doing better.",
                "escalation": "I'm personally ensuring our management team addresses this issue immediately."
            },
            
            CustomerSupportTone.EDUCATIONAL.value: {
                "greeting": "I'd be happy to explain how this works.",
                "acknowledgment": "That's an excellent question about {topic}.",
                "solution_intro": "Let me walk you through this step by step:",
                "closing": "I hope this explanation was helpful! Let me know if you'd like me to clarify anything.",
                "escalation": "For more detailed technical guidance, I'll connect you with our product specialists."
            },
            
            CustomerSupportTone.REASSURING.value: {
                "greeting": "Don't worry - this is a common issue and easily resolved.",
                "acknowledgment": "You're definitely not alone in experiencing this.",
                "solution_intro": "The good news is we can fix this quickly:",
                "closing": "You're all set! This should work smoothly going forward.",
                "escalation": "I want to make sure you feel completely confident, so I'm getting additional expert input."
            }
        }

    def _initialize_tone_transitions(self) -> Dict[str, List[str]]:
        """Initialize tone transition rules based on context."""
        
        return {
            "frustrated_customer": [
                CustomerSupportTone.EMPATHETIC.value,
                CustomerSupportTone.APOLOGETIC.value,
                CustomerSupportTone.REASSURING.value
            ],
            
            "technical_query": [
                CustomerSupportTone.TECHNICAL.value,
                CustomerSupportTone.EDUCATIONAL.value,
                CustomerSupportTone.PROFESSIONAL.value
            ],
            
            "urgent_issue": [
                CustomerSupportTone.URGENT.value,
                CustomerSupportTone.PROFESSIONAL.value,
                CustomerSupportTone.EMPATHETIC.value
            ],
            
            "billing_complaint": [
                CustomerSupportTone.APOLOGETIC.value,
                CustomerSupportTone.EMPATHETIC.value,
                CustomerSupportTone.PROFESSIONAL.value
            ],
            
            "general_inquiry": [
                CustomerSupportTone.FRIENDLY.value,
                CustomerSupportTone.PROFESSIONAL.value,
                CustomerSupportTone.EDUCATIONAL.value
            ],
            
            "confused_user": [
                CustomerSupportTone.EDUCATIONAL.value,
                CustomerSupportTone.REASSURING.value,
                CustomerSupportTone.FRIENDLY.value
            ]
        }

    async def detect_customer_tone(self, 
                                 query: str,
                                 context: Optional[Dict[str, Any]] = None) -> ToneProfile:
        """Detect customer tone and create appropriate response profile."""
        
        query_lower = query.lower()
        tone_scores = {}
        
        # Analyze query for tone indicators
        for tone, patterns in self.tone_indicators.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, query_lower))
                score += matches
            
            if score > 0:
                tone_scores[tone] = score
        
        # Determine primary characteristics
        is_frustrated = tone_scores.get("frustrated", 0) > 0
        is_urgent = tone_scores.get("urgent", 0) > 0
        is_technical = tone_scores.get("technical", 0) > 0
        is_confused = tone_scores.get("confused", 0) > 0
        is_polite = tone_scores.get("polite", 0) > 0
        is_casual = tone_scores.get("casual", 0) > 0
        
        # Calculate tone levels
        formality_level = 0.8 if is_polite else (0.3 if is_casual else 0.6)
        empathy_level = 0.9 if is_frustrated else (0.7 if is_confused else 0.5)
        technical_level = 0.9 if is_technical else 0.4
        urgency_level = 0.9 if is_urgent else (0.7 if is_frustrated else 0.3)
        
        # Determine primary tone
        primary_tone = self._select_primary_tone(
            is_frustrated, is_urgent, is_technical, is_confused, context
        )
        
        # Get customer context
        customer_tier = context.get("customer_tier", "standard") if context else "standard"
        sentiment = "frustrated" if is_frustrated else ("urgent" if is_urgent else "neutral")
        
        return ToneProfile(
            primary_tone=primary_tone,
            formality_level=formality_level,
            empathy_level=empathy_level,
            technical_level=technical_level,
            urgency_level=urgency_level,
            customer_tier=customer_tier,
            current_sentiment=sentiment,
            preferred_length="short" if is_urgent else "medium"
        )

    def _select_primary_tone(self, 
                           is_frustrated: bool,
                           is_urgent: bool, 
                           is_technical: bool,
                           is_confused: bool,
                           context: Optional[Dict[str, Any]]) -> str:
        """Select the most appropriate primary tone."""
        
        # Priority order for tone selection
        if is_frustrated:
            return CustomerSupportTone.EMPATHETIC.value
        elif is_urgent:
            return CustomerSupportTone.URGENT.value
        elif is_technical:
            return CustomerSupportTone.TECHNICAL.value
        elif is_confused:
            return CustomerSupportTone.EDUCATIONAL.value
        else:
            # Check context for additional clues
            if context:
                intent = context.get("intent", "")
                if "billing" in intent or "payment" in intent:
                    return CustomerSupportTone.PROFESSIONAL.value
                elif "complaint" in intent:
                    return CustomerSupportTone.APOLOGETIC.value
                elif "compliment" in intent:
                    return CustomerSupportTone.FRIENDLY.value
            
            return CustomerSupportTone.PROFESSIONAL.value

    async def adapt_response_tone(self, 
                                response: str,
                                tone_profile: ToneProfile,
                                context: Optional[Dict[str, Any]] = None) -> str:
        """Adapt response tone based on customer profile."""
        
        try:
            # Get tone templates
            templates = self.tone_templates.get(tone_profile.primary_tone, {})
            
            # Structure the response with appropriate tone
            adapted_response = self._apply_tone_structure(
                response, templates, tone_profile, context
            )
            
            # Apply tone-specific modifications
            adapted_response = self._apply_tone_modifications(
                adapted_response, tone_profile
            )
            
            return adapted_response
            
        except Exception as e:
            self.logger.error(f"Tone adaptation failed: {e}")
            return response  # Return original if adaptation fails

    def _apply_tone_structure(self, 
                            response: str,
                            templates: Dict[str, str],
                            tone_profile: ToneProfile,
                            context: Optional[Dict[str, Any]]) -> str:
        """Apply tone-specific structure to response."""
        
        # Extract topic from context
        topic = context.get("topic", "your request") if context else "your request"
        
        # Build structured response
        parts = []
        
        # Greeting (tone-appropriate)
        if "greeting" in templates:
            greeting = templates["greeting"]
            if "{topic}" in greeting:
                greeting = greeting.format(topic=topic)
            parts.append(greeting)
        
        # Acknowledgment (if empathy level is high)
        if tone_profile.empathy_level > 0.6 and "acknowledgment" in templates:
            acknowledgment = templates["acknowledgment"]
            if "{topic}" in acknowledgment:
                acknowledgment = acknowledgment.format(topic=topic)
            parts.append(acknowledgment)
        
        # Solution introduction
        if "solution_intro" in templates:
            parts.append(templates["solution_intro"])
        
        # Main response content
        parts.append(response)
        
        # Closing (tone-appropriate)
        if "closing" in templates:
            parts.append(templates["closing"])
        
        return "\n\n".join(parts)

    def _apply_tone_modifications(self, 
                                response: str,
                                tone_profile: ToneProfile) -> str:
        """Apply tone-specific language modifications."""
        
        modified_response = response
        
        # Formality adjustments
        if tone_profile.formality_level < 0.4:
            # Make more casual
            modified_response = self._make_more_casual(modified_response)
        elif tone_profile.formality_level > 0.8:
            # Make more formal
            modified_response = self._make_more_formal(modified_response)
        
        # Empathy adjustments
        if tone_profile.empathy_level > 0.7:
            modified_response = self._add_empathy_markers(modified_response)
        
        # Technical level adjustments
        if tone_profile.technical_level < 0.3:
            modified_response = self._simplify_technical_language(modified_response)
        
        # Urgency adjustments
        if tone_profile.urgency_level > 0.7:
            modified_response = self._add_urgency_markers(modified_response)
        
        return modified_response

    def _make_more_casual(self, text: str) -> str:
        """Make text more casual and friendly."""
        
        # Casual replacements
        replacements = {
            r'\bYou are\b': "You're",
            r'\bI will\b': "I'll", 
            r'\bWe will\b': "We'll",
            r'\bCannot\b': "Can't",
            r'\bDo not\b': "Don't",
            r'\bWould not\b': "Wouldn't",
            r'\bShould not\b': "Shouldn't",
            r'\bregarding\b': "about",
            r'\butilize\b': "use",
            r'\bassistance\b': "help"
        }
        
        for formal, casual in replacements.items():
            text = re.sub(formal, casual, text, flags=re.IGNORECASE)
        
        return text

    def _make_more_formal(self, text: str) -> str:
        """Make text more formal and professional."""
        
        # Formal replacements
        replacements = {
            r'\bYou\'re\b': "You are",
            r'\bI\'ll\b': "I will",
            r'\bWe\'ll\b': "We will", 
            r'\bCan\'t\b': "Cannot",
            r'\bDon\'t\b': "Do not",
            r'\bWouldn\'t\b': "Would not",
            r'\bShouldn\'t\b': "Should not",
            r'\babout\b': "regarding",
            r'\buse\b': "utilize",
            r'\bhelp\b': "assistance",
            r'\bOkay\b': "Very well",
            r'\bSure\b': "Certainly"
        }
        
        for casual, formal in replacements.items():
            text = re.sub(casual, formal, text, flags=re.IGNORECASE)
        
        return text

    def _add_empathy_markers(self, text: str) -> str:
        """Add empathetic language markers."""
        
        empathy_phrases = [
            "I understand how important this is to you.",
            "I can see why this would be concerning.",
            "I appreciate your patience with this matter.",
            "I want to make sure we resolve this for you."
        ]
        
        # Add empathy marker if not already present
        if not any(phrase.lower() in text.lower() for phrase in empathy_phrases):
            # Insert empathy marker after first sentence
            sentences = text.split('. ')
            if len(sentences) > 1:
                sentences.insert(1, empathy_phrases[0])
                text = '. '.join(sentences)
        
        return text

    def _simplify_technical_language(self, text: str) -> str:
        """Simplify technical language for non-technical users."""
        
        # Technical term simplifications
        simplifications = {
            r'\bAPI\b': "application interface",
            r'\bendpoint\b': "connection point",
            r'\bauthentication\b': "login verification",
            r'\bintegration\b': "connection",
            r'\bconfiguration\b': "settings",
            r'\bparameters\b': "settings",
            r'\bwebhook\b': "automatic notification",
            r'\bJSON\b': "data format",
            r'\bSSL\b': "security certificate"
        }
        
        for technical, simple in simplifications.items():
            text = re.sub(technical, simple, text, flags=re.IGNORECASE)
        
        return text

    def _add_urgency_markers(self, text: str) -> str:
        """Add urgency markers to response."""
        
        urgency_phrases = [
            "I'm prioritizing this request immediately.",
            "This is being handled as a priority.",
            "I'll ensure this gets immediate attention.",
            "Let me fast-track this for you."
        ]
        
        # Add urgency marker at the beginning
        if not any(phrase.lower() in text.lower() for phrase in urgency_phrases):
            text = urgency_phrases[0] + " " + text
        
        return text

    async def get_tone_recommendations(self, 
                                     context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get tone recommendations based on context."""
        
        recommendations = []
        
        # Analyze context
        intent = context.get("intent", "")
        customer_sentiment = context.get("sentiment", "neutral")
        customer_tier = context.get("customer_tier", "standard")
        
        # Generate recommendations
        if "complaint" in intent or customer_sentiment == "frustrated":
            recommendations.append({
                "tone": CustomerSupportTone.EMPATHETIC.value,
                "reason": "Customer appears frustrated - empathy will help de-escalate",
                "confidence": 0.9
            })
            recommendations.append({
                "tone": CustomerSupportTone.APOLOGETIC.value,
                "reason": "Complaint situation may require acknowledgment of fault",
                "confidence": 0.7
            })
        
        elif "technical" in intent:
            recommendations.append({
                "tone": CustomerSupportTone.TECHNICAL.value,
                "reason": "Technical query requires precise, detailed response",
                "confidence": 0.8
            })
            recommendations.append({
                "tone": CustomerSupportTone.EDUCATIONAL.value,
                "reason": "May need to explain technical concepts clearly",
                "confidence": 0.6
            })
        
        elif customer_tier == "enterprise":
            recommendations.append({
                "tone": CustomerSupportTone.PROFESSIONAL.value,
                "reason": "Enterprise customer expects formal, professional communication",
                "confidence": 0.9
            })
        
        else:
            recommendations.append({
                "tone": CustomerSupportTone.FRIENDLY.value,
                "reason": "General inquiry benefits from friendly, approachable tone",
                "confidence": 0.7
            })
        
        return recommendations

    async def analyze_tone_effectiveness(self, 
                                       response: str,
                                       tone_used: str,
                                       customer_feedback: Optional[str] = None) -> Dict[str, Any]:
        """Analyze effectiveness of tone choice."""
        
        analysis = {
            "tone_used": tone_used,
            "response_length": len(response.split()),
            "formality_score": self._calculate_formality_score(response),
            "empathy_score": self._calculate_empathy_score(response),
            "technical_score": self._calculate_technical_score(response)
        }
        
        # Analyze customer feedback if available
        if customer_feedback:
            feedback_sentiment = self._analyze_feedback_sentiment(customer_feedback)
            analysis["feedback_sentiment"] = feedback_sentiment
            analysis["tone_effectiveness"] = self._calculate_tone_effectiveness(
                tone_used, feedback_sentiment
            )
        
        return analysis

    def _calculate_formality_score(self, text: str) -> float:
        """Calculate formality score of text."""
        
        formal_indicators = [
            r'\bregarding\b', r'\butilize\b', r'\bassistance\b',
            r'\bYou are\b', r'\bI will\b', r'\bWe will\b'
        ]
        
        casual_indicators = [
            r'\bYou\'re\b', r'\bI\'ll\b', r'\bWe\'ll\b',
            r'\bCan\'t\b', r'\bDon\'t\b', r'\bokay\b', r'\bsure\b'
        ]
        
        formal_count = sum(len(re.findall(pattern, text, re.IGNORECASE)) 
                          for pattern in formal_indicators)
        casual_count = sum(len(re.findall(pattern, text, re.IGNORECASE)) 
                          for pattern in casual_indicators)
        
        total_words = len(text.split())
        if total_words == 0:
            return 0.5
        
        formal_ratio = formal_count / total_words
        casual_ratio = casual_count / total_words
        
        return min(max((formal_ratio - casual_ratio + 0.5), 0.0), 1.0)

    def _calculate_empathy_score(self, text: str) -> float:
        """Calculate empathy score of text."""
        
        empathy_indicators = [
            r'\bunderstand\b', r'\bsorry\b', r'\bapologize\b',
            r'\bfrustrating\b', r'\bimportant\b', r'\bappreciate\b',
            r'\bconcerning\b', r'\bpatience\b'
        ]
        
        empathy_count = sum(len(re.findall(pattern, text, re.IGNORECASE)) 
                           for pattern in empathy_indicators)
        
        total_words = len(text.split())
        if total_words == 0:
            return 0.0
        
        return min(empathy_count / total_words * 10, 1.0)  # Scale to 0-1

    def _calculate_technical_score(self, text: str) -> float:
        """Calculate technical complexity score of text."""
        
        technical_indicators = [
            r'\bAPI\b', r'\bendpoint\b', r'\bauthentication\b',
            r'\bintegration\b', r'\bconfiguration\b', r'\bparameters\b',
            r'\bwebhook\b', r'\bJSON\b', r'\bSSL\b', r'\bHTTPS\b'
        ]
        
        technical_count = sum(len(re.findall(pattern, text, re.IGNORECASE)) 
                             for pattern in technical_indicators)
        
        total_words = len(text.split())
        if total_words == 0:
            return 0.0
        
        return min(technical_count / total_words * 5, 1.0)  # Scale to 0-1

    def _analyze_feedback_sentiment(self, feedback: str) -> str:
        """Analyze sentiment of customer feedback."""
        
        positive_indicators = [
            r'\bthank\b', r'\bgreat\b', r'\bhelpful\b', r'\bexcellent\b',
            r'\bperfect\b', r'\bawesome\b', r'\bappreciate\b'
        ]
        
        negative_indicators = [
            r'\bunhelpful\b', r'\bterrible\b', r'\bawful\b', r'\bworse\b',
            r'\bfrustrated\b', r'\bangry\b', r'\bdisappointed\b'
        ]
        
        positive_count = sum(len(re.findall(pattern, feedback, re.IGNORECASE)) 
                            for pattern in positive_indicators)
        negative_count = sum(len(re.findall(pattern, feedback, re.IGNORECASE)) 
                            for pattern in negative_indicators)
        
        if positive_count > negative_count:
            return "positive"
        elif negative_count > positive_count:
            return "negative"
        else:
            return "neutral"

    def _calculate_tone_effectiveness(self, tone_used: str, feedback_sentiment: str) -> float:
        """Calculate effectiveness score based on tone and feedback."""
        
        # Tone-sentiment compatibility matrix
        compatibility = {
            CustomerSupportTone.EMPATHETIC.value: {"positive": 0.9, "neutral": 0.8, "negative": 0.6},
            CustomerSupportTone.PROFESSIONAL.value: {"positive": 0.8, "neutral": 0.9, "negative": 0.7},
            CustomerSupportTone.FRIENDLY.value: {"positive": 0.9, "neutral": 0.8, "negative": 0.5},
            CustomerSupportTone.TECHNICAL.value: {"positive": 0.8, "neutral": 0.8, "negative": 0.6},
            CustomerSupportTone.URGENT.value: {"positive": 0.7, "neutral": 0.7, "negative": 0.8},
            CustomerSupportTone.APOLOGETIC.value: {"positive": 0.8, "neutral": 0.7, "negative": 0.9}
        }
        
        return compatibility.get(tone_used, {}).get(feedback_sentiment, 0.5)
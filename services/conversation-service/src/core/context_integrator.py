"""
Context Integrator for RAG Pipeline

This module provides sophisticated context integration capabilities including:
- Customer profile integration with retrieved documents
- Dynamic context weighting and prioritization  
- Multi-source context fusion
- Adaptive context selection based on user preferences
"""

from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json
import logging
import numpy as np
from collections import defaultdict

from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate

from .customer_profiler import CustomerProfiler, CustomerProfile, ExpertiseLevel, CommunicationStyle
from .advanced_query_processor import QueryAnalysis, QueryType, QueryComplexity

logger = logging.getLogger(__name__)


class ContextSource(Enum):
    """Context source types."""
    KNOWLEDGE_BASE = "knowledge_base"
    CONVERSATION_HISTORY = "conversation_history"
    USER_PROFILE = "user_profile"
    TEMPORAL = "temporal"
    DOMAIN_SPECIFIC = "domain_specific"
    EXTERNAL = "external"


class ContextRelevance(Enum):
    """Context relevance levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    IRRELEVANT = "irrelevant"


@dataclass
class ContextPiece:
    """Individual piece of context with metadata."""
    content: str
    source: ContextSource
    relevance: ContextRelevance
    confidence_score: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    tokens_estimate: int = 0
    
    def __post_init__(self):
        """Estimate token count after initialization."""
        if not self.tokens_estimate:
            self.tokens_estimate = len(self.content.split()) * 1.3  # Rough estimate


@dataclass
class IntegratedContext:
    """Fully integrated context ready for response generation."""
    primary_context: str
    supporting_contexts: List[str]
    user_personalization: Dict[str, Any]
    context_sources: Dict[ContextSource, float]  # Source -> weight mapping
    total_tokens: int
    confidence_score: float
    adaptation_notes: List[str]
    metadata: Dict[str, Any] = field(default_factory=dict)


class ContextIntegrator:
    """Advanced context integration system that combines multiple sources."""
    
    def __init__(self, customer_profiler: CustomerProfiler, model: str = "gpt-4o-mini"):
        self.customer_profiler = customer_profiler
        self.llm = ChatOpenAI(model=model, temperature=0.1)
        
        # Context integration prompts
        self.context_synthesizer = self._create_context_synthesizer()
        self.relevance_ranker = self._create_relevance_ranker()
        self.personalization_adapter = self._create_personalization_adapter()
        self.context_compressor = self._create_context_compressor()
        
        # Configuration
        self.max_context_tokens = 4000
        self.min_context_pieces = 2
        self.max_context_pieces = 8
        
        # Context weighting strategies
        self.source_weights = {
            ContextSource.KNOWLEDGE_BASE: 0.4,
            ContextSource.CONVERSATION_HISTORY: 0.3,
            ContextSource.USER_PROFILE: 0.2,
            ContextSource.TEMPORAL: 0.05,
            ContextSource.DOMAIN_SPECIFIC: 0.05
        }
        
    def _create_context_synthesizer(self) -> ChatPromptTemplate:
        """Create context synthesis prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """You are a context synthesis expert. Your task is to integrate multiple context sources 
into a coherent, personalized context for answering a user's query.

Consider:
- User's expertise level and communication preferences
- Query complexity and type
- Relevance and reliability of each context source
- Optimal information density and structure

Return a JSON object:
{
    "primary_context": "main context for the response",
    "supporting_contexts": ["context1", "context2"],
    "integration_strategy": "how contexts were combined",
    "personalization_applied": ["adaptation1", "adaptation2"],
    "confidence": 0.9,
    "reasoning": "explanation of synthesis decisions"
}"""),
            ("human", """User Query: {query}
Query Analysis: {query_analysis}
User Profile: {user_profile}

Available Context Sources:
{context_sources}

Synthesize optimal context:""")
        ])
    
    def _create_relevance_ranker(self) -> ChatPromptTemplate:
        """Create relevance ranking prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """Rank context pieces by relevance to the user's query and profile.

Consider:
- Direct relevance to query
- User's expertise level and interests
- Information freshness and reliability
- Complementary information value

Return a JSON object with ranked contexts:
{
    "ranked_contexts": [
        {
            "index": 0,
            "relevance_score": 0.95,
            "relevance_reason": "directly answers the query",
            "personalization_fit": 0.8
        }
    ],
    "ranking_strategy": "explanation of ranking approach"
}"""),
            ("human", """Query: {query}
User Profile Summary: {user_profile}

Context Pieces to Rank:
{context_pieces}

Rank by relevance:""")
        ])
    
    def _create_personalization_adapter(self) -> ChatPromptTemplate:
        """Create personalization adaptation prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """Adapt context presentation based on user profile and preferences.

Adapt for:
- Technical level and expertise
- Communication style preferences
- Learning style and example preferences
- Response length preferences
- Domain expertise

Return a JSON object:
{
    "adapted_context": "context adapted for user",
    "adaptations_made": ["technical level adjustment", "added examples"],
    "personalization_score": 0.8,
    "reasoning": "explanation of adaptations"
}"""),
            ("human", """Original Context: {original_context}
User Profile: {user_profile}
Communication Preferences: {communication_preferences}

Adapt context for user:""")
        ])
    
    def _create_context_compressor(self) -> ChatPromptTemplate:
        """Create context compression prompt."""
        return ChatPromptTemplate.from_messages([
            ("system", """Compress context while preserving essential information and user-relevant details.

Maintain:
- Key facts and information
- User-specific adaptations
- Critical details for query answering
- Logical flow and coherence

Return compressed context as a string."""),
            ("human", """Context to Compress ({current_tokens} tokens -> target: {target_tokens} tokens):
{context}

User Priorities: {user_priorities}

Compress context:""")
        ])
    
    async def integrate_context(
        self,
        customer_id: str,
        query: str,
        query_analysis: QueryAnalysis,
        retrieved_documents: List[Dict[str, Any]],
        conversation_history: Optional[str] = None,
        additional_context: Optional[Dict[ContextSource, List[str]]] = None
    ) -> IntegratedContext:
        """Integrate all context sources into personalized response context."""
        
        # Get customer profile and preferences
        customer_profile = self.customer_profiler.get_or_create_profile(customer_id)
        context_preferences = self.customer_profiler.get_context_preferences(customer_id)
        personalization_hints = self.customer_profiler.get_personalization_hints(customer_id)
        
        # Build context pieces from all sources
        context_pieces = await self._build_context_pieces(
            query, query_analysis, retrieved_documents, conversation_history,
            additional_context, customer_profile
        )
        
        # Rank and filter context pieces
        ranked_pieces = await self._rank_context_pieces(query, context_pieces, context_preferences)
        
        # Select optimal context subset
        selected_pieces = self._select_context_subset(ranked_pieces, query_analysis, context_preferences)
        
        # Synthesize integrated context
        integrated = await self._synthesize_context(
            query, query_analysis, selected_pieces, customer_profile, personalization_hints
        )
        
        # Apply final personalization
        personalized = await self._apply_personalization(integrated, customer_profile, context_preferences)
        
        # Compress if needed
        if personalized.total_tokens > self.max_context_tokens:
            personalized = await self._compress_context(personalized, customer_profile)
        
        logger.info(f"Integrated context for {customer_id}: {len(selected_pieces)} pieces, {personalized.total_tokens} tokens")
        return personalized
    
    async def _build_context_pieces(
        self,
        query: str,
        query_analysis: QueryAnalysis,
        retrieved_documents: List[Dict[str, Any]],
        conversation_history: Optional[str],
        additional_context: Optional[Dict[ContextSource, List[str]]],
        customer_profile: CustomerProfile
    ) -> List[ContextPiece]:
        """Build context pieces from all available sources."""
        pieces = []
        
        # Knowledge base context
        for i, doc in enumerate(retrieved_documents[:6]):  # Limit to top 6 documents
            relevance = self._determine_relevance(doc.get("score", 0.5))
            pieces.append(ContextPiece(
                content=doc["content"],
                source=ContextSource.KNOWLEDGE_BASE,
                relevance=relevance,
                confidence_score=doc.get("score", 0.5),
                metadata={
                    "document_id": doc.get("id", f"doc_{i}"),
                    "title": doc.get("title", "Untitled"),
                    "source_metadata": doc.get("metadata", {})
                }
            ))
        
        # Conversation history context
        if conversation_history:
            pieces.append(ContextPiece(
                content=conversation_history,
                source=ContextSource.CONVERSATION_HISTORY,
                relevance=ContextRelevance.HIGH,
                confidence_score=0.8,
                metadata={"type": "conversation_summary"}
            ))
        
        # User profile context
        profile_context = self._build_profile_context(customer_profile, query_analysis)
        if profile_context:
            pieces.append(ContextPiece(
                content=profile_context,
                source=ContextSource.USER_PROFILE,
                relevance=ContextRelevance.MEDIUM,
                confidence_score=0.7,
                metadata={"profile_elements": ["preferences", "expertise", "interests"]}
            ))
        
        # Temporal context
        temporal_context = self._build_temporal_context(query_analysis)
        if temporal_context:
            pieces.append(ContextPiece(
                content=temporal_context,
                source=ContextSource.TEMPORAL,
                relevance=ContextRelevance.LOW,
                confidence_score=0.6,
                metadata={"type": "temporal_information"}
            ))
        
        # Additional context sources
        if additional_context:
            for source, contexts in additional_context.items():
                for context in contexts:
                    pieces.append(ContextPiece(
                        content=context,
                        source=source,
                        relevance=ContextRelevance.MEDIUM,
                        confidence_score=0.6,
                        metadata={"source": "additional", "type": source.value}
                    ))
        
        return pieces
    
    def _determine_relevance(self, score: float) -> ContextRelevance:
        """Determine relevance level from score."""
        if score >= 0.9:
            return ContextRelevance.CRITICAL
        elif score >= 0.7:
            return ContextRelevance.HIGH
        elif score >= 0.5:
            return ContextRelevance.MEDIUM
        elif score >= 0.3:
            return ContextRelevance.LOW
        else:
            return ContextRelevance.IRRELEVANT
    
    def _build_profile_context(self, profile: CustomerProfile, query_analysis: QueryAnalysis) -> Optional[str]:
        """Build context from user profile."""
        profile_elements = []
        
        # Add expertise level context
        profile_elements.append(f"User expertise level: {profile.expertise_level.value}")
        
        # Add interests if relevant
        if profile.primary_interests:
            relevant_interests = [interest for interest in profile.primary_interests 
                                if any(keyword in interest.lower() for keyword in query_analysis.keywords)]
            if relevant_interests:
                profile_elements.append(f"User interests: {', '.join(relevant_interests)}")
        
        # Add domain expertise if relevant
        if profile.domain_expertise:
            relevant_domains = {domain: score for domain, score in profile.domain_expertise.items()
                              if score > 0.5 and any(keyword in domain.lower() for keyword in query_analysis.keywords)}
            if relevant_domains:
                domain_str = ', '.join([f"{domain} ({score:.1f})" for domain, score in relevant_domains.items()])
                profile_elements.append(f"User domain expertise: {domain_str}")
        
        # Add communication preferences
        profile_elements.append(f"Preferred communication style: {profile.communication_style.value}")
        profile_elements.append(f"Preferred response length: {profile.preferred_response_length}")
        
        return "; ".join(profile_elements) if profile_elements else None
    
    def _build_temporal_context(self, query_analysis: QueryAnalysis) -> Optional[str]:
        """Build temporal context if relevant."""
        temporal_keywords = ["current", "recent", "latest", "now", "today", "new", "updated"]
        
        if any(keyword in query_analysis.original_query.lower() for keyword in temporal_keywords):
            return f"Current timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        return None
    
    async def _rank_context_pieces(
        self, 
        query: str, 
        context_pieces: List[ContextPiece], 
        context_preferences: Dict[str, Any]
    ) -> List[ContextPiece]:
        """Rank context pieces by relevance and personalization fit."""
        if len(context_pieces) <= 2:
            return context_pieces
        
        try:
            # Prepare context for ranking
            pieces_data = []
            for i, piece in enumerate(context_pieces):
                pieces_data.append({
                    "index": i,
                    "content": piece.content[:300],  # Truncate for ranking
                    "source": piece.source.value,
                    "current_relevance": piece.relevance.value,
                    "confidence": piece.confidence_score
                })
            
            response = await self.relevance_ranker.ainvoke({
                "query": query,
                "user_profile": json.dumps(context_preferences, indent=2),
                "context_pieces": json.dumps(pieces_data, indent=2)
            })
            
            ranking_result = json.loads(response.content)
            ranked_contexts = ranking_result.get("ranked_contexts", [])
            
            # Apply ranking
            ranked_pieces = []
            for rank_info in ranked_contexts:
                index = rank_info["index"]
                if 0 <= index < len(context_pieces):
                    piece = context_pieces[index]
                    # Update relevance based on ranking
                    piece.confidence_score = max(piece.confidence_score, rank_info.get("relevance_score", piece.confidence_score))
                    piece.metadata["ranking_score"] = rank_info.get("relevance_score", 0.5)
                    piece.metadata["personalization_fit"] = rank_info.get("personalization_fit", 0.5)
                    ranked_pieces.append(piece)
            
            # Add any pieces not included in ranking
            ranked_indices = {rank_info["index"] for rank_info in ranked_contexts}
            for i, piece in enumerate(context_pieces):
                if i not in ranked_indices:
                    ranked_pieces.append(piece)
            
            return ranked_pieces
            
        except Exception as e:
            logger.error(f"Error ranking context pieces: {e}")
            # Fallback to original ordering with basic scoring
            return sorted(context_pieces, key=lambda x: x.confidence_score, reverse=True)
    
    def _select_context_subset(
        self, 
        ranked_pieces: List[ContextPiece], 
        query_analysis: QueryAnalysis,
        context_preferences: Dict[str, Any]
    ) -> List[ContextPiece]:
        """Select optimal subset of context pieces."""
        selected = []
        total_tokens = 0
        
        # Always include critical and high relevance pieces
        for piece in ranked_pieces:
            if piece.relevance in [ContextRelevance.CRITICAL, ContextRelevance.HIGH]:
                if total_tokens + piece.tokens_estimate <= self.max_context_tokens:
                    selected.append(piece)
                    total_tokens += piece.tokens_estimate
        
        # Add medium relevance pieces if space allows
        for piece in ranked_pieces:
            if piece.relevance == ContextRelevance.MEDIUM and len(selected) < self.max_context_pieces:
                if total_tokens + piece.tokens_estimate <= self.max_context_tokens:
                    selected.append(piece)
                    total_tokens += piece.tokens_estimate
        
        # Ensure minimum context pieces
        if len(selected) < self.min_context_pieces:
            for piece in ranked_pieces:
                if piece not in selected and len(selected) < self.min_context_pieces:
                    selected.append(piece)
                    total_tokens += piece.tokens_estimate
        
        # Apply query complexity considerations
        if query_analysis.complexity == QueryComplexity.COMPLEX:
            # For complex queries, prefer more diverse sources
            source_counts = defaultdict(int)
            filtered_selected = []
            
            for piece in selected:
                if source_counts[piece.source] < 2:  # Max 2 pieces per source for complex queries
                    filtered_selected.append(piece)
                    source_counts[piece.source] += 1
            
            selected = filtered_selected
        
        return selected
    
    async def _synthesize_context(
        self,
        query: str,
        query_analysis: QueryAnalysis,
        context_pieces: List[ContextPiece],
        customer_profile: CustomerProfile,
        personalization_hints: Dict[str, Any]
    ) -> IntegratedContext:
        """Synthesize context pieces into integrated context."""
        try:
            # Prepare context sources for synthesis
            sources_data = {}
            for piece in context_pieces:
                source_key = piece.source.value
                if source_key not in sources_data:
                    sources_data[source_key] = []
                sources_data[source_key].append({
                    "content": piece.content,
                    "relevance": piece.relevance.value,
                    "confidence": piece.confidence_score,
                    "metadata": piece.metadata
                })
            
            # Prepare user profile summary
            profile_summary = {
                "expertise_level": customer_profile.expertise_level.value,
                "communication_style": customer_profile.communication_style.value,
                "interests": customer_profile.primary_interests[:3],
                "domain_expertise": customer_profile.domain_expertise
            }
            
            response = await self.context_synthesizer.ainvoke({
                "query": query,
                "query_analysis": json.dumps({
                    "type": query_analysis.query_type.value,
                    "complexity": query_analysis.complexity.value,
                    "intent": query_analysis.intent,
                    "topics": query_analysis.topics
                }),
                "user_profile": json.dumps(profile_summary, indent=2),
                "context_sources": json.dumps(sources_data, indent=2)
            })
            
            synthesis_result = json.loads(response.content)
            
            # Calculate source weights
            source_weights = {}
            for piece in context_pieces:
                source = piece.source
                if source not in source_weights:
                    source_weights[source] = 0
                source_weights[source] += piece.confidence_score
            
            # Normalize weights
            total_weight = sum(source_weights.values())
            if total_weight > 0:
                source_weights = {source: weight/total_weight for source, weight in source_weights.items()}
            
            # Calculate total tokens
            total_tokens = sum(piece.tokens_estimate for piece in context_pieces)
            
            return IntegratedContext(
                primary_context=synthesis_result["primary_context"],
                supporting_contexts=synthesis_result.get("supporting_contexts", []),
                user_personalization=personalization_hints,
                context_sources=source_weights,
                total_tokens=total_tokens,
                confidence_score=synthesis_result.get("confidence", 0.7),
                adaptation_notes=synthesis_result.get("personalization_applied", []),
                metadata={
                    "synthesis_strategy": synthesis_result.get("integration_strategy", ""),
                    "reasoning": synthesis_result.get("reasoning", ""),
                    "original_pieces_count": len(context_pieces)
                }
            )
            
        except Exception as e:
            logger.error(f"Error synthesizing context: {e}")
            return self._fallback_synthesis(context_pieces, personalization_hints)
    
    def _fallback_synthesis(self, context_pieces: List[ContextPiece], personalization_hints: Dict[str, Any]) -> IntegratedContext:
        """Fallback context synthesis using simple rules."""
        # Find highest confidence piece as primary
        primary_piece = max(context_pieces, key=lambda x: x.confidence_score) if context_pieces else None
        primary_context = primary_piece.content if primary_piece else "No relevant context available."
        
        # Use remaining pieces as supporting context
        supporting_contexts = []
        for piece in context_pieces:
            if piece != primary_piece and len(supporting_contexts) < 3:
                supporting_contexts.append(piece.content[:200])  # Truncate
        
        # Simple source weights
        source_weights = {}
        for piece in context_pieces:
            source_weights[piece.source] = source_weights.get(piece.source, 0) + 0.1
        
        total_tokens = sum(piece.tokens_estimate for piece in context_pieces)
        
        return IntegratedContext(
            primary_context=primary_context,
            supporting_contexts=supporting_contexts,
            user_personalization=personalization_hints,
            context_sources=source_weights,
            total_tokens=total_tokens,
            confidence_score=0.6,
            adaptation_notes=["fallback_synthesis"],
            metadata={"method": "fallback"}
        )
    
    async def _apply_personalization(
        self, 
        integrated: IntegratedContext, 
        customer_profile: CustomerProfile,
        context_preferences: Dict[str, Any]
    ) -> IntegratedContext:
        """Apply personalization to integrated context."""
        try:
            # Prepare communication preferences
            comm_prefs = {
                "expertise_level": customer_profile.expertise_level.value,
                "communication_style": customer_profile.communication_style.value,
                "response_length": customer_profile.preferred_response_length,
                "include_examples": customer_profile.preferred_examples,
                "technical_depth": context_preferences.get("context_depth", 0.7)
            }
            
            # Apply personalization to primary context
            response = await self.personalization_adapter.ainvoke({
                "original_context": integrated.primary_context,
                "user_profile": json.dumps({
                    "expertise": customer_profile.expertise_level.value,
                    "interests": customer_profile.primary_interests,
                    "communication_style": customer_profile.communication_style.value
                }),
                "communication_preferences": json.dumps(comm_prefs)
            })
            
            adaptation_result = json.loads(response.content)
            
            # Update integrated context
            integrated.primary_context = adaptation_result["adapted_context"]
            integrated.adaptation_notes.extend(adaptation_result.get("adaptations_made", []))
            integrated.metadata["personalization_score"] = adaptation_result.get("personalization_score", 0.5)
            integrated.metadata["personalization_reasoning"] = adaptation_result.get("reasoning", "")
            
            return integrated
            
        except Exception as e:
            logger.error(f"Error applying personalization: {e}")
            return integrated  # Return unchanged if personalization fails
    
    async def _compress_context(self, integrated: IntegratedContext, customer_profile: CustomerProfile) -> IntegratedContext:
        """Compress context to fit within token limits."""
        target_tokens = int(self.max_context_tokens * 0.9)  # 90% of max to leave buffer
        
        if integrated.total_tokens <= target_tokens:
            return integrated
        
        try:
            # Prepare user priorities based on profile
            user_priorities = [
                f"Expertise level: {customer_profile.expertise_level.value}",
                f"Communication style: {customer_profile.communication_style.value}",
                f"Primary interests: {', '.join(customer_profile.primary_interests[:3])}"
            ]
            
            # Compress primary context
            context_to_compress = integrated.primary_context
            if integrated.supporting_contexts:
                context_to_compress += "\n\nSupporting information:\n" + "\n".join(integrated.supporting_contexts)
            
            response = await self.context_compressor.ainvoke({
                "context": context_to_compress,
                "current_tokens": integrated.total_tokens,
                "target_tokens": target_tokens,
                "user_priorities": "; ".join(user_priorities)
            })
            
            compressed_context = response.content.strip()
            
            # Update integrated context
            integrated.primary_context = compressed_context
            integrated.supporting_contexts = []  # Merged into primary
            integrated.total_tokens = len(compressed_context.split()) * 1.3  # Re-estimate
            integrated.adaptation_notes.append("context_compressed")
            integrated.metadata["compression_ratio"] = integrated.total_tokens / target_tokens
            
            logger.info(f"Compressed context: {integrated.total_tokens} -> {integrated.total_tokens} tokens")
            return integrated
            
        except Exception as e:
            logger.error(f"Error compressing context: {e}")
            # Fallback: truncate primary context
            words = integrated.primary_context.split()
            if len(words) > target_tokens * 0.7:  # Rough word to token conversion
                truncated_words = words[:int(target_tokens * 0.7)]
                integrated.primary_context = " ".join(truncated_words) + "..."
                integrated.supporting_contexts = []
                integrated.total_tokens = len(truncated_words) * 1.3
                integrated.adaptation_notes.append("context_truncated")
            
            return integrated
    
    def get_context_summary(self, integrated: IntegratedContext) -> Dict[str, Any]:
        """Get summary of integrated context."""
        return {
            "total_tokens": integrated.total_tokens,
            "confidence_score": integrated.confidence_score,
            "source_distribution": {source.value: weight for source, weight in integrated.context_sources.items()},
            "adaptations_applied": integrated.adaptation_notes,
            "personalization_score": integrated.metadata.get("personalization_score", 0.0),
            "supporting_contexts_count": len(integrated.supporting_contexts),
            "compression_applied": "compression_ratio" in integrated.metadata,
            "synthesis_method": integrated.metadata.get("method", "llm_synthesis")
        }
    
    def evaluate_context_quality(self, integrated: IntegratedContext, query_analysis: QueryAnalysis) -> Dict[str, float]:
        """Evaluate the quality of integrated context."""
        scores = {}
        
        # Coverage score - how well context covers query topics
        query_keywords = set(query_analysis.keywords)
        context_text = integrated.primary_context.lower()
        covered_keywords = sum(1 for keyword in query_keywords if keyword.lower() in context_text)
        scores["coverage"] = covered_keywords / len(query_keywords) if query_keywords else 0.0
        
        # Relevance score - based on confidence and source weights
        scores["relevance"] = integrated.confidence_score
        
        # Completeness score - based on source diversity
        source_diversity = len(integrated.context_sources) / len(ContextSource)
        scores["completeness"] = min(source_diversity * 2, 1.0)  # Cap at 1.0
        
        # Personalization score
        scores["personalization"] = integrated.metadata.get("personalization_score", 0.5)
        
        # Efficiency score - token usage efficiency
        token_efficiency = min(integrated.total_tokens / self.max_context_tokens, 1.0)
        scores["efficiency"] = 1.0 - token_efficiency  # Lower token usage = higher efficiency
        
        # Overall quality score
        weights = {"coverage": 0.3, "relevance": 0.25, "completeness": 0.2, "personalization": 0.15, "efficiency": 0.1}
        scores["overall"] = sum(scores[metric] * weight for metric, weight in weights.items())
        
        return scores
    
    def adapt_integration_strategy(self, performance_metrics: Dict[str, float]) -> None:
        """Adapt integration strategy based on performance metrics."""
        # Adjust source weights based on performance
        if performance_metrics.get("coverage", 0) < 0.6:
            # Increase knowledge base weight for better coverage
            self.source_weights[ContextSource.KNOWLEDGE_BASE] = min(0.5, self.source_weights[ContextSource.KNOWLEDGE_BASE] * 1.1)
        
        if performance_metrics.get("personalization", 0) < 0.7:
            # Increase user profile weight for better personalization
            self.source_weights[ContextSource.USER_PROFILE] = min(0.3, self.source_weights[ContextSource.USER_PROFILE] * 1.2)
        
        # Adjust context limits based on efficiency
        if performance_metrics.get("efficiency", 0) < 0.5:
            self.max_context_pieces = max(4, self.max_context_pieces - 1)
        elif performance_metrics.get("efficiency", 0) > 0.8:
            self.max_context_pieces = min(10, self.max_context_pieces + 1)
        
        # Normalize source weights
        total_weight = sum(self.source_weights.values())
        if total_weight > 0:
            self.source_weights = {source: weight/total_weight for source, weight in self.source_weights.items()}
        
        logger.info(f"Adapted integration strategy: {self.source_weights}")
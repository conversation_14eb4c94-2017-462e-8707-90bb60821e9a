"""
Uncertainty Detector
Detect when the system lacks sufficient information and should express uncertainty
"""

from typing import Dict, List, Any, Optional, Tuple, Set
import logging
import re
import asyncio
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import json
import numpy as np
from collections import defaultdict

class UncertaintyLevel(Enum):
    """Levels of uncertainty in responses."""
    VERY_LOW = "very_low"      # 0.0-0.2: Very confident
    LOW = "low"                # 0.2-0.4: Mostly confident
    MEDIUM = "medium"          # 0.4-0.6: Some uncertainty
    HIGH = "high"              # 0.6-0.8: Significant uncertainty
    VERY_HIGH = "very_high"    # 0.8-1.0: Very uncertain

class UncertaintySource(Enum):
    """Sources of uncertainty in the system."""
    INSUFFICIENT_CONTEXT = "insufficient_context"
    CONFLICTING_SOURCES = "conflicting_sources"
    LOW_RETRIEVAL_CONFIDENCE = "low_retrieval_confidence"
    AMBIGUOUS_QUERY = "ambiguous_query"
    OUTDATED_INFORMATION = "outdated_information"
    DOMAIN_MISMATCH = "domain_mismatch"
    INCOMPLETE_KNOWLEDGE = "incomplete_knowledge"

@dataclass
class UncertaintySignal:
    """Individual uncertainty signal detected in the system."""
    
    signal_type: UncertaintySource
    confidence_impact: float  # How much this reduces overall confidence (0-1)
    description: str
    evidence: str = ""
    severity: str = "medium"  # low, medium, high
    
    # Context information
    query_segment: str = ""
    source_context: str = ""
    detected_at: datetime = field(default_factory=datetime.now)

@dataclass
class UncertaintyAssessment:
    """Complete uncertainty assessment for a query/response."""
    
    query: str
    overall_uncertainty: float  # 0-1 scale
    uncertainty_level: UncertaintyLevel
    
    # Detected signals
    uncertainty_signals: List[UncertaintySignal] = field(default_factory=list)
    
    # Recommendations
    should_express_uncertainty: bool = False
    recommended_response_type: str = "standard"  # standard, hedged, redirect, escalate
    suggested_phrases: List[str] = field(default_factory=list)
    
    # Context analysis
    retrieval_confidence: float = 0.0
    source_agreement: float = 0.0
    query_clarity: float = 0.0
    
    # Metadata
    assessment_time: datetime = field(default_factory=datetime.now)
    model_version: str = "1.0.0"

class UncertaintyDetector:
    """Detect and assess uncertainty in query processing and response generation."""
    
    def __init__(self, config=None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Uncertainty thresholds
        self.thresholds = {
            "express_uncertainty": 0.6,
            "recommend_escalation": 0.8,
            "min_retrieval_confidence": 0.4,
            "min_source_agreement": 0.5,
            "min_query_clarity": 0.3
        }
        
        # Uncertainty indicators
        self.uncertainty_patterns = self._initialize_uncertainty_patterns()
        
        # Confidence phrases for responses
        self.confidence_phrases = self._initialize_confidence_phrases()
        
        # Assessment cache
        self.assessment_cache: Dict[str, UncertaintyAssessment] = {}
    
    def _initialize_uncertainty_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns that indicate uncertainty."""
        
        return {
            "query_ambiguity": [
                r"\b(maybe|perhaps|possibly|might|could be|not sure)\b",
                r"\b(what if|suppose|assuming|unclear|vague)\b",
                r"\?\s*\?",  # Multiple question marks
                r"\b(or|either|alternative)\b.*\b(or|either|alternative)\b"
            ],
            "insufficient_context": [
                r"\b(more information|additional details|specific|exactly)\b",
                r"\b(which|what kind|what type|how many|when exactly)\b",
                r"\b(depends on|varies|different)\b"
            ],
            "conflicting_information": [
                r"\b(however|but|although|contradicts|conflicts)\b",
                r"\b(on the other hand|alternatively|different sources)\b",
                r"\b(disagree|dispute|inconsistent)\b"
            ],
            "temporal_uncertainty": [
                r"\b(outdated|old|recent|current|latest|up to date)\b",
                r"\b(as of|since|until|before|after)\b.*\d{4}",
                r"\b(may have changed|might be different|could be updated)\b"
            ],
            "domain_uncertainty": [
                r"\b(outside my expertise|not familiar|specialized|technical)\b",
                r"\b(consult|expert|specialist|professional)\b",
                r"\b(beyond|outside|not covered)\b"
            ]
        }
    
    def _initialize_confidence_phrases(self) -> Dict[str, List[str]]:
        """Initialize phrases for expressing different confidence levels."""
        
        return {
            "high_confidence": [
                "Based on the available information",
                "According to the documentation",
                "The evidence clearly shows",
                "It's well established that"
            ],
            "medium_confidence": [
                "It appears that",
                "The information suggests",
                "Based on what I can find",
                "From the available sources"
            ],
            "low_confidence": [
                "I'm not entirely certain, but",
                "Based on limited information",
                "It's possible that",
                "There are indications that"
            ],
            "very_low_confidence": [
                "I don't have enough information to",
                "I'm unable to provide a definitive answer",
                "This requires more specific information",
                "I'd recommend consulting"
            ],
            "uncertainty_expressions": [
                "I'm not sure about",
                "This is unclear from the available information",
                "There might be additional factors",
                "This could depend on specific circumstances"
            ]
        }
    
    async def assess_uncertainty(self, 
                               query: str, 
                               retrieved_documents: List[Dict[str, Any]], 
                               retrieval_scores: List[float] = None,
                               context: Optional[Dict[str, Any]] = None) -> UncertaintyAssessment:
        """Perform comprehensive uncertainty assessment."""
        
        try:
            # Initialize assessment
            assessment = UncertaintyAssessment(query=query)
            
            # Analyze query clarity
            assessment.query_clarity = await self._assess_query_clarity(query)
            
            # Analyze retrieval confidence
            assessment.retrieval_confidence = await self._assess_retrieval_confidence(
                retrieved_documents, retrieval_scores
            )
            
            # Analyze source agreement
            assessment.source_agreement = await self._assess_source_agreement(
                retrieved_documents
            )
            
            # Detect uncertainty signals
            uncertainty_signals = await self._detect_uncertainty_signals(
                query, retrieved_documents, context
            )
            assessment.uncertainty_signals = uncertainty_signals
            
            # Calculate overall uncertainty
            assessment.overall_uncertainty = await self._calculate_overall_uncertainty(
                assessment
            )
            
            # Determine uncertainty level
            assessment.uncertainty_level = self._get_uncertainty_level(
                assessment.overall_uncertainty
            )
            
            # Generate recommendations
            await self._generate_recommendations(assessment)
            
            # Cache assessment
            query_hash = str(hash(query))
            self.assessment_cache[query_hash] = assessment
            
            return assessment
            
        except Exception as e:
            self.logger.error(f"Uncertainty assessment failed: {e}")
            return UncertaintyAssessment(
                query=query,
                overall_uncertainty=0.5,
                uncertainty_level=UncertaintyLevel.MEDIUM,
                recommended_response_type="standard"
            )
    
    async def _assess_query_clarity(self, query: str) -> float:
        """Assess how clear and specific the query is."""
        
        clarity_score = 1.0
        
        # Check for ambiguous patterns
        for pattern_type, patterns in self.uncertainty_patterns.items():
            if pattern_type == "query_ambiguity":
                for pattern in patterns:
                    matches = len(re.findall(pattern, query, re.IGNORECASE))
                    clarity_score -= matches * 0.1
        
        # Check query length (very short or very long queries are often unclear)
        word_count = len(query.split())
        if word_count < 3:
            clarity_score -= 0.3
        elif word_count > 50:
            clarity_score -= 0.2
        
        # Check for question words (good for clarity)
        question_words = ["what", "how", "why", "when", "where", "who", "which"]
        has_question_words = any(word in query.lower() for word in question_words)
        if has_question_words:
            clarity_score += 0.1
        
        # Check for specific terms vs. vague terms
        specific_indicators = ["specific", "exactly", "precisely", "detailed"]
        vague_indicators = ["something", "anything", "general", "overall"]
        
        specific_count = sum(1 for word in specific_indicators if word in query.lower())
        vague_count = sum(1 for word in vague_indicators if word in query.lower())
        
        clarity_score += specific_count * 0.1
        clarity_score -= vague_count * 0.1
        
        return max(0.0, min(1.0, clarity_score))
    
    async def _assess_retrieval_confidence(self, 
                                         documents: List[Dict[str, Any]], 
                                         scores: List[float] = None) -> float:
        """Assess confidence in retrieved documents."""
        
        if not documents:
            return 0.0
        
        # Use provided scores if available
        if scores and len(scores) == len(documents):
            avg_score = sum(scores) / len(scores)
            return avg_score
        
        # Fallback: assess based on document metadata
        confidence_sum = 0.0
        
        for doc in documents:
            doc_confidence = 1.0
            
            # Check document completeness
            content_length = len(doc.get("content", ""))
            if content_length < 100:
                doc_confidence -= 0.3
            
            # Check for metadata quality
            metadata = doc.get("metadata", {})
            if not metadata.get("title"):
                doc_confidence -= 0.1
            if not metadata.get("source"):
                doc_confidence -= 0.1
            
            # Check for recency (if timestamp available)
            timestamp = metadata.get("timestamp")
            if timestamp:
                # Simple recency check (placeholder)
                doc_confidence += 0.1
            
            confidence_sum += doc_confidence
        
        return confidence_sum / len(documents)
    
    async def _assess_source_agreement(self, documents: List[Dict[str, Any]]) -> float:
        """Assess how much the sources agree with each other."""
        
        if len(documents) < 2:
            return 1.0  # Single source = no disagreement
        
        # Simple keyword-based agreement assessment
        all_keywords = []
        doc_keywords = []
        
        for doc in documents:
            content = doc.get("content", "").lower()
            keywords = set(re.findall(r'\b\w{4,}\b', content))
            doc_keywords.append(keywords)
            all_keywords.extend(keywords)
        
        if not all_keywords:
            return 0.5
        
        # Calculate pairwise agreement
        agreements = []
        for i in range(len(doc_keywords)):
            for j in range(i + 1, len(doc_keywords)):
                keywords1 = doc_keywords[i]
                keywords2 = doc_keywords[j]
                
                if keywords1 and keywords2:
                    intersection = len(keywords1 & keywords2)
                    union = len(keywords1 | keywords2)
                    agreement = intersection / union if union > 0 else 0
                    agreements.append(agreement)
        
        return sum(agreements) / len(agreements) if agreements else 0.5
    
    async def _detect_uncertainty_signals(self, 
                                        query: str, 
                                        documents: List[Dict[str, Any]], 
                                        context: Optional[Dict[str, Any]]) -> List[UncertaintySignal]:
        """Detect specific uncertainty signals."""
        
        signals = []
        
        # Check for query ambiguity signals
        for pattern in self.uncertainty_patterns["query_ambiguity"]:
            matches = re.finditer(pattern, query, re.IGNORECASE)
            for match in matches:
                signals.append(UncertaintySignal(
                    signal_type=UncertaintySource.AMBIGUOUS_QUERY,
                    confidence_impact=0.2,
                    description="Ambiguous language detected in query",
                    evidence=match.group(),
                    query_segment=query[max(0, match.start()-20):match.end()+20]
                ))
        
        # Check for insufficient context signals
        if len(documents) < 2:
            signals.append(UncertaintySignal(
                signal_type=UncertaintySource.INSUFFICIENT_CONTEXT,
                confidence_impact=0.3,
                description="Very few relevant documents found",
                evidence=f"Only {len(documents)} documents retrieved"
            ))
        
        # Check for conflicting information
        conflicting_patterns = self.uncertainty_patterns["conflicting_information"]
        for doc in documents:
            content = doc.get("content", "")
            for pattern in conflicting_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    signals.append(UncertaintySignal(
                        signal_type=UncertaintySource.CONFLICTING_SOURCES,
                        confidence_impact=0.25,
                        description="Conflicting information detected in sources",
                        evidence=pattern,
                        source_context=content[:200]
                    ))
                    break  # One signal per document
        
        # Check for domain uncertainty
        domain_patterns = self.uncertainty_patterns["domain_uncertainty"]
        for pattern in domain_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                signals.append(UncertaintySignal(
                    signal_type=UncertaintySource.DOMAIN_MISMATCH,
                    confidence_impact=0.4,
                    description="Query may be outside system expertise",
                    evidence=pattern
                ))
        
        # Check for temporal uncertainty
        temporal_patterns = self.uncertainty_patterns["temporal_uncertainty"]
        for doc in documents:
            content = doc.get("content", "")
            for pattern in temporal_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    signals.append(UncertaintySignal(
                        signal_type=UncertaintySource.OUTDATED_INFORMATION,
                        confidence_impact=0.15,
                        description="Information may be outdated",
                        evidence=pattern,
                        source_context=content[:200]
                    ))
                    break
        
        return signals

    async def _calculate_overall_uncertainty(self, assessment: UncertaintyAssessment) -> float:
        """Calculate overall uncertainty score."""

        # Base uncertainty from individual factors
        base_uncertainty = 0.0

        # Query clarity impact (inverted - low clarity = high uncertainty)
        base_uncertainty += (1.0 - assessment.query_clarity) * 0.3

        # Retrieval confidence impact (inverted)
        base_uncertainty += (1.0 - assessment.retrieval_confidence) * 0.3

        # Source agreement impact (inverted)
        base_uncertainty += (1.0 - assessment.source_agreement) * 0.2

        # Add uncertainty from detected signals
        signal_uncertainty = 0.0
        for signal in assessment.uncertainty_signals:
            signal_uncertainty += signal.confidence_impact

        # Combine with diminishing returns
        signal_uncertainty = min(0.8, signal_uncertainty)  # Cap at 0.8

        # Final uncertainty calculation
        total_uncertainty = base_uncertainty + signal_uncertainty * 0.2

        return min(1.0, total_uncertainty)

    def _get_uncertainty_level(self, uncertainty_score: float) -> UncertaintyLevel:
        """Convert uncertainty score to level."""

        if uncertainty_score < 0.2:
            return UncertaintyLevel.VERY_LOW
        elif uncertainty_score < 0.4:
            return UncertaintyLevel.LOW
        elif uncertainty_score < 0.6:
            return UncertaintyLevel.MEDIUM
        elif uncertainty_score < 0.8:
            return UncertaintyLevel.HIGH
        else:
            return UncertaintyLevel.VERY_HIGH

    async def _generate_recommendations(self, assessment: UncertaintyAssessment):
        """Generate recommendations based on uncertainty assessment."""

        uncertainty = assessment.overall_uncertainty

        # Determine if uncertainty should be expressed
        assessment.should_express_uncertainty = uncertainty >= self.thresholds["express_uncertainty"]

        # Determine response type
        if uncertainty >= self.thresholds["recommend_escalation"]:
            assessment.recommended_response_type = "escalate"
        elif uncertainty >= self.thresholds["express_uncertainty"]:
            assessment.recommended_response_type = "hedged"
        elif uncertainty >= 0.4:
            assessment.recommended_response_type = "qualified"
        else:
            assessment.recommended_response_type = "standard"

        # Generate suggested phrases
        assessment.suggested_phrases = await self._get_suggested_phrases(assessment)

    async def _get_suggested_phrases(self, assessment: UncertaintyAssessment) -> List[str]:
        """Get suggested phrases for expressing uncertainty."""

        phrases = []
        level = assessment.uncertainty_level

        if level == UncertaintyLevel.VERY_HIGH:
            phrases.extend(self.confidence_phrases["very_low_confidence"])
            phrases.extend(self.confidence_phrases["uncertainty_expressions"])
        elif level == UncertaintyLevel.HIGH:
            phrases.extend(self.confidence_phrases["low_confidence"])
            phrases.extend(self.confidence_phrases["uncertainty_expressions"][:2])
        elif level == UncertaintyLevel.MEDIUM:
            phrases.extend(self.confidence_phrases["medium_confidence"])
        elif level == UncertaintyLevel.LOW:
            phrases.extend(self.confidence_phrases["medium_confidence"][:2])
        else:  # VERY_LOW
            phrases.extend(self.confidence_phrases["high_confidence"])

        # Add specific phrases based on uncertainty sources
        for signal in assessment.uncertainty_signals:
            if signal.signal_type == UncertaintySource.INSUFFICIENT_CONTEXT:
                phrases.append("I need more specific information to provide a complete answer")
            elif signal.signal_type == UncertaintySource.CONFLICTING_SOURCES:
                phrases.append("There are different perspectives on this topic")
            elif signal.signal_type == UncertaintySource.DOMAIN_MISMATCH:
                phrases.append("This may require specialized expertise")
            elif signal.signal_type == UncertaintySource.OUTDATED_INFORMATION:
                phrases.append("Please verify this information is current")

        return list(set(phrases))  # Remove duplicates

    async def should_escalate(self, assessment: UncertaintyAssessment) -> bool:
        """Determine if the query should be escalated to human agents."""

        # High uncertainty threshold
        if assessment.overall_uncertainty >= self.thresholds["recommend_escalation"]:
            return True

        # Specific escalation triggers
        escalation_signals = [
            UncertaintySource.DOMAIN_MISMATCH,
            UncertaintySource.CONFLICTING_SOURCES
        ]

        for signal in assessment.uncertainty_signals:
            if (signal.signal_type in escalation_signals and
                signal.confidence_impact >= 0.3):
                return True

        # Multiple uncertainty signals
        if len(assessment.uncertainty_signals) >= 3:
            return True

        return False

    async def get_confidence_modifier(self, assessment: UncertaintyAssessment) -> str:
        """Get a confidence modifier phrase for the response."""

        level = assessment.uncertainty_level

        modifiers = {
            UncertaintyLevel.VERY_LOW: "",
            UncertaintyLevel.LOW: "Based on available information, ",
            UncertaintyLevel.MEDIUM: "From what I can determine, ",
            UncertaintyLevel.HIGH: "Based on limited information, ",
            UncertaintyLevel.VERY_HIGH: "I'm not entirely certain, but "
        }

        return modifiers.get(level, "")

    async def enhance_response_with_uncertainty(self,
                                              response: str,
                                              assessment: UncertaintyAssessment) -> str:
        """Enhance response with appropriate uncertainty expressions."""

        if not assessment.should_express_uncertainty:
            return response

        # Add confidence modifier at the beginning
        modifier = await self.get_confidence_modifier(assessment)
        enhanced_response = modifier + response

        # Add uncertainty expressions based on signals
        uncertainty_notes = []

        for signal in assessment.uncertainty_signals:
            if signal.signal_type == UncertaintySource.INSUFFICIENT_CONTEXT:
                uncertainty_notes.append("Please provide more specific details for a more accurate answer.")
            elif signal.signal_type == UncertaintySource.CONFLICTING_SOURCES:
                uncertainty_notes.append("Note that there may be different approaches or perspectives on this topic.")
            elif signal.signal_type == UncertaintySource.OUTDATED_INFORMATION:
                uncertainty_notes.append("Please verify that this information is current for your specific situation.")
            elif signal.signal_type == UncertaintySource.DOMAIN_MISMATCH:
                uncertainty_notes.append("For specialized guidance, consider consulting with a domain expert.")

        # Add notes to response
        if uncertainty_notes:
            enhanced_response += "\n\n" + " ".join(uncertainty_notes)

        # Add escalation suggestion if needed
        if await self.should_escalate(assessment):
            enhanced_response += "\n\nFor more detailed assistance, you may want to contact our support team."

        return enhanced_response

    async def get_uncertainty_explanation(self, assessment: UncertaintyAssessment) -> Dict[str, Any]:
        """Get detailed explanation of uncertainty assessment."""

        explanation = {
            "overall_uncertainty": assessment.overall_uncertainty,
            "uncertainty_level": assessment.uncertainty_level.value,
            "should_express_uncertainty": assessment.should_express_uncertainty,
            "should_escalate": await self.should_escalate(assessment),
            "factors": {
                "query_clarity": assessment.query_clarity,
                "retrieval_confidence": assessment.retrieval_confidence,
                "source_agreement": assessment.source_agreement
            },
            "signals": [],
            "recommendations": {
                "response_type": assessment.recommended_response_type,
                "suggested_phrases": assessment.suggested_phrases
            }
        }

        # Add signal details
        for signal in assessment.uncertainty_signals:
            explanation["signals"].append({
                "type": signal.signal_type.value,
                "impact": signal.confidence_impact,
                "description": signal.description,
                "evidence": signal.evidence
            })

        return explanation

    async def batch_assess_uncertainty(self,
                                     queries: List[str],
                                     document_sets: List[List[Dict[str, Any]]]) -> List[UncertaintyAssessment]:
        """Assess uncertainty for multiple queries in batch."""

        assessments = []

        for i, query in enumerate(queries):
            documents = document_sets[i] if i < len(document_sets) else []
            assessment = await self.assess_uncertainty(query, documents)
            assessments.append(assessment)

        return assessments

    async def get_uncertainty_statistics(self) -> Dict[str, Any]:
        """Get statistics about uncertainty assessments."""

        if not self.assessment_cache:
            return {"message": "No assessments in cache"}

        assessments = list(self.assessment_cache.values())

        stats = {
            "total_assessments": len(assessments),
            "average_uncertainty": sum(a.overall_uncertainty for a in assessments) / len(assessments),
            "uncertainty_distribution": defaultdict(int),
            "signal_frequency": defaultdict(int),
            "escalation_rate": 0.0,
            "expression_rate": 0.0
        }

        escalations = 0
        expressions = 0

        for assessment in assessments:
            # Count uncertainty levels
            stats["uncertainty_distribution"][assessment.uncertainty_level.value] += 1

            # Count signals
            for signal in assessment.uncertainty_signals:
                stats["signal_frequency"][signal.signal_type.value] += 1

            # Count escalations and expressions
            if await self.should_escalate(assessment):
                escalations += 1
            if assessment.should_express_uncertainty:
                expressions += 1

        stats["escalation_rate"] = escalations / len(assessments)
        stats["expression_rate"] = expressions / len(assessments)

        return stats

    def clear_cache(self):
        """Clear the assessment cache."""
        self.assessment_cache.clear()
        self.logger.info("Uncertainty assessment cache cleared")

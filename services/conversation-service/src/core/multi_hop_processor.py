"""
Multi-Hop Query Processing System
Handles complex queries requiring multiple retrieval rounds and answer synthesis.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import asyncio
from datetime import datetime

from .query_decomposer import QueryDecomposer, DecompositionResult, SubQuery
from .semantic_retriever import SemanticRetriever
from .response_generator import ResponseGenerator
from .multi_source_synthesis import MultiSourceSynthesis
from ..models.query_analysis import QueryAnalysis

@dataclass
class SubQueryResult:
    """Result of processing a single sub-query."""
    sub_query: SubQuery
    retrieved_documents: List[Dict[str, Any]]
    generated_answer: str
    confidence_score: float
    processing_time: float
    sources_used: List[str]

@dataclass
class MultiHopResult:
    """Complete result of multi-hop processing."""
    original_query: str
    decomposition: DecompositionResult
    sub_results: List[SubQueryResult]
    final_synthesis: str
    overall_confidence: float
    total_processing_time: float
    execution_path: List[str]
    sources_used: List[str]

class MultiHopProcessor:
    """Processes complex queries through multiple retrieval and reasoning hops."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.query_decomposer = QueryDecomposer(config)
        self.retriever = SemanticRetriever(config)
        self.response_generator = ResponseGenerator(config)
        self.synthesizer = MultiSourceSynthesis(config)
        
        # Processing settings
        self.max_hops = getattr(config, 'max_multi_hop_rounds', 5)
        self.parallel_processing = getattr(config, 'enable_parallel_processing', True)
        self.confidence_threshold = getattr(config, 'multi_hop_confidence_threshold', 0.6)

    async def process_multi_hop_query(
        self,
        query: str,
        query_analysis: QueryAnalysis,
        conversation_context: Optional[Dict] = None,
        user_profile: Optional[Dict] = None
    ) -> MultiHopResult:
        """Process a complex query through multiple hops."""
        
        start_time = datetime.now()
        execution_path = []
        
        try:
            # Step 1: Decompose the query
            self.logger.info(f"Starting multi-hop processing for: {query[:100]}...")
            execution_path.append("query_decomposition")
            
            decomposition = await self.query_decomposer.decompose_query(
                query, query_analysis, conversation_context
            )
            
            # If no decomposition needed, process as single query
            if not decomposition.sub_queries:
                execution_path.append("single_hop_fallback")
                return await self._process_single_hop(
                    query, query_analysis, conversation_context, user_profile, execution_path, start_time
                )
            
            self.logger.info(f"Decomposed into {len(decomposition.sub_queries)} sub-queries")
            
            # Step 2: Process sub-queries
            execution_path.append("sub_query_processing")
            sub_results = await self._process_sub_queries(
                decomposition, conversation_context, user_profile, execution_path
            )
            
            # Step 3: Synthesize final answer
            execution_path.append("final_synthesis")
            final_answer, overall_confidence = await self._synthesize_final_answer(
                query, decomposition, sub_results, query_analysis
            )
            
            # Calculate metrics
            total_time = (datetime.now() - start_time).total_seconds()
            all_sources = []
            for result in sub_results:
                all_sources.extend(result.sources_used)
            
            execution_path.append("completion")
            
            return MultiHopResult(
                original_query=query,
                decomposition=decomposition,
                sub_results=sub_results,
                final_synthesis=final_answer,
                overall_confidence=overall_confidence,
                total_processing_time=total_time,
                execution_path=execution_path,
                sources_used=list(set(all_sources))
            )
            
        except Exception as e:
            self.logger.error(f"Multi-hop processing failed: {e}")
            execution_path.append(f"error: {str(e)}")
            
            # Fallback to single hop
            return await self._process_single_hop(
                query, query_analysis, conversation_context, user_profile, execution_path, start_time
            )

    async def _process_sub_queries(
        self,
        decomposition: DecompositionResult,
        conversation_context: Optional[Dict],
        user_profile: Optional[Dict],
        execution_path: List[str]
    ) -> List[SubQueryResult]:
        """Process all sub-queries according to execution strategy."""
        
        # Optimize execution order
        execution_batches = self.query_decomposer.optimize_execution_order(decomposition.sub_queries)
        
        all_results = []
        accumulated_context = conversation_context or {}
        
        # Process batches sequentially, queries within batch in parallel
        for batch_idx, batch in enumerate(execution_batches):
            execution_path.append(f"batch_{batch_idx+1}")
            
            if self.parallel_processing and len(batch) > 1:
                # Process batch in parallel
                batch_tasks = [
                    self._process_single_sub_query(sq, accumulated_context, user_profile, all_results)
                    for sq in batch
                ]
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                
                # Filter out exceptions
                valid_results = [r for r in batch_results if isinstance(r, SubQueryResult)]
                all_results.extend(valid_results)
            else:
                # Process batch sequentially
                for sq in batch:
                    result = await self._process_single_sub_query(sq, accumulated_context, user_profile, all_results)
                    all_results.append(result)
            
            # Update accumulated context with new results
            for result in all_results[-len(batch):]:
                accumulated_context[f"sub_answer_{result.sub_query.id}"] = result.generated_answer
        
        return all_results

    async def _process_single_sub_query(
        self,
        sub_query: SubQuery,
        context: Dict,
        user_profile: Optional[Dict],
        previous_results: List[SubQueryResult]
    ) -> SubQueryResult:
        """Process a single sub-query."""
        
        start_time = datetime.now()
        
        try:
            # Build enhanced context for this sub-query
            enhanced_context = context.copy()
            
            # Add relevant previous answers
            for dep_id in sub_query.dependencies:
                for prev_result in previous_results:
                    if prev_result.sub_query.id == dep_id:
                        enhanced_context[f"dependency_{dep_id}"] = prev_result.generated_answer
            
            # Retrieve relevant documents
            retrieval_results = await self.retriever.retrieve_documents(
                query=sub_query.question,
                max_results=self.config.max_retrieval_results,
                context=enhanced_context
            )
            
            # Generate answer for sub-query
            answer = await self.response_generator.generate_response(
                query=sub_query.question,
                context=retrieval_results.get("context", ""),
                query_analysis=None,  # Create minimal analysis if needed
                conversation_context=enhanced_context
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return SubQueryResult(
                sub_query=sub_query,
                retrieved_documents=retrieval_results.get("documents", []),
                generated_answer=answer,
                confidence_score=retrieval_results.get("confidence", 0.7),
                processing_time=processing_time,
                sources_used=[doc.get("id", "") for doc in retrieval_results.get("documents", [])]
            )
            
        except Exception as e:
            self.logger.error(f"Sub-query processing failed for '{sub_query.question}': {e}")
            
            return SubQueryResult(
                sub_query=sub_query,
                retrieved_documents=[],
                generated_answer=f"Unable to process sub-query: {str(e)}",
                confidence_score=0.0,
                processing_time=(datetime.now() - start_time).total_seconds(),
                sources_used=[]
            )

    async def _synthesize_final_answer(
        self,
        original_query: str,
        decomposition: DecompositionResult,
        sub_results: List[SubQueryResult],
        query_analysis: QueryAnalysis
    ) -> Tuple[str, float]:
        """Synthesize final answer from sub-query results."""
        
        if not sub_results:
            return "Unable to process query due to decomposition failure.", 0.0
        
        # Prepare synthesis input
        synthesis_sources = []
        for result in sub_results:
            synthesis_sources.append({
                "content": result.generated_answer,
                "metadata": {
                    "sub_query": result.sub_query.question,
                    "confidence": result.confidence_score,
                    "sources": result.sources_used
                }
            })
        
        # Use multi-source synthesis
        synthesis_result = await self.synthesizer.synthesize_sources(
            sources=synthesis_sources,
            query=original_query,
            query_analysis=query_analysis
        )
        
        # Calculate overall confidence
        confidence_scores = [r.confidence_score for r in sub_results if r.confidence_score > 0]
        overall_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
        
        return synthesis_result.get("synthesized_content", ""), overall_confidence

    async def _process_single_hop(
        self,
        query: str,
        query_analysis: QueryAnalysis,
        conversation_context: Optional[Dict],
        user_profile: Optional[Dict],
        execution_path: List[str],
        start_time: datetime
    ) -> MultiHopResult:
        """Fallback to single-hop processing."""
        
        execution_path.append("single_hop_processing")
        
        # Standard retrieval and generation
        retrieval_results = await self.retriever.retrieve_documents(
            query=query,
            max_results=self.config.max_retrieval_results,
            context=conversation_context
        )
        
        answer = await self.response_generator.generate_response(
            query=query,
            context=retrieval_results.get("context", ""),
            query_analysis=query_analysis,
            conversation_context=conversation_context
        )
        
        total_time = (datetime.now() - start_time).total_seconds()
        
        return MultiHopResult(
            original_query=query,
            decomposition=DecompositionResult(query, [], "sequential", 1.0, False),
            sub_results=[],
            final_synthesis=answer,
            overall_confidence=retrieval_results.get("confidence", 0.7),
            total_processing_time=total_time,
            execution_path=execution_path,
            sources_used=[doc.get("id", "") for doc in retrieval_results.get("documents", [])]
        )
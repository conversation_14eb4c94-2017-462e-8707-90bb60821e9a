"""
Temperature Testing Utility for RAG System

Tests different temperature settings to find optimal balance between:
- Natural language flow
- Context adherence 
- Response quality
"""

import asyncio
import json
from typing import Dict, List, Any, Tu<PERSON>
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
import logging

class TemperatureTester:
    """Test optimal temperature settings for different query types."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Test temperature ranges
        self.temperature_ranges = {
            "factual": [0.1, 0.2, 0.3, 0.4, 0.5],
            "procedural": [0.1, 0.2, 0.3, 0.4, 0.5],
            "analytical": [0.2, 0.3, 0.4, 0.5],
            "conversational": [0.1, 0.2, 0.3, 0.4],
            "complex": [0.2, 0.3, 0.4, 0.5]
        }
        
        # Test queries for each type
        self.test_queries = {
            "factual": [
                {"query": "What is Python?", "context": "Python is a high-level programming language created by <PERSON> in 1991. It emphasizes code readability and simplicity."},
                {"query": "What is machine learning?", "context": "Machine learning is a subset of artificial intelligence that enables computers to learn without being explicitly programmed."}
            ],
            "procedural": [
                {"query": "How do I install Python?", "context": "To install Python: 1. Go to python.org 2. Download the installer 3. Run the installer 4. Check 'Add to PATH' 5. Click Install"},
                {"query": "How to create a virtual environment?", "context": "Create virtual environment: 1. Open terminal 2. Run 'python -m venv myenv' 3. Activate with 'source myenv/bin/activate' on Mac/Linux or 'myenv\\Scripts\\activate' on Windows"}
            ],
            "analytical": [
                {"query": "Compare Python and Java", "context": "Python: interpreted, dynamic typing, simpler syntax, slower execution. Java: compiled, static typing, verbose syntax, faster execution. Python better for rapid development, Java better for large applications."},
                {"query": "Pros and cons of microservices", "context": "Microservices pros: scalability, technology diversity, fault isolation. Cons: complexity, network overhead, data consistency challenges, operational overhead."}
            ],
            "conversational": [
                {"query": "Hi, can you help me?", "context": "I'm an AI assistant designed to help with questions and tasks. I can provide information, explain concepts, and assist with various topics."},
                {"query": "Thank you for the explanation", "context": "Previous conversation covered Python basics and installation steps. User showed understanding of the concepts."}
            ],
            "complex": [
                {"query": "How does machine learning work in recommendation systems?", "context": "Recommendation systems use ML algorithms like collaborative filtering (user-item interactions), content-based filtering (item features), and hybrid approaches. They analyze user behavior patterns, item characteristics, and similarity measures to predict preferences."},
                {"query": "Explain the relationship between databases and APIs", "context": "APIs serve as interfaces between applications and databases. Database stores data, API provides controlled access methods (GET, POST, PUT, DELETE). API handles authentication, validation, and data transformation. This separation enables security, scalability, and abstraction."}
            ]
        }
    
    async def test_temperature_range(self, query_type: str, temperature_list: List[float]) -> Dict[str, Any]:
        """Test a range of temperatures for a specific query type."""
        results = []
        
        test_cases = self.test_queries.get(query_type, [])
        if not test_cases:
            return {"error": f"No test cases for query type: {query_type}"}
        
        for temp in temperature_list:
            temp_results = []
            
            for test_case in test_cases:
                result = await self._test_single_temperature(
                    query_type, temp, test_case["query"], test_case["context"]
                )
                temp_results.append(result)
            
            results.append({
                "temperature": temp,
                "results": temp_results,
                "avg_adherence": sum(r["context_adherence_score"] for r in temp_results) / len(temp_results),
                "avg_naturalness": sum(r["naturalness_score"] for r in temp_results) / len(temp_results),
                "avg_quality": sum(r["overall_quality"] for r in temp_results) / len(temp_results)
            })
        
        return {
            "query_type": query_type,
            "temperature_results": results,
            "optimal_temperature": self._find_optimal_temperature(results)
        }
    
    async def _test_single_temperature(self, query_type: str, temperature: float, query: str, context: str) -> Dict[str, Any]:
        """Test a single temperature setting."""
        
        # Create LLM with specific temperature
        llm = ChatOpenAI(model="gpt-4o-mini", temperature=temperature)
        
        # Create test prompt
        test_prompt = ChatPromptTemplate.from_messages([
            ("system", f"""You are testing temperature settings for a RAG system.
            
QUERY TYPE: {query_type}
CONTEXT: {context}

INSTRUCTIONS:
1. Use ONLY the provided context
2. Generate a natural, helpful response
3. Do not add external knowledge
4. Maintain appropriate tone for the query type

Generate response:"""),
            ("human", query)
        ])
        
        try:
            response = await test_prompt.ainvoke({"query": query}, config={"llm": llm})
            response_text = response.content.strip()
            
            # Evaluate the response
            evaluation = await self._evaluate_response(response_text, context, query, query_type)
            
            return {
                "temperature": temperature,
                "query": query,
                "response": response_text,
                "context_adherence_score": evaluation["context_adherence"],
                "naturalness_score": evaluation["naturalness"],
                "overall_quality": evaluation["overall_quality"],
                "issues": evaluation["issues"]
            }
            
        except Exception as e:
            self.logger.error(f"Error testing temperature {temperature}: {e}")
            return {
                "temperature": temperature,
                "query": query,
                "response": "ERROR",
                "context_adherence_score": 0,
                "naturalness_score": 0,
                "overall_quality": 0,
                "issues": [str(e)]
            }
    
    async def _evaluate_response(self, response: str, context: str, query: str, query_type: str) -> Dict[str, Any]:
        """Evaluate response quality across different dimensions."""
        
        evaluator_llm = ChatOpenAI(model="gpt-4o", temperature=0.1)
        
        evaluation_prompt = f"""Evaluate this RAG response across multiple dimensions:

QUERY: {query}
QUERY TYPE: {query_type}
CONTEXT PROVIDED: {context}
RESPONSE: {response}

Rate each dimension (1-5 scale):

1. CONTEXT ADHERENCE: Does response stick to provided context?
   - 5: Perfect adherence, no external information
   - 4: Mostly adherent, minor inferences
   - 3: Some adherence, some external knowledge
   - 2: Poor adherence, significant external information
   - 1: No adherence, mostly external knowledge

2. NATURALNESS: How natural and conversational is the response?
   - 5: Very natural, flows well
   - 4: Natural, minor stiffness
   - 3: Somewhat natural
   - 2: Stilted, robotic
   - 1: Very unnatural

3. OVERALL QUALITY: How helpful and appropriate is the response?
   - 5: Excellent, fully addresses query
   - 4: Good, addresses most aspects
   - 3: Adequate, partial answer
   - 2: Poor, misses key points
   - 1: Very poor, irrelevant

Return JSON format:
{{
    "context_adherence": 4,
    "naturalness": 3,
    "overall_quality": 4,
    "issues": ["specific issue 1", "specific issue 2"],
    "strengths": ["strength 1", "strength 2"]
}}"""

        try:
            eval_response = await evaluator_llm.ainvoke(evaluation_prompt)
            return json.loads(eval_response.content)
        except Exception as e:
            self.logger.error(f"Error in evaluation: {e}")
            return {
                "context_adherence": 3,
                "naturalness": 3,
                "overall_quality": 3,
                "issues": ["Evaluation failed"],
                "strengths": []
            }
    
    def _find_optimal_temperature(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Find optimal temperature based on combined scores."""
        
        best_score = 0
        best_temp = 0.2
        best_result = None
        
        for result in results:
            # Weighted score: context adherence is most important
            combined_score = (
                result["avg_adherence"] * 0.5 +  # 50% weight on context adherence
                result["avg_naturalness"] * 0.3 +  # 30% weight on naturalness
                result["avg_quality"] * 0.2  # 20% weight on overall quality
            )
            
            if combined_score > best_score:
                best_score = combined_score
                best_temp = result["temperature"]
                best_result = result
        
        return {
            "optimal_temperature": best_temp,
            "combined_score": best_score,
            "details": best_result
        }
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive temperature testing across all query types."""
        
        self.logger.info("Starting comprehensive temperature testing...")
        
        all_results = {}
        optimal_config = {}
        
        for query_type, temp_range in self.temperature_ranges.items():
            self.logger.info(f"Testing {query_type} queries with temperatures: {temp_range}")
            
            result = await self.test_temperature_range(query_type, temp_range)
            all_results[query_type] = result
            
            optimal_config[query_type] = result["optimal_temperature"]["optimal_temperature"]
        
        # Generate summary
        summary = {
            "test_summary": {
                "total_query_types": len(self.temperature_ranges),
                "temperature_range_tested": "0.1 - 0.5",
                "optimal_configuration": optimal_config
            },
            "detailed_results": all_results,
            "recommendations": self._generate_recommendations(optimal_config, all_results)
        }
        
        self.logger.info("Temperature testing completed!")
        return summary
    
    def _generate_recommendations(self, optimal_config: Dict[str, float], detailed_results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on test results."""
        
        recommendations = []
        
        # Check if any temperatures are consistently high/low
        avg_temp = sum(optimal_config.values()) / len(optimal_config)
        
        if avg_temp < 0.25:
            recommendations.append("Overall low temperatures suggest prioritizing context adherence over naturalness")
        elif avg_temp > 0.4:
            recommendations.append("Higher temperatures needed - consider improving context quality")
        else:
            recommendations.append("Balanced temperature range - good context quality and response naturalness")
        
        # Check for query-type specific patterns
        factual_temp = optimal_config.get("factual", 0.2)
        complex_temp = optimal_config.get("complex", 0.3)
        
        if complex_temp - factual_temp > 0.2:
            recommendations.append("Significant temperature difference between simple and complex queries - adaptive approach recommended")
        
        # Check context adherence scores
        low_adherence_types = []
        for query_type, results in detailed_results.items():
            if results.get("optimal_temperature", {}).get("details", {}).get("avg_adherence", 0) < 4.0:
                low_adherence_types.append(query_type)
        
        if low_adherence_types:
            recommendations.append(f"Consider lower temperatures for {', '.join(low_adherence_types)} to improve context adherence")
        
        recommendations.append("Implement adaptive temperature based on context quality and query complexity")
        recommendations.append("Monitor context adherence scores in production and adjust accordingly")
        
        return recommendations

# Usage example
async def main():
    tester = TemperatureTester()
    results = await tester.run_comprehensive_test()
    
    print("Temperature Testing Results:")
    print("=" * 50)
    print(json.dumps(results["test_summary"], indent=2))
    print("\nRecommendations:")
    for rec in results["recommendations"]:
        print(f"- {rec}")

if __name__ == "__main__":
    asyncio.run(main())
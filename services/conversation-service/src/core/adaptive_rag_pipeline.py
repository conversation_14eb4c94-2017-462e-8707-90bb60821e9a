from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import asyncio
import json
import logging
from datetime import datetime
from langchain_openai import ChatOpenAI

# Import our enhanced components
from .advanced_query_processor import AdvancedQueryProcessor, QueryAnalysis, QueryType, QueryComplexity
from .advanced_context_manager import AdvancedContextManager, ContextualInformation
from .response_quality_manager import ResponseQualityManager, QualityMetrics
from .multi_source_synthesis import MultiSourceSynthesizer, SourceDocument, SynthesizedResponse
from .response_templates import StructuredResponseTemplates, ResponseTemplateType
from .temperature_tester import TemperatureTester
from .prompts import get_prompt_template, build_prompt_variables

@dataclass
class RAGResponse:
    """Complete RAG response with metadata."""
    response: str
    query_analysis: QueryAnalysis
    contextual_info: ContextualInformation
    quality_metrics: QualityMetrics
    retrieval_strategy: Dict[str, Any]
    processing_time: float
    confidence_score: float
    sources: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    # Enhanced synthesis information
    synthesis_info: Optional[SynthesizedResponse] = None
    response_template_used: Optional[str] = None
    structured_format: bool = False

class AdaptiveRAGStrategy:
    """Adaptive retrieval strategy based on query characteristics and performance history."""
    
    def __init__(self):
        self.strategy_history: Dict[str, List[float]] = {}  # Strategy -> success scores
        self.query_type_preferences: Dict[str, str] = {}  # Query type -> preferred strategy
        
    def select_strategy(self, query_analysis: QueryAnalysis, context: str = "") -> Dict[str, Any]:
        """Select optimal retrieval strategy based on query analysis and history."""
        
        # Base strategy from query analysis
        base_strategy = {
            "use_semantic_search": True,
            "use_keyword_search": True,
            "use_hybrid_ranking": True,
            "context_window": 5,
            "retrieval_rounds": 1,
            "reranking_enabled": True,
            "query_expansion": True
        }
        
        # Adapt based on query type
        if query_analysis.query_type == QueryType.PROCEDURAL:
            base_strategy.update({
                "context_window": 7,
                "prefer_sequential": True,
                "boost_step_by_step": True
            })
        elif query_analysis.query_type == QueryType.ANALYTICAL:
            base_strategy.update({
                "context_window": 10,
                "use_comparative_search": True,
                "retrieval_rounds": 2,
                "enable_multi_perspective": True
            })
        elif query_analysis.query_type == QueryType.MULTI_HOP:
            base_strategy.update({
                "retrieval_rounds": 3,
                "use_graph_traversal": True,
                "context_window": 12,
                "enable_reasoning_chain": True
            })
        elif query_analysis.query_type == QueryType.CONVERSATIONAL:
            base_strategy.update({
                "context_window": 3,
                "boost_conversational_context": True,
                "prefer_recent_context": True
            })
        
        # Adapt based on complexity
        if query_analysis.complexity == QueryComplexity.COMPLEX:
            base_strategy["retrieval_rounds"] = max(base_strategy["retrieval_rounds"], 2)
            base_strategy["context_window"] += 2
        elif query_analysis.complexity == QueryComplexity.SIMPLE:
            base_strategy["retrieval_rounds"] = 1
            base_strategy["context_window"] = min(base_strategy["context_window"], 5)
        
        # Adapt based on urgency
        if query_analysis.urgency == "high":
            base_strategy.update({
                "fast_mode": True,
                "retrieval_rounds": 1,
                "skip_reranking": True
            })
        elif query_analysis.urgency == "critical":
            base_strategy.update({
                "fast_mode": True,
                "retrieval_rounds": 1,
                "skip_reranking": True,
                "use_cache_aggressive": True
            })
        
        # Apply learned preferences
        query_type_key = query_analysis.query_type.value
        if query_type_key in self.query_type_preferences:
            preferred_strategy = self.query_type_preferences[query_type_key]
            if preferred_strategy in self.strategy_history:
                avg_success = sum(self.strategy_history[preferred_strategy]) / len(self.strategy_history[preferred_strategy])
                if avg_success > 0.8:  # High success rate
                    base_strategy["strategy_boost"] = preferred_strategy
        
        return base_strategy
    
    def update_strategy_performance(self, strategy: Dict[str, Any], success_score: float):
        """Update strategy performance based on results."""
        strategy_key = json.dumps(sorted(strategy.items()))
        
        if strategy_key not in self.strategy_history:
            self.strategy_history[strategy_key] = []
        
        self.strategy_history[strategy_key].append(success_score)
        
        # Keep only recent history (last 100 entries)
        if len(self.strategy_history[strategy_key]) > 100:
            self.strategy_history[strategy_key] = self.strategy_history[strategy_key][-100:]

class EnhancedRAGPipeline:
    """State-of-the-art RAG pipeline with adaptive strategies and quality assurance."""
    
    def __init__(self, 
                 knowledge_base_url: str = "http://localhost:8002",
                 analytics_url: str = "http://localhost:8005"):
        
        # Core components
        self.query_processor = AdvancedQueryProcessor()
        self.context_manager = AdvancedContextManager()
        self.quality_manager = ResponseQualityManager()
        self.adaptive_strategy = AdaptiveRAGStrategy()
        
        # Enhanced synthesis and templating
        self.multi_source_synthesizer = MultiSourceSynthesizer()
        self.response_templates = StructuredResponseTemplates()
        self.temperature_tester = TemperatureTester()
        
        # Adaptive LLM configuration for context-grounded responses
        self.base_temperature = 0.2  # Conservative base for factual accuracy
        self.llm = ChatOpenAI(model="gpt-4o", temperature=self.base_temperature)
        
        # Temperature ranges by query complexity
        self.temperature_config = {
            "simple": 0.1,      # Very conservative for factual queries
            "intermediate": 0.3, # Slightly more creative for explanations
            "complex": 0.4,     # Balanced for multi-step reasoning
            "conversational": 0.2, # Conservative but natural
            "analytical": 0.3   # Moderate for structured analysis
        }
        
        # Service URLs
        self.knowledge_base_url = knowledge_base_url
        self.analytics_url = analytics_url
        
        # Performance tracking
        self.performance_history: List[Dict[str, Any]] = []
        
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    async def process_query(self, 
                          conversation_id: str,
                          user_message: str,
                          user_profile: Optional[Dict[str, Any]] = None,
                          conversation_context: str = "") -> RAGResponse:
        """Process user query through the enhanced RAG pipeline."""
        
        start_time = datetime.now()
        
        try:
            # Step 1: Advanced query analysis
            self.logger.info(f"Processing query: {user_message[:50]}...")
            query_analysis = await self.query_processor.analyze_query(
                user_message, conversation_context
            )
            
            # Step 2: Select adaptive retrieval strategy
            retrieval_strategy = self.adaptive_strategy.select_strategy(
                query_analysis, conversation_context
            )
            
            # Step 3: Retrieve relevant documents
            retrieved_documents = await self._retrieve_documents(
                query_analysis, retrieval_strategy
            )
            
            # Step 4: Build optimal context
            contextual_info = await self.context_manager.build_optimal_context(
                conversation_id=conversation_id,
                current_message=user_message,
                query_analysis=query_analysis,
                retrieved_documents=retrieved_documents,
                user_profile=user_profile
            )
            
            # Step 5: Generate initial response with synthesis and templates
            initial_response, synthesis_info, template_used = await self._generate_response(
                query_analysis, contextual_info, user_profile, retrieved_documents
            )
            
            # Step 6: Quality validation and improvement
            final_response, quality_metrics = await self.quality_manager.auto_improve_if_needed(
                response=initial_response,
                query=user_message,
                query_type=query_analysis.query_type.value,
                context=contextual_info.primary_context,
                user_profile=user_profile
            )
            
            # Step 7: Calculate processing time and confidence
            processing_time = (datetime.now() - start_time).total_seconds()
            confidence_score = self._calculate_confidence_score(
                query_analysis, contextual_info, quality_metrics
            )
            
            # Step 8: Create response object with enhanced synthesis info
            rag_response = RAGResponse(
                response=final_response,
                query_analysis=query_analysis,
                contextual_info=contextual_info,
                quality_metrics=quality_metrics,
                retrieval_strategy=retrieval_strategy,
                processing_time=processing_time,
                confidence_score=confidence_score,
                sources=retrieved_documents[:5],  # Top 5 sources
                synthesis_info=synthesis_info,
                response_template_used=template_used,
                structured_format=template_used is not None,
                metadata={
                    "conversation_id": conversation_id,
                    "timestamp": datetime.now().isoformat(),
                    "pipeline_version": "enhanced_v2.0",
                    "improvement_applied": quality_metrics.requires_revision,
                    "multi_source_synthesis": synthesis_info is not None,
                    "structured_template": template_used
                }
            )
            
            # Step 9: Update conversation history
            self.context_manager.add_conversation_turn(
                conversation_id=conversation_id,
                user_message=user_message,
                assistant_response=final_response,
                context_used=[contextual_info.primary_context],
                retrieval_quality=quality_metrics.overall_score
            )
            
            # Step 10: Update adaptive strategy performance
            success_score = self._calculate_success_score(quality_metrics, confidence_score)
            self.adaptive_strategy.update_strategy_performance(retrieval_strategy, success_score)
            
            # Step 11: Track performance
            self._track_performance(rag_response)
            
            self.logger.info(f"Query processed successfully in {processing_time:.2f}s with confidence {confidence_score:.2f}")
            
            return rag_response
            
        except Exception as e:
            self.logger.error(f"Error in RAG pipeline: {e}")
            return self._create_error_response(user_message, str(e), start_time)
    
    async def _retrieve_documents(self, 
                                query_analysis: QueryAnalysis, 
                                strategy: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Retrieve documents using adaptive strategy."""
        
        # This would integrate with the knowledge base service
        # For now, return mock data structure
        retrieved_docs = []
        
        # Multi-round retrieval if specified
        rounds = strategy.get("retrieval_rounds", 1)
        queries_to_use = [query_analysis.original_query] + query_analysis.expanded_queries[:2]
        
        for round_num in range(rounds):
            query_set = queries_to_use[round_num:round_num+1] if round_num < len(queries_to_use) else [query_analysis.original_query]
            
            for query in query_set:
                # Simulate retrieval call to knowledge base service
                docs = await self._call_knowledge_base_service(
                    query, 
                    strategy.get("context_window", 5),
                    query_analysis.entities,
                    strategy
                )
                retrieved_docs.extend(docs)
        
        # Remove duplicates and apply strategy-specific filtering
        unique_docs = self._deduplicate_and_filter(retrieved_docs, strategy)
        
        return unique_docs[:strategy.get("context_window", 10)]
    
    async def _call_knowledge_base_service(self, query: str, top_k: int, entities: List[str], strategy: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Call knowledge base service for document retrieval."""
        import httpx
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.knowledge_base_url}/api/v1/search",
                    params={"q": query, "limit": top_k},
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return [
                        {
                            "id": result["id"],
                            "content": result["content"],
                            "title": result["title"],
                            "score": result.get("score", 0.0),
                            "metadata": {
                                **result.get("metadata", {}),
                                "category": result.get("category", "Unknown"),
                                "subcategory": result.get("subcategory"),
                                "tags": result.get("tags", [])
                            }
                        }
                        for result in data.get("results", [])
                    ]
                else:
                    self.logger.warning(f"Knowledge base service returned {response.status_code}")
                    
        except Exception as e:
            self.logger.error(f"Error calling knowledge base service: {e}")
        
        # Fallback to mock data if service fails
        return [
            {
                "id": f"doc_{i}",
                "content": f"Mock content for query: {query}",
                "title": f"Document {i}",
                "score": 0.9 - (i * 0.1),
                "metadata": {"source": "mock", "entities": entities}
            }
            for i in range(min(top_k, 3))
        ]
    
    def _deduplicate_and_filter(self, docs: List[Dict[str, Any]], strategy: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Remove duplicates and apply strategy-specific filtering."""
        seen_ids = set()
        unique_docs = []
        
        for doc in docs:
            if doc.get("id") not in seen_ids:
                seen_ids.add(doc.get("id"))
                
                # Apply strategy-specific filters
                if strategy.get("boost_recent") and "timestamp" in doc.get("metadata", {}):
                    doc["score"] *= 1.2
                
                if strategy.get("prefer_sequential") and "step" in doc.get("content", "").lower():
                    doc["score"] *= 1.3
                
                unique_docs.append(doc)
        
        # Sort by score
        return sorted(unique_docs, key=lambda x: x.get("score", 0), reverse=True)
    
    def _get_optimal_temperature(self, query_analysis: QueryAnalysis, context_quality: float) -> float:
        """Get optimal temperature based on query type and context quality."""
        base_temp = self.temperature_config.get(
            query_analysis.query_type.value.lower(), 
            self.base_temperature
        )
        
        # Adjust based on context quality
        if context_quality < 0.7:
            # Lower temperature for poor context to prevent hallucination
            base_temp *= 0.8
        elif context_quality > 0.9:
            # Slightly higher for excellent context
            base_temp *= 1.1
            
        # Adjust based on query complexity
        if query_analysis.complexity.value == "SIMPLE":
            base_temp *= 0.8  # More conservative for simple queries
        elif query_analysis.complexity.value == "COMPLEX":
            base_temp *= 1.1  # Slightly more creative for complex reasoning
            
        # Ensure within safe bounds (0.1 - 0.5)
        return max(0.1, min(0.5, base_temp))
    
    async def _generate_response(self, query_analysis: QueryAnalysis, contextual_info: ContextualInformation, user_profile: Optional[Dict[str, Any]] = None, retrieved_documents: Optional[List[Dict[str, Any]]] = None) -> Tuple[str, Optional[SynthesizedResponse], Optional[str]]:
        """Generate response using multi-source synthesis and structured templates."""
        
        # Step 1: Check if multi-source synthesis is needed
        synthesis_info = None
        if retrieved_documents and len(retrieved_documents) > 1:
            source_docs = self._convert_to_source_documents(retrieved_documents)
            if len(source_docs) > 1:  # Only synthesize if we have multiple sources
                synthesis_info = await self.multi_source_synthesizer.synthesize_sources(
                    query=query_analysis.original_query,
                    sources=source_docs,
                    query_type=query_analysis.query_type.value
                )
        
        # Step 2: Determine if structured template should be used
        template_type = self._determine_template_type(query_analysis)
        structured_template = None
        if template_type:
            structured_template = self.response_templates.get_template(template_type)
        
        # Step 3: Calculate optimal temperature
        optimal_temp = self._get_optimal_temperature(
            query_analysis, 
            contextual_info.confidence_score
        )
        
        # Create LLM instance with optimal temperature
        adaptive_llm = ChatOpenAI(model="gpt-4o", temperature=optimal_temp)
        
        # Step 4: Choose response generation method
        if structured_template and template_type:
            # Use structured template
            response_content = await self._generate_structured_response(
                structured_template, query_analysis, contextual_info, 
                synthesis_info, optimal_temp
            )
        else:
            # Use traditional prompt template
            prompt_template = get_prompt_template(query_analysis.query_type.value)
            prompt_variables = build_prompt_variables(contextual_info, query_analysis, user_profile, retrieved_documents)
            
            # Enhance with synthesis information if available
            if synthesis_info:
                prompt_variables["synthesized_content"] = synthesis_info.synthesized_content
                prompt_variables["source_citations"] = synthesis_info.source_citations
                prompt_variables["conflicting_info"] = synthesis_info.conflicting_information
            
            # Add context adherence instructions
            prompt_variables["context_adherence_instruction"] = """
CRITICAL: Base your response ONLY on the provided context. Do not add information from your training data.
If the context doesn't contain sufficient information, acknowledge this limitation clearly.
"""
            
            try:
                self.logger.info(f"Generating response with temperature: {optimal_temp}")
                response = await prompt_template.ainvoke(prompt_variables, config={"llm": adaptive_llm})
                
                # Validate context adherence
                response_content = await self._validate_context_adherence(
                    response.content.strip(), 
                    contextual_info.primary_context,
                    query_analysis.original_query
                )
                
            except Exception as e:
                self.logger.error(f"Error generating response with LLM: {e}")
                response_content = self._create_fallback_response(query_analysis, retrieved_documents or [])
        
        return response_content, synthesis_info, template_type
    
    async def _validate_context_adherence(self, response: str, context: str, query: str) -> str:
        """Validate that response stays within provided context boundaries."""
        
        # Create validation LLM with very low temperature
        validator_llm = ChatOpenAI(model="gpt-4o-mini", temperature=0.05)
        
        validation_prompt = """You are a context adherence validator. Check if the response contains information not present in the given context.

CONTEXT:
{context}

QUERY: {query}

RESPONSE TO VALIDATE:
{response}

VALIDATION TASK:
1. Identify any claims in the response not supported by the context
2. Check for hallucinated facts or information
3. Verify all statements have source support

If the response contains unsupported information, return "REVISION_NEEDED" followed by the specific issues.
If the response is well-grounded in context, return "VALIDATED" followed by the original response.

Format: VALIDATED/REVISION_NEEDED: [response or issues]"""

        try:
            validation_result = await validator_llm.ainvoke(
                validation_prompt.format(
                    context=context[:2000],  # Limit context size
                    query=query,
                    response=response
                )
            )
            
            validation_content = validation_result.content.strip()
            
            if validation_content.startswith("REVISION_NEEDED"):
                self.logger.warning(f"Context adherence issues detected: {validation_content}")
                # Return a safer, more conservative response
                return self._create_conservative_response(query, context)
            elif validation_content.startswith("VALIDATED"):
                # Extract the validated response
                return validation_content.replace("VALIDATED:", "").strip() or response
            else:
                # Default to original response if validation format is unclear
                return response
                
        except Exception as e:
            self.logger.error(f"Error in context adherence validation: {e}")
            return response  # Return original if validation fails
    
    def _create_conservative_response(self, query: str, context: str) -> str:
        """Create a conservative response strictly based on context."""
        # Extract key information from context
        context_summary = context[:500] + ("..." if len(context) > 500 else "")
        
        return f"""Based on the available information in the knowledge base:

{context_summary}

I've provided the relevant information I found regarding your query: "{query}". If you need more specific details or have follow-up questions, please let me know and I'll search for additional relevant information."""
    
    def _create_fallback_response(self, query_analysis: QueryAnalysis, retrieved_documents: List[Dict[str, Any]]) -> str:
        """Create a fallback response using retrieved documents when LLM fails."""
        if not retrieved_documents:
            return f"I apologize, but I couldn't find specific information about '{query_analysis.original_query}' in the knowledge base."
        
        best_doc = retrieved_documents[0]
        title = best_doc.get("title", "")
        content = best_doc.get("content", "")
        max_length = 300
        
        if "how" in query_analysis.original_query.lower():
            max_length = 400
            prefix = f"Based on the information about {title}: "
        elif "what is" in query_analysis.original_query.lower() or "define" in query_analysis.original_query.lower():
            prefix = f"{title}: "
        else:
            prefix = f"Regarding your question about '{query_analysis.original_query}', here's relevant information from {title}: "
        
        truncated_content = content[:max_length] + ('...' if len(content) > max_length else '')
        return prefix + truncated_content
    
    def _calculate_confidence_score(self, query_analysis: QueryAnalysis, contextual_info: ContextualInformation, quality_metrics: QualityMetrics) -> float:
        """Calculate overall confidence score for the response."""
        confidence = (
            0.3 * query_analysis.confidence +
            0.3 * contextual_info.confidence_score +
            0.4 * (quality_metrics.overall_score / 5.0)
        )
        
        # Apply adjustments
        if len(contextual_info.supporting_context) > 2:
            confidence += 0.1
        if query_analysis.complexity == QueryComplexity.SIMPLE:
            confidence += 0.05
        if quality_metrics.requires_revision:
            confidence -= 0.1
        
        return max(0.0, min(1.0, confidence))
    
    def _calculate_success_score(self, quality_metrics: QualityMetrics, confidence_score: float) -> float:
        """Calculate success score for strategy adaptation."""
        return (quality_metrics.overall_score / 5.0 + confidence_score) / 2
    
    def _track_performance(self, rag_response: RAGResponse) -> None:
        """Track pipeline performance metrics."""
        performance_entry = {
            "timestamp": datetime.now().isoformat(),
            "processing_time": rag_response.processing_time,
            "quality_score": rag_response.quality_metrics.overall_score,
            "confidence_score": rag_response.confidence_score,
            "query_type": rag_response.query_analysis.query_type.value,
            "query_complexity": rag_response.query_analysis.complexity.value,
            "improvement_applied": rag_response.quality_metrics.requires_revision,
            "retrieval_strategy": rag_response.retrieval_strategy
        }
        
        self.performance_history.append(performance_entry)
        
        # Keep only recent history
        if len(self.performance_history) > 1000:
            self.performance_history = self.performance_history[-1000:]
    
    def _create_error_response(self, query: str, error: str, start_time: datetime) -> RAGResponse:
        """Create error response when pipeline fails."""
        from .advanced_query_processor import QueryType, QueryComplexity
        
        # Create minimal response objects
        error_query_analysis = QueryAnalysis(
            original_query=query,
            query_type=QueryType.FACTUAL,
            complexity=QueryComplexity.SIMPLE,
            intent="error_handling",
            entities=[],
            topics=["error"],
            sentiment="neutral",
            urgency="medium",
            expanded_queries=[],
            keywords=[],
            context_needed=False,
            confidence=0.0
        )
        
        error_context = ContextualInformation(
            primary_context="Error occurred during processing",
            supporting_context=[],
            conversation_history="",
            user_profile={},
            temporal_context="",
            domain_context="",
            confidence_score=0.0,
            relevance_scores=[]
        )
        
        error_quality = QualityMetrics(
            accuracy=1.0,
            completeness=1.0,
            relevance=1.0,
            clarity=1.0,
            appropriateness=1.0,
            overall_score=1.0,
            suggestions=[],
            requires_revision=False,
            confidence_level="low",
            timestamp=datetime.now()
        )
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return RAGResponse(
            response=f"I apologize, but I encountered an issue processing your query: '{query}'. Please try rephrasing your question or contact support if the problem persists.",
            query_analysis=error_query_analysis,
            contextual_info=error_context,
            quality_metrics=error_quality,
            retrieval_strategy={},
            processing_time=processing_time,
            confidence_score=0.0,
            sources=[],
            synthesis_info=None,
            response_template_used=None,
            structured_format=False,
            metadata={"error": error, "pipeline_version": "enhanced_v2.0"}
        )
    
    def get_pipeline_statistics(self) -> Dict[str, Any]:
        """Get comprehensive pipeline performance statistics."""
        if not self.performance_history:
            return {"message": "No performance data available"}
        
        recent_history = self.performance_history[-100:]
        
        return {
            "total_queries": len(self.performance_history),
            "recent_performance": {
                "avg_processing_time": sum(entry["processing_time"] for entry in recent_history) / len(recent_history),
                "avg_quality_score": sum(entry["quality_score"] for entry in recent_history) / len(recent_history),
                "avg_confidence": sum(entry["confidence_score"] for entry in recent_history) / len(recent_history),
                "improvement_rate": sum(1 for entry in recent_history if entry["improvement_applied"]) / len(recent_history)
            },
            "query_type_distribution": self._get_query_type_distribution(recent_history),
            "strategy_performance": dict(list(self.adaptive_strategy.strategy_history.items())[:10]),
            "quality_manager_stats": self.quality_manager.get_quality_statistics(),
            "context_manager_conversations": len(self.context_manager.conversations)
        }
    
    def _get_query_type_distribution(self, history: List[Dict[str, Any]]) -> Dict[str, int]:
        """Get distribution of query types."""
        distribution = {}
        for entry in history:
            query_type = entry["query_type"]
            distribution[query_type] = distribution.get(query_type, 0) + 1
        return distribution
    
    async def optimize_pipeline(self) -> Dict[str, Any]:
        """Optimize pipeline based on performance history."""
        if len(self.performance_history) < 50:
            return {"message": "Insufficient data for optimization"}
        
        # Analyze performance patterns
        recent_stats = self.get_pipeline_statistics()
        
        # Adjust quality threshold based on performance
        avg_quality = recent_stats["recent_performance"]["avg_quality_score"]
        if avg_quality > 4.2:
            self.quality_manager.update_quality_threshold(4.2)
        elif avg_quality < 3.5:
            self.quality_manager.update_quality_threshold(3.5)
        
        # Optimize context window sizes based on query types
        optimizations_applied = []
        
        if recent_stats["recent_performance"]["avg_processing_time"] > 5.0:
            # Reduce complexity for faster processing
            optimizations_applied.append("Reduced context windows for faster processing")
        
        if recent_stats["recent_performance"]["avg_confidence"] < 0.7:
            # Increase retrieval rounds for better confidence
            optimizations_applied.append("Increased retrieval rounds for better confidence")
        
        return {
            "optimizations_applied": optimizations_applied,
            "current_performance": recent_stats["recent_performance"],
            "quality_threshold": self.quality_manager.quality_threshold
        }
    
    def _convert_to_source_documents(self, retrieved_documents: List[Dict[str, Any]]) -> List[SourceDocument]:
        """Convert retrieved documents to SourceDocument format for synthesis."""
        source_docs = []
        for doc in retrieved_documents:
            source_doc = SourceDocument(
                id=doc.get("id", "unknown"),
                content=doc.get("content", ""),
                title=doc.get("title", ""),
                source=doc.get("source", "knowledge_base"),
                authority_score=doc.get("authority_score", 0.5),
                recency_score=doc.get("recency_score", 0.5),
                relevance_score=doc.get("score", 0.5),
                metadata=doc.get("metadata", {})
            )
            source_docs.append(source_doc)
        return source_docs
    
    def _determine_template_type(self, query_analysis: QueryAnalysis) -> Optional[str]:
        """Determine which structured template to use based on query analysis."""
        query_lower = query_analysis.original_query.lower()
        
        # Map query patterns to template types
        if any(word in query_lower for word in ["components", "parts", "elements", "consists of"]):
            return ResponseTemplateType.COMPONENT_LISTING.value
        elif any(word in query_lower for word in ["compare", "difference", "vs", "versus", "better"]):
            return ResponseTemplateType.COMPARISON.value
        elif any(word in query_lower for word in ["how to", "steps", "procedure", "process"]):
            return ResponseTemplateType.PROCEDURAL.value
        elif any(word in query_lower for word in ["what is", "define", "definition", "explain"]):
            return ResponseTemplateType.DEFINITION.value
        elif any(word in query_lower for word in ["analyze", "analysis", "why", "reason", "cause"]):
            return ResponseTemplateType.ANALYTICAL.value
        elif any(word in query_lower for word in ["troubleshoot", "problem", "issue", "error", "fix"]):
            return ResponseTemplateType.TROUBLESHOOTING.value
        elif query_analysis.query_type == QueryType.MULTI_HOP:
            return ResponseTemplateType.MULTI_HOP.value
        elif query_analysis.query_type == QueryType.CONVERSATIONAL:
            return ResponseTemplateType.CONVERSATIONAL.value
        
        return None
    
    async def _generate_structured_response(self, template, query_analysis: QueryAnalysis, 
                                          contextual_info: ContextualInformation, 
                                          synthesis_info: Optional[SynthesizedResponse],
                                          temperature: float) -> str:
        """Generate response using structured template."""
        
        # Create LLM instance with specified temperature
        template_llm = ChatOpenAI(model="gpt-4o", temperature=temperature)
        
        # Prepare template variables
        template_vars = {
            "query": query_analysis.original_query,
            "context": contextual_info.primary_context,
            "topic": query_analysis.entities[0] if query_analysis.entities else "the topic"
        }
        
        # Add synthesis information if available
        if synthesis_info:
            template_vars["synthesized_content"] = synthesis_info.synthesized_content
            template_vars["source_citations"] = synthesis_info.source_citations
            template_vars["supporting_evidence"] = synthesis_info.supporting_evidence
        
        try:
            response = await template.ainvoke(template_vars, config={"llm": template_llm})
            return response.content.strip()
        except Exception as e:
            self.logger.error(f"Error generating structured response: {e}")
            # Fallback to regular response generation
            return f"I understand you're asking about {template_vars['topic']}. Based on the available information: {contextual_info.primary_context[:300]}..."
    
    async def optimize_temperature_settings(self) -> Dict[str, Any]:
        """Use temperature tester to optimize temperature settings for different query types."""
        
        try:
            self.logger.info("Running temperature optimization tests...")
            
            # Run temperature tests for different query types
            test_results = await self.temperature_tester.run_comprehensive_tests()
            
            # Update temperature configurations based on results
            if test_results:
                for query_type, optimal_temp in test_results.get("optimal_temperatures", {}).items():
                    if query_type in self.temperature_config:
                        old_temp = self.temperature_config[query_type]
                        self.temperature_config[query_type] = optimal_temp
                        self.logger.info(f"Updated temperature for {query_type}: {old_temp} -> {optimal_temp}")
            
            return {
                "success": True,
                "optimized_temperatures": self.temperature_config,
                "test_results": test_results
            }
            
        except Exception as e:
            self.logger.error(f"Temperature optimization failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "current_temperatures": self.temperature_config
            }
    
    def get_temperature_recommendations(self) -> Dict[str, float]:
        """Get current temperature recommendations by query type."""
        return self.temperature_config.copy()
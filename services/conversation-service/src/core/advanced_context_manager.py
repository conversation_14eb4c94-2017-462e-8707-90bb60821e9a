"""
Advanced Context Manager for RAG Pipeline

This module provides sophisticated context management capabilities including:
- User profile tracking
- Conversation history management
- Context optimization for different query types
"""

from typing import Dict, List, Any, Optional, Deque, Tuple
from collections import deque
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import pickle
import os
import hashlib
import asyncio
from pathlib import Path
import sqlite3
from contextlib import contextmanager
import logging

from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate

from .advanced_query_processor import QueryAnalysis, QueryComplexity

logger = logging.getLogger(__name__)


class ConversationState(Enum):
    """Conversation state tracking."""
    INITIATED = "initiated"
    ONGOING = "ongoing"
    COMPLETED = "completed"
    STALE = "stale"
    ARCHIVED = "archived"


@dataclass
class ConversationSession:
    """Persistent conversation session."""
    session_id: str
    user_id: Optional[str]
    state: ConversationState
    created_at: datetime
    last_active: datetime
    turn_count: int
    compressed_history: Optional[str] = None
    full_history_hash: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CompressedContext:
    """Compressed conversation context for memory optimization."""
    summary: str
    key_topics: List[str]
    user_preferences: Dict[str, Any]
    important_facts: List[str]
    compression_ratio: float
    original_length: int
    compressed_length: int
    timestamp: datetime


@dataclass 
class ContextualInformation:
    """Comprehensive contextual information for RAG responses."""
    primary_context: str
    supporting_context: List[str]
    conversation_history: str
    user_profile: Dict[str, Any]
    temporal_context: str
    domain_context: str
    confidence_score: float
    relevance_scores: List[float]


@dataclass
class ConversationTurn:
    """Represents a single conversation turn."""
    user_message: str
    assistant_response: str
    timestamp: datetime
    context_used: List[str]
    retrieval_quality: float
    user_feedback: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class AdvancedContextManager:
    """Advanced context management with user profiling, optimization, and persistence."""
    
    def __init__(
        self, 
        max_conversation_length: int = 20, 
        context_window_tokens: int = 4000,
        persistence_dir: str = "data/conversations",
        compression_threshold: int = 10,
        compression_ratio_target: float = 0.3
    ):
        self.conversations: Dict[str, Deque[ConversationTurn]] = {}
        self.user_profiles: Dict[str, Dict[str, Any]] = {}
        self.context_cache: Dict[str, ContextualInformation] = {}
        self.conversation_sessions: Dict[str, ConversationSession] = {}
        self.compressed_contexts: Dict[str, CompressedContext] = {}
        
        self.max_conversation_length = max_conversation_length
        self.context_window_tokens = context_window_tokens
        self.compression_threshold = compression_threshold
        self.compression_ratio_target = compression_ratio_target
        
        # Setup persistence
        self.persistence_dir = Path(persistence_dir)
        self.persistence_dir.mkdir(parents=True, exist_ok=True)
        self.db_path = self.persistence_dir / "conversations.db"
        self._init_persistence()
        
        # LLM for context optimization
        self.context_optimizer = ChatOpenAI(model="gpt-4o-mini", temperature=0.1)
        
        # Context selection strategies
        self.context_selector = self._create_context_selector()
        self.conversation_summarizer = self._create_conversation_summarizer()
        self.relevance_scorer = self._create_relevance_scorer()
        self.context_compressor = self._create_context_compressor()
        
        # Load existing sessions
        self._load_active_sessions()
    
    def _init_persistence(self) -> None:
        """Initialize SQLite database for conversation persistence."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Create sessions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS conversation_sessions (
                    session_id TEXT PRIMARY KEY,
                    user_id TEXT,
                    state TEXT,
                    created_at TIMESTAMP,
                    last_active TIMESTAMP,
                    turn_count INTEGER,
                    compressed_history TEXT,
                    full_history_hash TEXT,
                    metadata TEXT
                )
            """)
            
            # Create conversation turns table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS conversation_turns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT,
                    user_message TEXT,
                    assistant_response TEXT,
                    timestamp TIMESTAMP,
                    context_used TEXT,
                    retrieval_quality REAL,
                    user_feedback REAL,
                    metadata TEXT,
                    FOREIGN KEY (session_id) REFERENCES conversation_sessions (session_id)
                )
            """)
            
            # Create compressed contexts table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS compressed_contexts (
                    session_id TEXT PRIMARY KEY,
                    summary TEXT,
                    key_topics TEXT,
                    user_preferences TEXT,
                    important_facts TEXT,
                    compression_ratio REAL,
                    original_length INTEGER,
                    compressed_length INTEGER,
                    timestamp TIMESTAMP,
                    FOREIGN KEY (session_id) REFERENCES conversation_sessions (session_id)
                )
            """)
            
            conn.commit()
    
    def _create_context_compressor(self) -> ChatPromptTemplate:
        """Create context compression prompt template."""
        return ChatPromptTemplate.from_messages([
            ("system", """You are a conversation context compressor. Your task is to compress conversation history 
while preserving the most important information for maintaining context in future interactions.

Create a compressed summary that includes:
1. Key topics discussed
2. Important facts and decisions
3. User preferences and patterns
4. Unresolved issues or ongoing concerns

Aim for a {compression_ratio_target}x compression ratio while retaining essential context.

Return a JSON object:
{
    "summary": "concise conversation summary",
    "key_topics": ["topic1", "topic2"],
    "user_preferences": {"preference": "value"},
    "important_facts": ["fact1", "fact2"],
    "compression_notes": "what was compressed and why"
}"""),
            ("human", """Conversation History ({original_length} chars):
{conversation_history}

Current user profile:
{user_profile}

Compress to approximately {target_length} characters:""")
        ])
    
    def _create_context_selector(self):
        return ChatPromptTemplate.from_messages([
            ("system", """You are a context selection expert. Given retrieved documents and a query analysis, 
select and organize the most relevant context for answering the user's question.

Return a JSON object:
{
    "primary_context": "most relevant context",
    "supporting_context": ["additional context 1", "additional context 2"],
    "relevance_scores": [0.95, 0.87, 0.76],
    "reasoning": "explanation of context selection",
    "confidence": 0.9
}"""),
            ("human", """Query Analysis: {query_analysis}
            
Retrieved Documents:
{retrieved_documents}

Conversation Context:
{conversation_context}

Select optimal context:""")
        ])
    
    def _create_conversation_summarizer(self):
        return ChatPromptTemplate.from_messages([
            ("system", "Summarize the conversation history to provide relevant context for the current query."),
            ("human", "Current Query: {current_query}\n\nConversation History:\n{conversation_history}\n\nProvide summary:")
        ])
    
    def _create_relevance_scorer(self):
        return ChatPromptTemplate.from_messages([
            ("system", "Score the relevance of each context piece to the user's query on a scale of 0.0 to 1.0."),
            ("human", "Query: {query}\nContext Pieces:\n{context_pieces}\n\nScore relevance:")
        ])
    
    async def build_optimal_context(self, conversation_id: str, current_message: str, query_analysis: QueryAnalysis, retrieved_documents: List[Dict[str, Any]], user_profile: Optional[Dict[str, Any]] = None) -> ContextualInformation:
        """Build optimal context using advanced selection strategies."""
        conversation_history = self._get_conversation_summary(conversation_id, current_message)
        
        # Update user profile
        if user_profile:
            self.user_profiles[conversation_id] = user_profile
        elif conversation_id not in self.user_profiles:
            self.user_profiles[conversation_id] = self._infer_user_profile(conversation_id, query_analysis)
        
        context_selection = await self._select_context(query_analysis, retrieved_documents, conversation_history)
        temporal_context = self._build_temporal_context(query_analysis)
        domain_context = self._build_domain_context(query_analysis, retrieved_documents)
        
        contextual_info = ContextualInformation(
            primary_context=context_selection["primary_context"],
            supporting_context=context_selection["supporting_context"],
            conversation_history=conversation_history,
            user_profile=self.user_profiles[conversation_id],
            temporal_context=temporal_context,
            domain_context=domain_context,
            confidence_score=context_selection["confidence"],
            relevance_scores=context_selection["relevance_scores"]
        )
        
        # Cache the context
        cache_key = f"{conversation_id}_{hash(current_message)}"
        self.context_cache[cache_key] = contextual_info
        
        return contextual_info
    
    async def _select_context(self, query_analysis: QueryAnalysis, retrieved_documents: List[Dict[str, Any]], conversation_history: str) -> Dict[str, Any]:
        """Select optimal context using LLM-based selection."""
        try:
            doc_summaries = [
                {
                    "id": i,
                    "title": doc.get("title", "Untitled"),
                    "content": doc["content"][:500],
                    "relevance_score": doc.get("score", 0.0),
                    "metadata": doc.get("metadata", {})
                }
                for i, doc in enumerate(retrieved_documents[:10])
            ]
            
            response = await self.context_selector.ainvoke({
                "query_analysis": json.dumps({
                    "query": query_analysis.original_query,
                    "type": query_analysis.query_type.value,
                    "complexity": query_analysis.complexity.value,
                    "intent": query_analysis.intent,
                    "keywords": query_analysis.keywords
                }),
                "retrieved_documents": json.dumps(doc_summaries, indent=2),
                "conversation_context": conversation_history[:1000]
            })
            
            return json.loads(response.content)
            
        except Exception:
            return self._fallback_context_selection(retrieved_documents, query_analysis)
    
    def _fallback_context_selection(self, retrieved_documents: List[Dict[str, Any]], query_analysis: QueryAnalysis) -> Dict[str, Any]:
        """Fallback context selection using rules."""
        if not retrieved_documents:
            return {
                "primary_context": "No relevant information found.",
                "supporting_context": [],
                "relevance_scores": [],
                "confidence": 0.0
            }
        
        sorted_docs = sorted(retrieved_documents, key=lambda x: x.get("score", 0), reverse=True)
        
        return {
            "primary_context": sorted_docs[0]["content"][:1000],
            "supporting_context": [doc["content"][:500] for doc in sorted_docs[1:3]],
            "relevance_scores": [doc.get("score", 0.5) for doc in sorted_docs[:3]],
            "confidence": 0.7
        }
    
    def _get_conversation_summary(self, conversation_id: str, current_message: str) -> str:
        """Get summarized conversation history."""
        if conversation_id not in self.conversations:
            return "No previous conversation history."
        
        history = list(self.conversations[conversation_id])
        if not history:
            return "No previous conversation history."
        
        # Create simple summary of recent turns
        recent_turns = history[-5:]  # Last 5 turns
        summary_parts = []
        
        for turn in recent_turns:
            summary_parts.append(f"User: {turn.user_message[:100]}...")
            summary_parts.append(f"Assistant: {turn.assistant_response[:100]}...")
        
        return "\n".join(summary_parts)
    
    def _infer_user_profile(self, conversation_id: str, query_analysis: QueryAnalysis) -> Dict[str, Any]:
        """Infer user profile from conversation and query analysis."""
        profile = {
            "expertise_level": "intermediate",
            "preferred_response_style": "detailed",
            "interests": query_analysis.topics,
            "conversation_start": datetime.now(),
            "query_patterns": [query_analysis.query_type.value],
            "sentiment_history": [query_analysis.sentiment],
            "urgency_patterns": [query_analysis.urgency]
        }
        
        # Analyze complexity patterns
        if query_analysis.complexity in [QueryComplexity.COMPLEX, QueryComplexity.MULTI_STEP]:
            profile["expertise_level"] = "advanced"
        elif query_analysis.complexity == QueryComplexity.SIMPLE:
            profile["expertise_level"] = "beginner"
        
        return profile
    
    def _build_temporal_context(self, query_analysis: QueryAnalysis) -> str:
        """Build temporal context if relevant."""
        now = datetime.now()
        temporal_indicators = ["recent", "latest", "current", "today", "now", "new"]
        
        if any(indicator in query_analysis.original_query.lower() for indicator in temporal_indicators):
            return f"Current date and time: {now.strftime('%Y-%m-%d %H:%M:%S')}"
        
        return ""
    
    def _build_domain_context(self, query_analysis: QueryAnalysis, retrieved_documents: List[Dict[str, Any]]) -> str:
        """Build domain-specific context."""
        domains = set()
        for doc in retrieved_documents[:5]:
            metadata = doc.get("metadata", {})
            if "domain" in metadata:
                domains.add(metadata["domain"])
            if "category" in metadata:
                domains.add(metadata["category"])
        
        return f"Domain context: {', '.join(domains)}" if domains else ""
    
    def add_conversation_turn(self, conversation_id: str, user_message: str, assistant_response: str, context_used: List[str], retrieval_quality: float) -> None:
        """Add a conversation turn to history."""
        if conversation_id not in self.conversations:
            self.conversations[conversation_id] = deque(maxlen=self.max_conversation_length)
        
        turn = ConversationTurn(
            user_message=user_message,
            assistant_response=assistant_response,
            timestamp=datetime.now(),
            context_used=context_used,
            retrieval_quality=retrieval_quality
        )
        
        self.conversations[conversation_id].append(turn)
        self._update_user_profile(conversation_id, turn)
    
    def _update_user_profile(self, conversation_id: str, turn: ConversationTurn) -> None:
        """Update user profile based on conversation turn."""
        if conversation_id not in self.user_profiles:
            return
        
        profile = self.user_profiles[conversation_id]
        
        # Update interaction patterns
        if "total_interactions" not in profile:
            profile["total_interactions"] = 0
        profile["total_interactions"] += 1
        
        # Track average retrieval quality
        if "avg_retrieval_quality" not in profile:
            profile["avg_retrieval_quality"] = turn.retrieval_quality
        else:
            current_avg = profile["avg_retrieval_quality"]
            total = profile["total_interactions"]
            profile["avg_retrieval_quality"] = (current_avg * (total - 1) + turn.retrieval_quality) / total
        
        # Update last interaction
        profile["last_interaction"] = turn.timestamp
    
    def clear_conversation(self, conversation_id: str) -> None:
        """Clear conversation history for a given ID."""
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]
        if conversation_id in self.user_profiles:
            del self.user_profiles[conversation_id]
    
    def get_active_conversations(self) -> List[str]:
        """Get list of active conversation IDs."""
        # Consider conversations active if they have activity in last 24 hours
        cutoff = datetime.now() - timedelta(hours=24)
        active = []
        
        for conv_id, turns in self.conversations.items():
            if turns and turns[-1].timestamp > cutoff:
                active.append(conv_id)
        
        return active
    
    def _load_active_sessions(self) -> None:
        """Load active conversation sessions from database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Load sessions active in last 24 hours
                cutoff = datetime.now() - timedelta(hours=24)
                cursor.execute("""
                    SELECT * FROM conversation_sessions 
                    WHERE last_active > ? AND state IN ('initiated', 'ongoing')
                """, (cutoff,))
                
                for row in cursor.fetchall():
                    session = ConversationSession(
                        session_id=row[0],
                        user_id=row[1],
                        state=ConversationState(row[2]),
                        created_at=datetime.fromisoformat(row[3]),
                        last_active=datetime.fromisoformat(row[4]),
                        turn_count=row[5],
                        compressed_history=row[6],
                        full_history_hash=row[7],
                        metadata=json.loads(row[8]) if row[8] else {}
                    )
                    self.conversation_sessions[session.session_id] = session
                    
                    # Load recent turns for active sessions
                    self._load_conversation_turns(session.session_id)
                    
                    # Load compressed context if available
                    self._load_compressed_context(session.session_id)
                    
        except Exception as e:
            logger.error(f"Error loading active sessions: {e}")
    
    def _load_conversation_turns(self, session_id: str) -> None:
        """Load conversation turns for a session."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT user_message, assistant_response, timestamp, context_used, 
                           retrieval_quality, user_feedback, metadata
                    FROM conversation_turns 
                    WHERE session_id = ? 
                    ORDER BY timestamp DESC 
                    LIMIT ?
                """, (session_id, self.max_conversation_length))
                
                turns = deque(maxlen=self.max_conversation_length)
                for row in reversed(cursor.fetchall()):  # Reverse to maintain chronological order
                    turn = ConversationTurn(
                        user_message=row[0],
                        assistant_response=row[1],
                        timestamp=datetime.fromisoformat(row[2]),
                        context_used=json.loads(row[3]) if row[3] else [],
                        retrieval_quality=row[4],
                        user_feedback=row[5],
                        metadata=json.loads(row[6]) if row[6] else {}
                    )
                    turns.append(turn)
                
                self.conversations[session_id] = turns
                
        except Exception as e:
            logger.error(f"Error loading conversation turns for {session_id}: {e}")
    
    def _load_compressed_context(self, session_id: str) -> None:
        """Load compressed context for a session."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT summary, key_topics, user_preferences, important_facts,
                           compression_ratio, original_length, compressed_length, timestamp
                    FROM compressed_contexts 
                    WHERE session_id = ?
                """, (session_id,))
                
                row = cursor.fetchone()
                if row:
                    compressed_context = CompressedContext(
                        summary=row[0],
                        key_topics=json.loads(row[1]) if row[1] else [],
                        user_preferences=json.loads(row[2]) if row[2] else {},
                        important_facts=json.loads(row[3]) if row[3] else [],
                        compression_ratio=row[4],
                        original_length=row[5],
                        compressed_length=row[6],
                        timestamp=datetime.fromisoformat(row[7])
                    )
                    self.compressed_contexts[session_id] = compressed_context
                    
        except Exception as e:
            logger.error(f"Error loading compressed context for {session_id}: {e}")
    
    def _save_conversation_session(self, session: ConversationSession) -> None:
        """Save conversation session to database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO conversation_sessions 
                    (session_id, user_id, state, created_at, last_active, turn_count, 
                     compressed_history, full_history_hash, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    session.session_id,
                    session.user_id,
                    session.state.value,
                    session.created_at.isoformat(),
                    session.last_active.isoformat(),
                    session.turn_count,
                    session.compressed_history,
                    session.full_history_hash,
                    json.dumps(session.metadata)
                ))
                conn.commit()
                
        except Exception as e:
            logger.error(f"Error saving conversation session {session.session_id}: {e}")
    
    def _save_conversation_turn(self, session_id: str, turn: ConversationTurn) -> None:
        """Save conversation turn to database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO conversation_turns 
                    (session_id, user_message, assistant_response, timestamp, 
                     context_used, retrieval_quality, user_feedback, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    session_id,
                    turn.user_message,
                    turn.assistant_response,
                    turn.timestamp.isoformat(),
                    json.dumps(turn.context_used),
                    turn.retrieval_quality,
                    turn.user_feedback,
                    json.dumps(turn.metadata)
                ))
                conn.commit()
                
        except Exception as e:
            logger.error(f"Error saving conversation turn for {session_id}: {e}")
    
    def _save_compressed_context(self, session_id: str, compressed_context: CompressedContext) -> None:
        """Save compressed context to database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO compressed_contexts 
                    (session_id, summary, key_topics, user_preferences, important_facts,
                     compression_ratio, original_length, compressed_length, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    session_id,
                    compressed_context.summary,
                    json.dumps(compressed_context.key_topics),
                    json.dumps(compressed_context.user_preferences),
                    json.dumps(compressed_context.important_facts),
                    compressed_context.compression_ratio,
                    compressed_context.original_length,
                    compressed_context.compressed_length,
                    compressed_context.timestamp.isoformat()
                ))
                conn.commit()
                
        except Exception as e:
            logger.error(f"Error saving compressed context for {session_id}: {e}")
    
    async def compress_conversation_history(self, session_id: str) -> Optional[CompressedContext]:
        """Compress conversation history when it exceeds threshold."""
        if session_id not in self.conversations:
            return None
            
        turns = list(self.conversations[session_id])
        if len(turns) < self.compression_threshold:
            return None
        
        # Build full conversation history
        history_parts = []
        for turn in turns:
            history_parts.append(f"User: {turn.user_message}")
            history_parts.append(f"Assistant: {turn.assistant_response}")
        
        conversation_history = "\n".join(history_parts)
        original_length = len(conversation_history)
        target_length = int(original_length * self.compression_ratio_target)
        
        user_profile = self.user_profiles.get(session_id, {})
        
        try:
            response = await self.context_compressor.ainvoke({
                "conversation_history": conversation_history,
                "user_profile": json.dumps(user_profile, indent=2),
                "original_length": original_length,
                "target_length": target_length,
                "compression_ratio_target": self.compression_ratio_target
            })
            
            compression_data = json.loads(response.content)
            
            compressed_context = CompressedContext(
                summary=compression_data["summary"],
                key_topics=compression_data["key_topics"],
                user_preferences=compression_data["user_preferences"],
                important_facts=compression_data["important_facts"],
                compression_ratio=len(compression_data["summary"]) / original_length,
                original_length=original_length,
                compressed_length=len(compression_data["summary"]),
                timestamp=datetime.now()
            )
            
            # Save compressed context
            self.compressed_contexts[session_id] = compressed_context
            self._save_compressed_context(session_id, compressed_context)
            
            # Update session with compressed history
            if session_id in self.conversation_sessions:
                session = self.conversation_sessions[session_id]
                session.compressed_history = compression_data["summary"]
                session.full_history_hash = hashlib.md5(conversation_history.encode()).hexdigest()
                self._save_conversation_session(session)
            
            logger.info(f"Compressed conversation {session_id}: {original_length} -> {compressed_context.compressed_length} chars")
            return compressed_context
            
        except Exception as e:
            logger.error(f"Error compressing conversation history for {session_id}: {e}")
            return None
    
    def get_conversation_context_with_compression(self, session_id: str, current_message: str) -> str:
        """Get conversation context with intelligent compression."""
        # Check for compressed context
        if session_id in self.compressed_contexts:
            compressed = self.compressed_contexts[session_id]
            context_parts = [
                f"Previous conversation summary: {compressed.summary}",
                f"Key topics: {', '.join(compressed.key_topics)}",
                f"Important facts: {'; '.join(compressed.important_facts)}"
            ]
            
            # Add recent uncompressed turns
            if session_id in self.conversations:
                recent_turns = list(self.conversations[session_id])[-3:]  # Last 3 turns
                for turn in recent_turns:
                    context_parts.append(f"User: {turn.user_message[:100]}...")
                    context_parts.append(f"Assistant: {turn.assistant_response[:100]}...")
            
            return "\n".join(context_parts)
        
        # Fallback to regular conversation summary
        return self._get_conversation_summary(session_id, current_message)
    
    def create_conversation_session(self, session_id: str, user_id: Optional[str] = None) -> ConversationSession:
        """Create a new conversation session."""
        session = ConversationSession(
            session_id=session_id,
            user_id=user_id,
            state=ConversationState.INITIATED,
            created_at=datetime.now(),
            last_active=datetime.now(),
            turn_count=0
        )
        
        self.conversation_sessions[session_id] = session
        self._save_conversation_session(session)
        
        return session
    
    def update_session_state(self, session_id: str, state: ConversationState) -> None:
        """Update conversation session state."""
        if session_id in self.conversation_sessions:
            session = self.conversation_sessions[session_id]
            session.state = state
            session.last_active = datetime.now()
            self._save_conversation_session(session)
    
    async def add_conversation_turn_with_compression(
        self, 
        conversation_id: str, 
        user_message: str, 
        assistant_response: str, 
        context_used: List[str], 
        retrieval_quality: float
    ) -> None:
        """Add conversation turn with automatic compression if needed."""
        # Create session if it doesn't exist
        if conversation_id not in self.conversation_sessions:
            self.create_conversation_session(conversation_id)
        
        # Add the turn
        self.add_conversation_turn(conversation_id, user_message, assistant_response, context_used, retrieval_quality)
        
        # Update session
        session = self.conversation_sessions[conversation_id]
        session.turn_count += 1
        session.last_active = datetime.now()
        session.state = ConversationState.ONGOING
        self._save_conversation_session(session)
        
        # Check if compression is needed
        if session.turn_count >= self.compression_threshold:
            await self.compress_conversation_history(conversation_id)
    
    def get_context_relevance_scores(self, context_pieces: List[str], query: str) -> List[float]:
        """Calculate relevance scores for context pieces."""
        scores = []
        query_words = set(query.lower().split())
        
        for context in context_pieces:
            context_words = set(context.lower().split())
            
            # Simple word overlap scoring
            overlap = len(query_words & context_words)
            total_unique = len(query_words | context_words)
            
            if total_unique > 0:
                jaccard_score = overlap / total_unique
            else:
                jaccard_score = 0.0
            
            # Length penalty for very short or very long contexts
            length_penalty = 1.0
            if len(context) < 50:
                length_penalty = 0.8
            elif len(context) > 2000:
                length_penalty = 0.9
            
            scores.append(jaccard_score * length_penalty)
        
        return scores
    
    def prune_context_by_relevance(self, contexts: List[str], scores: List[float], max_contexts: int = 5) -> Tuple[List[str], List[float]]:
        """Prune contexts by relevance scores."""
        if len(contexts) <= max_contexts:
            return contexts, scores
        
        # Sort by relevance score
        scored_contexts = list(zip(contexts, scores))
        scored_contexts.sort(key=lambda x: x[1], reverse=True)
        
        # Take top contexts
        pruned_contexts = [ctx for ctx, score in scored_contexts[:max_contexts]]
        pruned_scores = [score for ctx, score in scored_contexts[:max_contexts]]
        
        return pruned_contexts, pruned_scores
from .enhanced_context_manager import <PERSON>hanced<PERSON>ontextManager
from .fact_verification import FactVerificationSystem
from .multi_source_synthesis import MultiSourceSynthesis
from .multi_hop_processor import MultiHopProcessor, MultiHopResult
from .customer_profile import CustomerProfileManager
from .context_aware_generator import ContextAwareResponseGenerator, ContextualResponse

class EnhancedRAGPipeline:
    """Enhanced RAG Pipeline with Phase 2 intelligence features."""
    
    def __init__(self, config: RAGConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize enhanced components
        self.context_manager = EnhancedContextManager(config)
        self.fact_verifier = FactVerificationSystem(config)
        self.multi_source_synthesizer = MultiSourceSynthesis(config)
        self.multi_hop_processor = MultiHopProcessor(config)
        
        # Customer context components
        self.customer_profile_manager = CustomerProfileManager(config)
        self.context_aware_generator = ContextAwareResponseGenerator(
            config, self.customer_profile_manager
        )
        
        # Enable customer context features
        self.enable_customer_context = getattr(config, 'enable_customer_context', True)
        
        # Multi-hop settings
        self.enable_multi_hop = getattr(config, 'enable_multi_hop_processing', True)
        self.multi_hop_threshold = getattr(config, 'multi_hop_complexity_threshold', 0.7)
        
        # Initialize existing components
        self.query_processor = QueryProcessor(config)
        self.retriever = SemanticRetriever(config)
        self.response_generator = ResponseGenerator(config)
        self.quality_evaluator = QualityEvaluator(config)
        
    async def process_query(
        self, 
        query: str, 
        conversation_id: str, 
        user_id: str,
        customer_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Enhanced process_query with customer context integration."""
        
        start_time = datetime.now()
        
        try:
            # Use customer_id or fallback to user_id
            effective_customer_id = customer_id or user_id
            
            # 1. Enhanced Context Management
            conversation_context = await self.context_manager.get_conversation_context(
                conversation_id, user_id
            )
            
            # 2. Query Processing with Context
            query_analysis = await self.query_processor.analyze_query(
                query, conversation_context
            )
            
            # Step 3: Determine if multi-hop processing is needed
            needs_multi_hop = (
                self.enable_multi_hop and 
                query_analysis.complexity_score > self.multi_hop_threshold and
                query_analysis.query_type in [QueryType.COMPARISON, QueryType.PROCESS, QueryType.TROUBLESHOOTING]
            )
            
            # Step 3: Enhanced retrieval with customer context
            if self.enable_customer_context and effective_customer_id:
                customer_context = await self.customer_profile_manager.get_contextual_preferences(
                    effective_customer_id,
                    query_analysis.query_type.value if query_analysis else None
                )
                
                # Use customer context to enhance retrieval
                retrieval_results = await self._enhanced_retrieval_with_context(
                    query, query_analysis, conversation_context, customer_context
                )
            else:
                retrieval_results = await self._enhanced_retrieval(
                    query, query_analysis, conversation_context
                )
            
            if needs_multi_hop:
                # Multi-hop processing path
                multi_hop_result = await self.multi_hop_processor.process_multi_hop_query(
                    query, query_analysis, conversation_context, user_profile
                )
                
                response = multi_hop_result.final_synthesis
                retrieval_results = {
                    "sources": multi_hop_result.sources_used,
                    "context": response,
                    "confidence": multi_hop_result.overall_confidence,
                    "multi_hop_info": {
                        "decomposition": multi_hop_result.decomposition,
                        "sub_results": len(multi_hop_result.sub_results),
                        "execution_path": multi_hop_result.execution_path
                    }
                }
                
                # Skip standard synthesis since multi-hop already synthesized
                synthesized_context = response
                
            else:
                # Standard processing path
                # Multi-Source Synthesis
                if len(retrieval_results.get("sources", [])) > 1:
                    synthesized_context = await self.multi_source_synthesizer.synthesize_sources(
                        retrieval_results["sources"], query, query_analysis
                    )
                else:
                    synthesized_context = retrieval_results.get("context", "")
                
                # Step 4: Context-aware response generation
                if self.enable_customer_context and effective_customer_id and not needs_multi_hop:
                    contextual_response = await self.context_aware_generator.generate_contextual_response(
                        query, synthesized_context, query_analysis, effective_customer_id, conversation_context
                    )
                    
                    response = contextual_response.content
                    personalization_info = {
                        "tone_used": contextual_response.tone_used,
                        "personalizations_applied": contextual_response.personalization_applied,
                        "customer_context": contextual_response.customer_context,
                        "contextual_confidence": contextual_response.confidence_score
                    }
                else:
                    # Standard response generation
                    response = await self.response_generator.generate_response(
                        query, synthesized_context, query_analysis, conversation_context
                    )
                    personalization_info = {"personalization_used": False}
            
            # 6. Fact Verification
            verification_result = await self.fact_verifier.verify_response(
                response, retrieval_results.get("sources", [])
            )
            
            # 7. Quality Evaluation
            quality_result = await self.quality_evaluator.evaluate_response(
                query, response, synthesized_context, verification_result
            )
            
            # 8. Update Context
            await self.context_manager.update_conversation_context(
                conversation_id, user_id, query, response, query_analysis
            )
            
            # Step 5: Record interaction for future personalization
            if self.enable_customer_context and effective_customer_id:
                await self._record_customer_interaction(
                    effective_customer_id, query, response, query_analysis, quality_result
                )
            
            processing_time = time.time() - start_time
            
            response_data = {
                "response": response,
                "conversation_id": conversation_id,
                "query_analysis": query_analysis.to_dict(),
                "retrieval_results": retrieval_results,
                "synthesis_info": synthesized_context if isinstance(synthesized_context, dict) else {},
                "verification_result": verification_result,
                "quality_metrics": quality_result,
                "processing_time": processing_time,
                "context_info": {
                    "context_size": len(conversation_context.get("messages", [])),
                    "context_strategy": conversation_context.get("strategy", "default")
                },
                "multi_hop_used": needs_multi_hop,
                "personalization_info": personalization_info
            }
            
            if needs_multi_hop:
                response_data["multi_hop_info"] = retrieval_results.get("multi_hop_info", {})
            
            return response_data
            
        except Exception as e:
            self.logger.error(f"Enhanced RAG pipeline failed: {e}")
            return await self._fallback_response(query, user_id, conversation_id)
    
    async def _enhanced_retrieval(self, 
                                query: str, 
                                query_analysis: Dict[str, Any],
                                conversation_context: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced retrieval with context awareness."""
        
        # Expand query based on conversation context
        expanded_query = await self._expand_query_with_context(
            query, conversation_context, query_analysis
        )
        
        # Multi-stage retrieval
        retrieval_results = await self.retriever.retrieve_documents(
            expanded_query, 
            query_analysis.get("complexity", "SIMPLE"),
            conversation_context
        )
        
        return retrieval_results
    
    async def _expand_query_with_context(self,
                                       query: str,
                                       conversation_context: Dict[str, Any],
                                       query_analysis: Dict[str, Any]) -> str:
        """Expand query using conversation context."""
        
        if not conversation_context.get("messages"):
            return query
        
        # Get recent relevant context
        recent_messages = conversation_context["messages"][-3:]
        context_text = " ".join([msg.get("content", "") for msg in recent_messages])
        
        # Simple context expansion
        if query_analysis.get("requires_context", False):
            return f"{context_text} {query}"
        
        return query
    
    async def _fallback_response(self, 
                               query: str, 
                               user_id: str, 
                               conversation_id: Optional[str]) -> Dict[str, Any]:
        """Fallback response when main pipeline fails."""
        
        return {
            "response": "I apologize, but I'm experiencing technical difficulties. Please try again.",
            "conversation_id": conversation_id,
            "error": True,
            "fallback": True
        }

    async def _enhanced_retrieval_with_context(
        self,
        query: str,
        query_analysis: QueryAnalysis,
        conversation_context: Dict,
        customer_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Enhanced retrieval that considers customer context."""
        
        # Modify retrieval based on customer preferences
        retrieval_params = {
            "max_results": self.config.max_retrieval_results
        }
        
        # Adjust retrieval based on expertise level
        expertise = customer_context.get("expertise_level", "intermediate")
        if expertise == "beginner":
            # Prefer introductory content
            retrieval_params["prefer_introductory"] = True
        elif expertise in ["advanced", "expert"]:
            # Prefer technical content
            retrieval_params["prefer_technical"] = True
        
        # Consider recent topics for context
        recent_topics = customer_context.get("recent_topics", [])
        if recent_topics:
            retrieval_params["boost_topics"] = recent_topics[:3]
        
        # Use customer tier for result prioritization
        tier = customer_context.get("customer_tier", "basic")
        if tier in ["premium", "enterprise", "vip"]:
            retrieval_params["max_results"] = min(
                retrieval_params["max_results"] * 2, 
                self.config.max_retrieval_results * 2
            )
        
        # Perform enhanced retrieval
        return await self.retrieval_engine.retrieve_documents(
            query=query,
            context=conversation_context,
            **retrieval_params
        )

    async def _record_customer_interaction(
        self,
        customer_id: str,
        query: str,
        response: str,
        query_analysis: Optional[QueryAnalysis],
        quality_result: Dict[str, Any]
    ):
        """Record customer interaction for future personalization."""
        
        try:
            # Extract topics from query analysis
            topics = []
            if query_analysis:
                if hasattr(query_analysis, 'primary_topic'):
                    topics.append(query_analysis.primary_topic)
                if hasattr(query_analysis, 'entities'):
                    topics.extend([entity.get('text', '') for entity in query_analysis.entities[:3]])
            
            # Determine resolution status
            quality_score = quality_result.get("overall_score", 0.0)
            if quality_score >= 4.0:
                resolution_status = "resolved"
            elif quality_score >= 2.5:
                resolution_status = "partially_resolved"
            else:
                resolution_status = "needs_improvement"
            
            # Record the interaction
            await self.customer_profile_manager.record_interaction(
                customer_id=customer_id,
                query=query,
                response=response,
                satisfaction_score=quality_score,
                resolution_status=resolution_status,
                topics=topics
            )
            
        except Exception as e:
            self.logger.error(f"Failed to record customer interaction: {e}")

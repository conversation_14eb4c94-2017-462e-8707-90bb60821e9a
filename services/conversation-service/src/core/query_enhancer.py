"""
Query Enhancer
Advanced query processing with expansion, decomposition, and optimization
"""

from typing import Dict, List, Any, Optional, Tuple, Set
import logging
import re
import asyncio
from dataclasses import dataclass, field
from enum import Enum
import json
from collections import Counter, defaultdict

# NLP libraries
try:
    import spacy
    import nltk
    from nltk.corpus import wordnet, stopwords
    from nltk.tokenize import word_tokenize, sent_tokenize
    from nltk.stem import WordNetLemmatizer
    from textblob import TextBlob
    NLP_LIBS_AVAILABLE = True
except ImportError:
    NLP_LIBS_AVAILABLE = False

@dataclass
class QueryAnalysis:
    """Comprehensive query analysis results."""
    
    # Basic properties
    original_query: str
    cleaned_query: str
    query_length: int
    word_count: int
    
    # Query classification
    query_type: str  # question, command, search, etc.
    intent: str      # information_seeking, problem_solving, etc.
    complexity: str  # simple, moderate, complex
    
    # Linguistic analysis
    language: str
    entities: List[Dict[str, str]]
    keywords: List[str]
    key_phrases: List[str]
    
    # Semantic analysis
    topics: List[str]
    sentiment: str
    urgency_level: str
    
    # Structure analysis
    has_question_words: bool
    has_negation: bool
    has_temporal_references: bool
    has_conditional_logic: bool
    
    # Enhancement suggestions
    expansion_candidates: List[str]
    decomposition_suggestions: List[str]
    clarification_needed: List[str]

@dataclass
class EnhancedQuery:
    """Enhanced query with multiple variations."""
    
    original: str
    primary_enhanced: str
    
    # Query variations
    expanded_queries: List[str] = field(default_factory=list)
    decomposed_queries: List[str] = field(default_factory=list)
    reformulated_queries: List[str] = field(default_factory=list)
    
    # Extracted components
    keywords: List[str] = field(default_factory=list)
    entities: List[str] = field(default_factory=list)
    intent_keywords: List[str] = field(default_factory=list)
    
    # Metadata
    confidence_score: float = 1.0
    enhancement_methods: List[str] = field(default_factory=list)
    processing_notes: List[str] = field(default_factory=list)

class QueryType(Enum):
    """Types of queries."""
    QUESTION = "question"
    COMMAND = "command"
    SEARCH = "search"
    COMPARISON = "comparison"
    TROUBLESHOOTING = "troubleshooting"
    PROCEDURAL = "procedural"
    FACTUAL = "factual"
    OPINION = "opinion"

class QueryComplexity(Enum):
    """Query complexity levels."""
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"
    MULTI_PART = "multi_part"

class QueryEnhancer:
    """Advanced query enhancement with NLP and domain knowledge."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Initialize NLP components
        self.nlp_model = None
        self.lemmatizer = None
        self.stopwords_set = set()
        
        if NLP_LIBS_AVAILABLE:
            try:
                self.nlp_model = spacy.load("en_core_web_sm")
                self.lemmatizer = WordNetLemmatizer()
                nltk.download('stopwords', quiet=True)
                nltk.download('wordnet', quiet=True)
                nltk.download('punkt', quiet=True)
                nltk.download('averaged_perceptron_tagger', quiet=True)
                self.stopwords_set = set(stopwords.words('english'))
                self.logger.info("NLP components initialized successfully")
            except Exception as e:
                self.logger.warning(f"Advanced NLP features unavailable: {e}")
        
        # Initialize domain knowledge
        self._initialize_domain_knowledge()
        
        # Query processing statistics
        self.processing_stats = defaultdict(int)

    def _initialize_domain_knowledge(self):
        """Initialize domain-specific knowledge bases."""
        
        # Customer support domain synonyms
        self.domain_synonyms = {
            'error': ['bug', 'issue', 'problem', 'failure', 'glitch', 'malfunction'],
            'login': ['sign in', 'authentication', 'access', 'log in', 'signin'],
            'payment': ['billing', 'charge', 'transaction', 'invoice', 'subscription'],
            'setup': ['configuration', 'installation', 'initialization', 'config'],
            'help': ['support', 'assistance', 'guidance', 'aid', 'troubleshoot'],
            'api': ['interface', 'endpoint', 'service', 'integration', 'webhook'],
            'account': ['profile', 'user', 'registration', 'membership'],
            'password': ['credentials', 'authentication', 'security', 'access code'],
            'update': ['upgrade', 'modification', 'change', 'revision'],
            'delete': ['remove', 'erase', 'eliminate', 'cancel']
        }
        
        # Intent patterns
        self.intent_patterns = {
            'information_seeking': [
                r'\b(what|how|when|where|why|which|who)\b',
                r'\b(explain|describe|tell me|show me)\b',
                r'\b(information|details|documentation)\b'
            ],
            'problem_solving': [
                r'\b(error|issue|problem|bug|broken|not working)\b',
                r'\b(fix|solve|resolve|troubleshoot|repair)\b',
                r'\b(help|stuck|unable|cannot|can\'t)\b'
            ],
            'task_completion': [
                r'\b(how to|step by step|guide|tutorial)\b',
                r'\b(create|setup|configure|install)\b',
                r'\b(process|procedure|workflow)\b'
            ],
            'comparison': [
                r'\b(compare|difference|versus|vs|better|best)\b',
                r'\b(alternative|option|choice)\b'
            ],
            'status_inquiry': [
                r'\b(status|state|condition|progress)\b',
                r'\b(check|verify|confirm|validate)\b'
            ]
        }
        
        # Urgency indicators
        self.urgency_indicators = {
            'critical': ['urgent', 'emergency', 'critical', 'asap', 'immediately', 'now'],
            'high': ['important', 'priority', 'soon', 'quickly', 'fast'],
            'medium': ['when possible', 'convenient', 'normal'],
            'low': ['eventually', 'nice to have', 'future', 'someday']
        }
        
        # Question word mappings
        self.question_mappings = {
            'what': ['definition', 'explanation', 'description'],
            'how': ['procedure', 'method', 'process', 'steps'],
            'when': ['time', 'schedule', 'timing'],
            'where': ['location', 'place', 'position'],
            'why': ['reason', 'cause', 'explanation'],
            'who': ['person', 'responsible', 'contact'],
            'which': ['choice', 'option', 'selection']
        }

    async def analyze_query(self, query: str) -> QueryAnalysis:
        """Perform comprehensive query analysis."""
        
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Basic preprocessing
            cleaned_query = self._clean_query(query)
            
            # Basic properties
            word_count = len(cleaned_query.split())
            query_length = len(cleaned_query)
            
            # Classification
            query_type = await self._classify_query_type(cleaned_query)
            intent = await self._detect_intent(cleaned_query)
            complexity = await self._assess_complexity(cleaned_query)
            
            # Linguistic analysis
            language = await self._detect_language(cleaned_query)
            entities = await self._extract_entities(cleaned_query)
            keywords = await self._extract_keywords(cleaned_query)
            key_phrases = await self._extract_key_phrases(cleaned_query)
            
            # Semantic analysis
            topics = await self._extract_topics(cleaned_query)
            sentiment = await self._analyze_sentiment(cleaned_query)
            urgency_level = await self._detect_urgency(cleaned_query)
            
            # Structure analysis
            structure_analysis = await self._analyze_structure(cleaned_query)
            
            # Enhancement suggestions
            expansion_candidates = await self._suggest_expansions(cleaned_query)
            decomposition_suggestions = await self._suggest_decomposition(cleaned_query)
            clarification_needed = await self._identify_clarification_needs(cleaned_query)
            
            analysis = QueryAnalysis(
                original_query=query,
                cleaned_query=cleaned_query,
                query_length=query_length,
                word_count=word_count,
                query_type=query_type,
                intent=intent,
                complexity=complexity,
                language=language,
                entities=entities,
                keywords=keywords,
                key_phrases=key_phrases,
                topics=topics,
                sentiment=sentiment,
                urgency_level=urgency_level,
                has_question_words=structure_analysis['has_question_words'],
                has_negation=structure_analysis['has_negation'],
                has_temporal_references=structure_analysis['has_temporal_references'],
                has_conditional_logic=structure_analysis['has_conditional_logic'],
                expansion_candidates=expansion_candidates,
                decomposition_suggestions=decomposition_suggestions,
                clarification_needed=clarification_needed
            )
            
            # Update statistics
            processing_time = asyncio.get_event_loop().time() - start_time
            self.processing_stats['total_analyses'] += 1
            self.processing_stats['total_processing_time'] += processing_time
            
            self.logger.info(f"Query analysis completed in {processing_time:.3f}s")
            return analysis
            
        except Exception as e:
            self.logger.error(f"Query analysis failed: {e}")
            return self._create_fallback_analysis(query)

    async def enhance_query(self, 
                          query: str,
                          analysis: Optional[QueryAnalysis] = None,
                          enhancement_methods: Optional[List[str]] = None) -> EnhancedQuery:
        """Enhance query with multiple techniques."""
        
        if analysis is None:
            analysis = await self.analyze_query(query)
        
        enhancement_methods = enhancement_methods or ['expand', 'decompose', 'reformulate']
        
        try:
            enhanced = EnhancedQuery(
                original=query,
                primary_enhanced=analysis.cleaned_query
            )
            
            # Apply enhancement methods
            if 'expand' in enhancement_methods:
                enhanced.expanded_queries = await self._expand_query(analysis)
                enhanced.enhancement_methods.append('expansion')
            
            if 'decompose' in enhancement_methods:
                enhanced.decomposed_queries = await self._decompose_query(analysis)
                enhanced.enhancement_methods.append('decomposition')
            
            if 'reformulate' in enhancement_methods:
                enhanced.reformulated_queries = await self._reformulate_query(analysis)
                enhanced.enhancement_methods.append('reformulation')
            
            # Extract components
            enhanced.keywords = analysis.keywords
            enhanced.entities = [entity['value'] for entity in analysis.entities]
            enhanced.intent_keywords = await self._extract_intent_keywords(analysis)
            
            # Calculate confidence score
            enhanced.confidence_score = await self._calculate_confidence(analysis, enhanced)
            
            # Select primary enhanced query
            enhanced.primary_enhanced = await self._select_primary_enhancement(enhanced)
            
            self.logger.info(f"Query enhanced with {len(enhanced.enhancement_methods)} methods")
            return enhanced
            
        except Exception as e:
            self.logger.error(f"Query enhancement failed: {e}")
            return EnhancedQuery(original=query, primary_enhanced=query)

    def _clean_query(self, query: str) -> str:
        """Clean and normalize query text."""
        
        # Remove extra whitespace
        cleaned = ' '.join(query.split())
        
        # Fix common typos and normalize
        cleaned = cleaned.strip()
        
        # Normalize punctuation
        cleaned = re.sub(r'[?!]{2,}', '?', cleaned)
        cleaned = re.sub(r'\.{2,}', '.', cleaned)
        
        return cleaned

    async def _classify_query_type(self, query: str) -> str:
        """Classify the type of query."""
        
        query_lower = query.lower()
        
        # Question patterns
        if re.search(r'\b(what|how|when|where|why|which|who)\b', query_lower) or query.endswith('?'):
            return QueryType.QUESTION.value
        
        # Command patterns
        if re.search(r'^(show|tell|explain|describe|list|find)', query_lower):
            return QueryType.COMMAND.value
        
        # Comparison patterns
        if re.search(r'\b(compare|difference|versus|vs|better|best)\b', query_lower):
            return QueryType.COMPARISON.value
        
        # Troubleshooting patterns
        if re.search(r'\b(error|issue|problem|bug|broken|not working|fix)\b', query_lower):
            return QueryType.TROUBLESHOOTING.value
        
        # Procedural patterns
        if re.search(r'\b(how to|step by step|guide|tutorial|process)\b', query_lower):
            return QueryType.PROCEDURAL.value
        
        # Default to search
        return QueryType.SEARCH.value

    async def _detect_intent(self, query: str) -> str:
        """Detect the intent behind the query."""
        
        query_lower = query.lower()
        intent_scores = {}
        
        for intent, patterns in self.intent_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, query_lower))
                score += matches
            intent_scores[intent] = score
        
        if intent_scores:
            return max(intent_scores, key=intent_scores.get)
        
        return 'information_seeking'

    async def _assess_complexity(self, query: str) -> str:
        """Assess query complexity."""
        
        word_count = len(query.split())
        
        # Count complexity indicators
        complexity_indicators = 0
        
        # Multiple questions
        if query.count('?') > 1:
            complexity_indicators += 1
        
        # Conjunctions
        if re.search(r'\b(and|or|but|however|although|while)\b', query.lower()):
            complexity_indicators += 1
        
        # Conditional logic
        if re.search(r'\b(if|when|unless|provided|assuming)\b', query.lower()):
            complexity_indicators += 1
        
        # Multiple topics
        if len(re.findall(r'\b(about|regarding|concerning)\b', query.lower())) > 1:
            complexity_indicators += 1
        
        # Determine complexity
        if word_count > 20 or complexity_indicators >= 3:
            return QueryComplexity.COMPLEX.value
        elif word_count > 10 or complexity_indicators >= 2:
            return QueryComplexity.MODERATE.value
        elif complexity_indicators > 0:
            return QueryComplexity.MULTI_PART.value
        else:
            return QueryComplexity.SIMPLE.value

    async def _detect_language(self, query: str) -> str:
        """Detect query language."""
        
        try:
            if NLP_LIBS_AVAILABLE:
                blob = TextBlob(query)
                return blob.detect_language()
        except:
            pass
        
        # Default to English
        return 'en'

    async def _extract_entities(self, query: str) -> List[Dict[str, str]]:
        """Extract named entities from query."""
        
        entities = []
        
        try:
            # Extract common patterns
            # Email addresses
            emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', query)
            for email in emails:
                entities.append({'type': 'email', 'value': email})
            
            # URLs
            urls = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', query)
            for url in urls:
                entities.append({'type': 'url', 'value': url})
            
            # Phone numbers
            phones = re.findall(r'(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}', query)
            for phone in phones:
                entities.append({'type': 'phone', 'value': phone})
            
            # Use spaCy for advanced entity extraction
            if self.nlp_model and NLP_LIBS_AVAILABLE:
                doc = self.nlp_model(query)
                for ent in doc.ents:
                    entities.append({
                        'type': ent.label_,
                        'value': ent.text,
                        'confidence': 1.0
                    })
            
        except Exception as e:
            self.logger.warning(f"Entity extraction failed: {e}")
        
        return entities

    async def _extract_keywords(self, query: str) -> List[str]:
        """Extract important keywords from query."""
        
        try:
            # Remove stopwords and extract meaningful words
            words = word_tokenize(query.lower()) if NLP_LIBS_AVAILABLE else query.lower().split()
            
            # Filter out stopwords and short words
            keywords = []
            for word in words:
                if (len(word) > 2 and 
                    word.isalpha() and 
                    (not self.stopwords_set or word not in self.stopwords_set)):
                    keywords.append(word)
            
            # Lemmatize if possible
            if self.lemmatizer and NLP_LIBS_AVAILABLE:
                keywords = [self.lemmatizer.lemmatize(word) for word in keywords]
            
            # Remove duplicates while preserving order
            seen = set()
            unique_keywords = []
            for keyword in keywords:
                if keyword not in seen:
                    seen.add(keyword)
                    unique_keywords.append(keyword)
            
            return unique_keywords[:10]  # Limit to top 10
            
        except Exception as e:
            self.logger.warning(f"Keyword extraction failed: {e}")
            return query.lower().split()[:5]

    async def _extract_key_phrases(self, query: str) -> List[str]:
        """Extract key phrases from query."""
        
        phrases = []
        
        try:
            # Extract noun phrases using spaCy
            if self.nlp_model and NLP_LIBS_AVAILABLE:
                doc = self.nlp_model(query)
                for chunk in doc.noun_chunks:
                    if len(chunk.text.split()) > 1:  # Multi-word phrases
                        phrases.append(chunk.text.lower())
            
            # Extract quoted phrases
            quoted_phrases = re.findall(r'"([^"]*)"', query)
            phrases.extend([phrase.lower() for phrase in quoted_phrases])
            
            # Extract common patterns
            patterns = [
                r'\b\w+\s+\w+\s+\w+\b',  # 3-word phrases
                r'\b\w+\s+\w+\b'         # 2-word phrases
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, query.lower())
                phrases.extend(matches)
            
            # Remove duplicates and filter
            unique_phrases = list(set(phrases))
            filtered_phrases = [p for p in unique_phrases if len(p.split()) >= 2]
            
            return filtered_phrases[:5]  # Limit to top 5
            
        except Exception as e:
            self.logger.warning(f"Key phrase extraction failed: {e}")
            return []

    async def _extract_topics(self, query: str) -> List[str]:
        """Extract main topics from query."""
        
        topics = []
        query_lower = query.lower()
        
        # Domain-specific topic detection
        topic_keywords = {
            'billing': ['payment', 'billing', 'invoice', 'subscription', 'charge', 'refund'],
            'technical': ['api', 'integration', 'setup', 'configuration', 'code', 'error'],
            'account': ['account', 'profile', 'login', 'password', 'user', 'registration'],
            'product': ['feature', 'functionality', 'update', 'release', 'product'],
            'support': ['help', 'support', 'assistance', 'service', 'contact', 'troubleshoot']
        }
        
        for topic, keywords in topic_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                topics.append(topic)
        
        return topics

    async def _analyze_sentiment(self, query: str) -> str:
        """Analyze sentiment of the query."""
        
        try:
            if NLP_LIBS_AVAILABLE:
                blob = TextBlob(query)
                polarity = blob.sentiment.polarity
                
                if polarity > 0.1:
                    return 'positive'
                elif polarity < -0.1:
                    return 'negative'
                else:
                    return 'neutral'
        except:
            pass
        
        # Fallback sentiment analysis
        positive_words = ['good', 'great', 'excellent', 'love', 'like', 'appreciate']
        negative_words = ['bad', 'terrible', 'hate', 'problem', 'issue', 'error', 'broken']
        
        query_lower = query.lower()
        positive_count = sum(1 for word in positive_words if word in query_lower)
        negative_count = sum(1 for word in negative_words if word in query_lower)
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'

    async def _detect_urgency(self, query: str) -> str:
        """Detect urgency level of the query."""
        
        query_lower = query.lower()
        
        for urgency_level, indicators in self.urgency_indicators.items():
            if any(indicator in query_lower for indicator in indicators):
                return urgency_level
        
        return 'medium'

    async def _analyze_structure(self, query: str) -> Dict[str, bool]:
        """Analyze structural elements of the query."""
        
        query_lower = query.lower()
        
        return {
            'has_question_words': bool(re.search(r'\b(what|how|when|where|why|which|who)\b', query_lower)),
            'has_negation': bool(re.search(r'\b(not|no|never|none|nothing|neither)\b', query_lower)),
            'has_temporal_references': bool(re.search(r'\b(today|tomorrow|yesterday|now|soon|later|before|after)\b', query_lower)),
            'has_conditional_logic': bool(re.search(r'\b(if|when|unless|provided|assuming)\b', query_lower))
        }

    async def _suggest_expansions(self, query: str) -> List[str]:
        """Suggest query expansions."""
        
        expansions = []
        query_lower = query.lower()
        
        # Add synonyms for key terms
        for term, synonyms in self.domain_synonyms.items():
            if term in query_lower:
                for synonym in synonyms[:2]:  # Limit to 2 synonyms per term
                    expanded = query_lower.replace(term, synonym)
                    if expanded != query_lower:
                        expansions.append(expanded)
        
        # Add related terms based on question words
        for qword, related_terms in self.question_mappings.items():
            if qword in query_lower:
                for term in related_terms[:2]:
                    expansions.append(f"{query} {term}")
        
        return expansions[:5]  # Limit to top 5

    async def _suggest_decomposition(self, query: str) -> List[str]:
        """Suggest query decomposition."""
        
        decomposed = []
        
        # Split on conjunctions
        conjunctions = ['and', 'or', 'but', 'also', 'plus']
        for conj in conjunctions:
            if f' {conj} ' in query.lower():
                parts = query.lower().split(f' {conj} ')
                decomposed.extend([part.strip() for part in parts if part.strip()])
        
        # Split multiple questions
        if query.count('?') > 1:
            questions = query.split('?')
            decomposed.extend([q.strip() + '?' for q in questions if q.strip()])
        
        return decomposed

    async def _identify_clarification_needs(self, query: str) -> List[str]:
        """Identify what clarifications might be needed."""
        
        clarifications = []
        query_lower = query.lower()
        
        # Vague terms that need clarification
        vague_terms = ['it', 'this', 'that', 'thing', 'stuff', 'issue', 'problem']
        for term in vague_terms:
            if f' {term} ' in query_lower or query_lower.startswith(f'{term} '):
                clarifications.append(f"What specifically does '{term}' refer to?")
        
        # Missing context
        if len(query.split()) < 5:
            clarifications.append("Could you provide more context or details?")
        
        # Ambiguous pronouns
        pronouns = ['he', 'she', 'they', 'we', 'you']
        for pronoun in pronouns:
            if pronoun in query_lower:
                clarifications.append(f"Who does '{pronoun}' refer to?")
        
        return clarifications[:3]  # Limit to top 3

    async def _expand_query(self, analysis: QueryAnalysis) -> List[str]:
        """Generate expanded query variations."""
        
        expanded = []
        
        # Use analysis results for intelligent expansion
        base_query = analysis.cleaned_query
        
        # Add synonyms
        for keyword in analysis.keywords[:3]:  # Top 3 keywords
            if keyword in self.domain_synonyms:
                for synonym in self.domain_synonyms[keyword][:2]:
                    expanded_query = base_query.replace(keyword, synonym)
                    if expanded_query != base_query:
                        expanded.append(expanded_query)
        
        # Add related terms based on topics
        for topic in analysis.topics:
            expanded.append(f"{base_query} {topic}")
        
        # Add intent-specific expansions
        if analysis.intent == 'problem_solving':
            expanded.extend([
                f"how to fix {base_query}",
                f"troubleshoot {base_query}",
                f"solve {base_query}"
            ])
        elif analysis.intent == 'information_seeking':
            expanded.extend([
                f"what is {base_query}",
                f"explain {base_query}",
                f"information about {base_query}"
            ])
        
        return expanded[:5]

    async def _decompose_query(self, analysis: QueryAnalysis) -> List[str]:
        """Decompose complex queries into simpler parts."""
        
        return analysis.decomposition_suggestions

    async def _reformulate_query(self, analysis: QueryAnalysis) -> List[str]:
        """Reformulate query in different ways."""
        
        reformulated = []
        base_query = analysis.cleaned_query
        
        # Convert questions to statements
        if analysis.query_type == QueryType.QUESTION.value:
            if base_query.startswith('how'):
                reformulated.append(base_query.replace('how to', 'steps to'))
                reformulated.append(base_query.replace('how', 'method for'))
            elif base_query.startswith('what'):
                reformulated.append(base_query.replace('what is', 'definition of'))
                reformulated.append(base_query.replace('what', 'information about'))
        
        # Add formal/informal variations
        if 'can\'t' in base_query:
            reformulated.append(base_query.replace('can\'t', 'cannot'))
        if 'won\'t' in base_query:
            reformulated.append(base_query.replace('won\'t', 'will not'))
        
        return reformulated[:3]

    async def _extract_intent_keywords(self, analysis: QueryAnalysis) -> List[str]:
        """Extract keywords specific to the detected intent."""
        
        intent_keywords = []
        
        if analysis.intent in self.intent_patterns:
            patterns = self.intent_patterns[analysis.intent]
            for pattern in patterns:
                matches = re.findall(pattern, analysis.cleaned_query.lower())
                intent_keywords.extend(matches)
        
        return list(set(intent_keywords))

    async def _calculate_confidence(self, analysis: QueryAnalysis, enhanced: EnhancedQuery) -> float:
        """Calculate confidence score for the enhancement."""
        
        confidence = 1.0
        
        # Reduce confidence for very short queries
        if analysis.word_count < 3:
            confidence *= 0.7
        
        # Reduce confidence for complex queries
        if analysis.complexity == QueryComplexity.COMPLEX.value:
            confidence *= 0.8
        
        # Increase confidence if we found entities and keywords
        if analysis.entities:
            confidence *= 1.1
        if len(analysis.keywords) > 2:
            confidence *= 1.05
        
        # Ensure confidence is between 0 and 1
        return max(0.0, min(confidence, 1.0))

    async def _select_primary_enhancement(self, enhanced: EnhancedQuery) -> str:
        """Select the best enhanced query as primary."""
        
        # For now, return the first expanded query if available
        if enhanced.expanded_queries:
            return enhanced.expanded_queries[0]
        elif enhanced.reformulated_queries:
            return enhanced.reformulated_queries[0]
        else:
            return enhanced.original

    def _create_fallback_analysis(self, query: str) -> QueryAnalysis:
        """Create a basic analysis when full analysis fails."""
        
        return QueryAnalysis(
            original_query=query,
            cleaned_query=query.strip(),
            query_length=len(query),
            word_count=len(query.split()),
            query_type=QueryType.SEARCH.value,
            intent='information_seeking',
            complexity=QueryComplexity.SIMPLE.value,
            language='en',
            entities=[],
            keywords=query.lower().split()[:5],
            key_phrases=[],
            topics=[],
            sentiment='neutral',
            urgency_level='medium',
            has_question_words=False,
            has_negation=False,
            has_temporal_references=False,
            has_conditional_logic=False,
            expansion_candidates=[],
            decomposition_suggestions=[],
            clarification_needed=[]
        )

    async def get_enhancement_stats(self) -> Dict[str, Any]:
        """Get query enhancement statistics."""
        
        stats = {
            'total_analyses': self.processing_stats['total_analyses'],
            'avg_processing_time': 0.0
        }
        
        if self.processing_stats['total_analyses'] > 0:
            stats['avg_processing_time'] = (
                self.processing_stats['total_processing_time'] / 
                self.processing_stats['total_analyses']
            )
        
        return stats
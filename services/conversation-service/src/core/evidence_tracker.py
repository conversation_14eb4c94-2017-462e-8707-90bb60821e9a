"""
Evidence Tracker
Map response content to source evidence for transparency and verification
"""

from typing import Dict, List, Any, Optional, Set, Tuple
import logging
import re
import asyncio
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import json
import hashlib
from collections import defaultdict

class EvidenceType(Enum):
    """Types of evidence that can be tracked."""
    DIRECT_QUOTE = "direct_quote"
    PARAPHRASE = "paraphrase"
    INFERENCE = "inference"
    SYNTHESIS = "synthesis"
    BACKGROUND = "background"

class ConfidenceLevel(Enum):
    """Confidence levels for evidence mapping."""
    VERY_HIGH = "very_high"  # 0.9+
    HIGH = "high"           # 0.7-0.9
    MEDIUM = "medium"       # 0.5-0.7
    LOW = "low"            # 0.3-0.5
    VERY_LOW = "very_low"  # <0.3

@dataclass
class SourceEvidence:
    """Evidence from a source document."""
    
    source_id: str
    source_title: str
    content_snippet: str
    start_position: int
    end_position: int
    
    # Evidence metadata
    evidence_type: EvidenceType
    confidence_score: float
    relevance_score: float
    
    # Context information
    surrounding_context: str = ""
    section_title: str = ""
    document_metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ResponseSegment:
    """A segment of the response with its evidence mapping."""
    
    segment_id: str
    content: str
    start_position: int
    end_position: int
    
    # Evidence mapping
    supporting_evidence: List[SourceEvidence] = field(default_factory=list)
    evidence_strength: float = 0.0
    
    # Verification status
    is_verified: bool = False
    verification_notes: str = ""
    potential_issues: List[str] = field(default_factory=list)

@dataclass
class EvidenceMap:
    """Complete evidence mapping for a response."""
    
    response_id: str
    response_content: str
    segments: List[ResponseSegment] = field(default_factory=list)
    
    # Overall metrics
    overall_evidence_coverage: float = 0.0
    total_sources_used: int = 0
    unsupported_segments: List[str] = field(default_factory=list)
    
    # Quality indicators
    evidence_quality_score: float = 0.0
    transparency_score: float = 0.0
    
    # Metadata
    created_at: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)

class EvidenceTracker:
    """Track and map response content to source evidence."""
    
    def __init__(self, config=None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Evidence tracking settings
        self.tracking_settings = {
            "min_segment_length": 10,
            "max_segment_length": 200,
            "min_evidence_confidence": 0.3,
            "similarity_threshold": 0.7,
            "context_window_size": 100,
            "enable_fuzzy_matching": True
        }
        
        # Evidence patterns
        self.evidence_patterns = self._initialize_evidence_patterns()
        
        # Cached evidence maps
        self.evidence_cache: Dict[str, EvidenceMap] = {}
    
    def _initialize_evidence_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for different types of evidence."""
        
        return {
            "direct_quote": [
                r'"([^"]+)"',
                r"'([^']+)'",
                r"according to.*?[,.]",
                r"states that.*?[,.]",
                r"mentions.*?[,.]"
            ],
            "paraphrase": [
                r"in other words.*?[,.]",
                r"essentially.*?[,.]",
                r"basically.*?[,.]",
                r"to put it simply.*?[,.]"
            ],
            "inference": [
                r"this suggests.*?[,.]",
                r"this implies.*?[,.]",
                r"we can conclude.*?[,.]",
                r"it follows that.*?[,.]"
            ],
            "synthesis": [
                r"combining.*?[,.]",
                r"taking together.*?[,.]",
                r"overall.*?[,.]",
                r"in summary.*?[,.]"
            ]
        }
    
    async def create_evidence_map(self, 
                                response_content: str, 
                                source_documents: List[Dict[str, Any]], 
                                response_id: str = None) -> EvidenceMap:
        """Create comprehensive evidence mapping for a response."""
        
        try:
            response_id = response_id or self._generate_response_id(response_content)
            
            # Segment the response
            segments = await self._segment_response(response_content)
            
            # Map evidence for each segment
            mapped_segments = []
            for segment in segments:
                evidence_segment = await self._map_segment_evidence(
                    segment, source_documents
                )
                mapped_segments.append(evidence_segment)
            
            # Calculate overall metrics
            evidence_map = EvidenceMap(
                response_id=response_id,
                response_content=response_content,
                segments=mapped_segments
            )
            
            await self._calculate_evidence_metrics(evidence_map, source_documents)
            
            # Cache the evidence map
            self.evidence_cache[response_id] = evidence_map
            
            return evidence_map
            
        except Exception as e:
            self.logger.error(f"Failed to create evidence map: {e}")
            return EvidenceMap(
                response_id=response_id or "error",
                response_content=response_content
            )
    
    async def _segment_response(self, response_content: str) -> List[Dict[str, Any]]:
        """Segment response into trackable units."""
        
        segments = []
        
        # Split by sentences first
        sentences = re.split(r'[.!?]+', response_content)
        
        current_segment = ""
        current_start = 0
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            # Check if adding this sentence would exceed max length
            potential_segment = f"{current_segment} {sentence}".strip()
            
            if (len(potential_segment) > self.tracking_settings["max_segment_length"] and 
                len(current_segment) >= self.tracking_settings["min_segment_length"]):
                
                # Save current segment
                segments.append({
                    "content": current_segment,
                    "start_position": current_start,
                    "end_position": current_start + len(current_segment)
                })
                
                # Start new segment
                current_segment = sentence
                current_start = current_start + len(current_segment) + 1
            else:
                current_segment = potential_segment
        
        # Add final segment
        if (current_segment and 
            len(current_segment) >= self.tracking_settings["min_segment_length"]):
            segments.append({
                "content": current_segment,
                "start_position": current_start,
                "end_position": current_start + len(current_segment)
            })
        
        return segments
    
    async def _map_segment_evidence(self, 
                                  segment: Dict[str, Any], 
                                  source_documents: List[Dict[str, Any]]) -> ResponseSegment:
        """Map evidence for a single response segment."""
        
        segment_id = self._generate_segment_id(segment["content"])
        
        response_segment = ResponseSegment(
            segment_id=segment_id,
            content=segment["content"],
            start_position=segment["start_position"],
            end_position=segment["end_position"]
        )
        
        # Find supporting evidence
        supporting_evidence = await self._find_supporting_evidence(
            segment["content"], source_documents
        )
        
        response_segment.supporting_evidence = supporting_evidence
        response_segment.evidence_strength = await self._calculate_evidence_strength(
            supporting_evidence
        )
        
        # Verify the segment
        await self._verify_segment(response_segment, source_documents)
        
        return response_segment
    
    async def _find_supporting_evidence(self, 
                                      segment_content: str, 
                                      source_documents: List[Dict[str, Any]]) -> List[SourceEvidence]:
        """Find evidence that supports a response segment."""
        
        evidence_list = []
        
        for source_doc in source_documents:
            source_content = source_doc.get("content", "")
            source_id = source_doc.get("id", "")
            source_title = source_doc.get("title", "")
            
            # Find potential evidence snippets
            evidence_snippets = await self._extract_evidence_snippets(
                segment_content, source_content, source_id, source_title
            )
            
            evidence_list.extend(evidence_snippets)
        
        # Sort by relevance and confidence
        evidence_list.sort(key=lambda x: x.confidence_score * x.relevance_score, reverse=True)
        
        # Filter by minimum confidence
        filtered_evidence = [
            evidence for evidence in evidence_list
            if evidence.confidence_score >= self.tracking_settings["min_evidence_confidence"]
        ]
        
        return filtered_evidence[:5]  # Limit to top 5 pieces of evidence
    
    async def _extract_evidence_snippets(self, 
                                       segment_content: str, 
                                       source_content: str, 
                                       source_id: str, 
                                       source_title: str) -> List[SourceEvidence]:
        """Extract evidence snippets from a source document."""
        
        evidence_snippets = []
        segment_lower = segment_content.lower()
        source_lower = source_content.lower()
        
        # Look for direct matches
        direct_matches = await self._find_direct_matches(
            segment_content, source_content, source_id, source_title
        )
        evidence_snippets.extend(direct_matches)
        
        # Look for semantic similarities
        semantic_matches = await self._find_semantic_matches(
            segment_content, source_content, source_id, source_title
        )
        evidence_snippets.extend(semantic_matches)
        
        # Look for keyword overlaps
        keyword_matches = await self._find_keyword_matches(
            segment_content, source_content, source_id, source_title
        )
        evidence_snippets.extend(keyword_matches)
        
        return evidence_snippets
    
    async def _find_direct_matches(self, 
                                 segment_content: str, 
                                 source_content: str, 
                                 source_id: str, 
                                 source_title: str) -> List[SourceEvidence]:
        """Find direct text matches between segment and source."""
        
        evidence_list = []
        
        # Look for exact phrase matches (3+ words)
        segment_words = segment_content.split()
        
        for i in range(len(segment_words) - 2):
            for length in range(3, min(8, len(segment_words) - i + 1)):
                phrase = " ".join(segment_words[i:i + length])
                
                if phrase.lower() in source_content.lower():
                    # Find the position in source
                    start_pos = source_content.lower().find(phrase.lower())
                    end_pos = start_pos + len(phrase)
                    
                    # Get surrounding context
                    context_start = max(0, start_pos - self.tracking_settings["context_window_size"])
                    context_end = min(len(source_content), end_pos + self.tracking_settings["context_window_size"])
                    context = source_content[context_start:context_end]
                    
                    evidence = SourceEvidence(
                        source_id=source_id,
                        source_title=source_title,
                        content_snippet=source_content[start_pos:end_pos],
                        start_position=start_pos,
                        end_position=end_pos,
                        evidence_type=EvidenceType.DIRECT_QUOTE,
                        confidence_score=min(1.0, length / 5.0),  # Longer phrases = higher confidence
                        relevance_score=0.9,
                        surrounding_context=context
                    )
                    
                    evidence_list.append(evidence)
        
        return evidence_list
    
    async def _find_semantic_matches(self, 
                                   segment_content: str, 
                                   source_content: str, 
                                   source_id: str, 
                                   source_title: str) -> List[SourceEvidence]:
        """Find semantic similarities between segment and source."""
        
        evidence_list = []
        
        # Split source into sentences for comparison
        source_sentences = re.split(r'[.!?]+', source_content)
        
        for i, sentence in enumerate(source_sentences):
            sentence = sentence.strip()
            if len(sentence) < 20:  # Skip very short sentences
                continue
            
            # Calculate semantic similarity (simplified)
            similarity_score = await self._calculate_text_similarity(
                segment_content, sentence
            )
            
            if similarity_score >= self.tracking_settings["similarity_threshold"]:
                # Find position in original source
                start_pos = source_content.find(sentence)
                end_pos = start_pos + len(sentence)
                
                # Get context
                context_start = max(0, start_pos - self.tracking_settings["context_window_size"])
                context_end = min(len(source_content), end_pos + self.tracking_settings["context_window_size"])
                context = source_content[context_start:context_end]
                
                evidence = SourceEvidence(
                    source_id=source_id,
                    source_title=source_title,
                    content_snippet=sentence,
                    start_position=start_pos,
                    end_position=end_pos,
                    evidence_type=EvidenceType.PARAPHRASE,
                    confidence_score=similarity_score,
                    relevance_score=similarity_score,
                    surrounding_context=context
                )
                
                evidence_list.append(evidence)
        
        return evidence_list
    
    async def _find_keyword_matches(self, 
                                  segment_content: str, 
                                  source_content: str, 
                                  source_id: str, 
                                  source_title: str) -> List[SourceEvidence]:
        """Find evidence based on keyword overlap."""
        
        evidence_list = []
        
        # Extract keywords from segment
        segment_keywords = set(re.findall(r'\b\w{4,}\b', segment_content.lower()))
        
        # Split source into paragraphs
        source_paragraphs = source_content.split('\n\n')
        
        for paragraph in source_paragraphs:
            if len(paragraph.strip()) < 50:  # Skip short paragraphs
                continue
            
            # Extract keywords from paragraph
            paragraph_keywords = set(re.findall(r'\b\w{4,}\b', paragraph.lower()))
            
            # Calculate keyword overlap
            overlap = len(segment_keywords & paragraph_keywords)
            total_keywords = len(segment_keywords | paragraph_keywords)
            
            if total_keywords > 0:
                overlap_ratio = overlap / total_keywords
                
                if overlap_ratio >= 0.3:  # At least 30% keyword overlap
                    # Find position in source
                    start_pos = source_content.find(paragraph)
                    end_pos = start_pos + len(paragraph)
                    
                    evidence = SourceEvidence(
                        source_id=source_id,
                        source_title=source_title,
                        content_snippet=paragraph[:200] + "..." if len(paragraph) > 200 else paragraph,
                        start_position=start_pos,
                        end_position=end_pos,
                        evidence_type=EvidenceType.BACKGROUND,
                        confidence_score=overlap_ratio,
                        relevance_score=overlap_ratio,
                        surrounding_context=paragraph
                    )
                    
                    evidence_list.append(evidence)
        
        return evidence_list

    async def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two text segments."""

        # Simple word overlap similarity (can be enhanced with embeddings)
        words1 = set(re.findall(r'\b\w+\b', text1.lower()))
        words2 = set(re.findall(r'\b\w+\b', text2.lower()))

        if not words1 or not words2:
            return 0.0

        intersection = len(words1 & words2)
        union = len(words1 | words2)

        return intersection / union if union > 0 else 0.0

    async def _calculate_evidence_strength(self, evidence_list: List[SourceEvidence]) -> float:
        """Calculate overall evidence strength for a segment."""

        if not evidence_list:
            return 0.0

        # Weight evidence by type and confidence
        type_weights = {
            EvidenceType.DIRECT_QUOTE: 1.0,
            EvidenceType.PARAPHRASE: 0.8,
            EvidenceType.INFERENCE: 0.6,
            EvidenceType.SYNTHESIS: 0.7,
            EvidenceType.BACKGROUND: 0.4
        }

        total_strength = 0.0
        total_weight = 0.0

        for evidence in evidence_list:
            weight = type_weights.get(evidence.evidence_type, 0.5)
            strength = evidence.confidence_score * evidence.relevance_score * weight
            total_strength += strength
            total_weight += weight

        return total_strength / total_weight if total_weight > 0 else 0.0

    async def _verify_segment(self, segment: ResponseSegment, source_documents: List[Dict[str, Any]]):
        """Verify a response segment against source evidence."""

        issues = []

        # Check if segment has sufficient evidence
        if not segment.supporting_evidence:
            issues.append("No supporting evidence found")
            segment.is_verified = False
        elif segment.evidence_strength < 0.5:
            issues.append("Weak evidence support")
            segment.is_verified = False
        else:
            segment.is_verified = True

        # Check for potential contradictions
        contradictions = await self._check_contradictions(segment, source_documents)
        if contradictions:
            issues.extend(contradictions)
            segment.is_verified = False

        segment.potential_issues = issues
        segment.verification_notes = "; ".join(issues) if issues else "Verified with strong evidence"

    async def _check_contradictions(self,
                                  segment: ResponseSegment,
                                  source_documents: List[Dict[str, Any]]) -> List[str]:
        """Check for contradictions between segment and sources."""

        contradictions = []

        # Simple contradiction detection patterns
        contradiction_patterns = [
            (r'\bnot\s+\w+', r'\bis\s+\w+'),
            (r'\bno\s+\w+', r'\byes\s+\w+'),
            (r'\bfalse\b', r'\btrue\b'),
            (r'\bincorrect\b', r'\bcorrect\b'),
            (r'\bunable\s+to\b', r'\bable\s+to\b')
        ]

        segment_lower = segment.content.lower()

        for source_doc in source_documents:
            source_content = source_doc.get("content", "").lower()

            for neg_pattern, pos_pattern in contradiction_patterns:
                if (re.search(neg_pattern, segment_lower) and
                    re.search(pos_pattern, source_content)):
                    contradictions.append(f"Potential contradiction detected with {source_doc.get('title', 'source')}")
                elif (re.search(pos_pattern, segment_lower) and
                      re.search(neg_pattern, source_content)):
                    contradictions.append(f"Potential contradiction detected with {source_doc.get('title', 'source')}")

        return contradictions

    async def _calculate_evidence_metrics(self,
                                        evidence_map: EvidenceMap,
                                        source_documents: List[Dict[str, Any]]):
        """Calculate overall evidence metrics for the response."""

        total_segments = len(evidence_map.segments)
        verified_segments = sum(1 for seg in evidence_map.segments if seg.is_verified)

        # Evidence coverage
        evidence_map.overall_evidence_coverage = verified_segments / total_segments if total_segments > 0 else 0.0

        # Total sources used
        all_source_ids = set()
        for segment in evidence_map.segments:
            for evidence in segment.supporting_evidence:
                all_source_ids.add(evidence.source_id)
        evidence_map.total_sources_used = len(all_source_ids)

        # Unsupported segments
        evidence_map.unsupported_segments = [
            seg.content[:50] + "..." if len(seg.content) > 50 else seg.content
            for seg in evidence_map.segments
            if not seg.is_verified
        ]

        # Evidence quality score
        if evidence_map.segments:
            avg_evidence_strength = sum(seg.evidence_strength for seg in evidence_map.segments) / len(evidence_map.segments)
            evidence_map.evidence_quality_score = avg_evidence_strength

        # Transparency score (based on coverage and quality)
        evidence_map.transparency_score = (
            evidence_map.overall_evidence_coverage * 0.6 +
            evidence_map.evidence_quality_score * 0.4
        )

    def _generate_response_id(self, response_content: str) -> str:
        """Generate unique ID for a response."""

        content_hash = hashlib.md5(response_content.encode()).hexdigest()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"resp_{timestamp}_{content_hash[:8]}"

    def _generate_segment_id(self, segment_content: str) -> str:
        """Generate unique ID for a response segment."""

        content_hash = hashlib.md5(segment_content.encode()).hexdigest()
        return f"seg_{content_hash[:12]}"

    async def get_evidence_summary(self, evidence_map: EvidenceMap) -> Dict[str, Any]:
        """Get a summary of evidence mapping results."""

        summary = {
            "response_id": evidence_map.response_id,
            "total_segments": len(evidence_map.segments),
            "verified_segments": sum(1 for seg in evidence_map.segments if seg.is_verified),
            "evidence_coverage": evidence_map.overall_evidence_coverage,
            "evidence_quality": evidence_map.evidence_quality_score,
            "transparency_score": evidence_map.transparency_score,
            "sources_used": evidence_map.total_sources_used,
            "unsupported_count": len(evidence_map.unsupported_segments),
            "evidence_breakdown": {}
        }

        # Evidence type breakdown
        evidence_types = defaultdict(int)
        for segment in evidence_map.segments:
            for evidence in segment.supporting_evidence:
                evidence_types[evidence.evidence_type.value] += 1

        summary["evidence_breakdown"] = dict(evidence_types)

        return summary

    async def export_evidence_map(self, evidence_map: EvidenceMap, file_path: str):
        """Export evidence map to JSON file."""

        try:
            # Convert to serializable format
            export_data = {
                "response_id": evidence_map.response_id,
                "response_content": evidence_map.response_content,
                "segments": [],
                "metrics": {
                    "evidence_coverage": evidence_map.overall_evidence_coverage,
                    "evidence_quality": evidence_map.evidence_quality_score,
                    "transparency_score": evidence_map.transparency_score,
                    "sources_used": evidence_map.total_sources_used
                },
                "created_at": evidence_map.created_at.isoformat(),
                "last_updated": evidence_map.last_updated.isoformat()
            }

            for segment in evidence_map.segments:
                segment_data = {
                    "segment_id": segment.segment_id,
                    "content": segment.content,
                    "start_position": segment.start_position,
                    "end_position": segment.end_position,
                    "evidence_strength": segment.evidence_strength,
                    "is_verified": segment.is_verified,
                    "verification_notes": segment.verification_notes,
                    "potential_issues": segment.potential_issues,
                    "supporting_evidence": []
                }

                for evidence in segment.supporting_evidence:
                    evidence_data = {
                        "source_id": evidence.source_id,
                        "source_title": evidence.source_title,
                        "content_snippet": evidence.content_snippet,
                        "evidence_type": evidence.evidence_type.value,
                        "confidence_score": evidence.confidence_score,
                        "relevance_score": evidence.relevance_score,
                        "start_position": evidence.start_position,
                        "end_position": evidence.end_position
                    }
                    segment_data["supporting_evidence"].append(evidence_data)

                export_data["segments"].append(segment_data)

            with open(file_path, 'w') as f:
                json.dump(export_data, f, indent=2)

            self.logger.info(f"Evidence map exported to {file_path}")

        except Exception as e:
            self.logger.error(f"Failed to export evidence map: {e}")

    async def validate_response_evidence(self,
                                       response_content: str,
                                       source_documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Quick validation of response evidence without full mapping."""

        try:
            # Create evidence map
            evidence_map = await self.create_evidence_map(response_content, source_documents)

            # Generate validation report
            validation_report = {
                "is_well_supported": evidence_map.overall_evidence_coverage >= 0.7,
                "evidence_coverage": evidence_map.overall_evidence_coverage,
                "evidence_quality": evidence_map.evidence_quality_score,
                "transparency_score": evidence_map.transparency_score,
                "sources_used": evidence_map.total_sources_used,
                "unsupported_segments": len(evidence_map.unsupported_segments),
                "issues_found": [],
                "recommendations": []
            }

            # Add issues and recommendations
            if evidence_map.overall_evidence_coverage < 0.5:
                validation_report["issues_found"].append("Low evidence coverage")
                validation_report["recommendations"].append("Add more supporting sources")

            if evidence_map.evidence_quality_score < 0.6:
                validation_report["issues_found"].append("Weak evidence quality")
                validation_report["recommendations"].append("Use more direct quotes and specific references")

            if evidence_map.total_sources_used < 2:
                validation_report["issues_found"].append("Limited source diversity")
                validation_report["recommendations"].append("Include evidence from multiple sources")

            return validation_report

        except Exception as e:
            self.logger.error(f"Evidence validation failed: {e}")
            return {
                "is_well_supported": False,
                "error": str(e),
                "evidence_coverage": 0.0,
                "issues_found": ["Validation failed"],
                "recommendations": ["Review response and sources manually"]
            }

    async def get_evidence_statistics(self) -> Dict[str, Any]:
        """Get statistics about evidence tracking performance."""

        if not self.evidence_cache:
            return {"message": "No evidence maps in cache"}

        stats = {
            "total_responses_tracked": len(self.evidence_cache),
            "average_evidence_coverage": 0.0,
            "average_evidence_quality": 0.0,
            "average_transparency_score": 0.0,
            "evidence_type_distribution": defaultdict(int),
            "common_issues": defaultdict(int)
        }

        total_coverage = 0.0
        total_quality = 0.0
        total_transparency = 0.0

        for evidence_map in self.evidence_cache.values():
            total_coverage += evidence_map.overall_evidence_coverage
            total_quality += evidence_map.evidence_quality_score
            total_transparency += evidence_map.transparency_score

            # Count evidence types
            for segment in evidence_map.segments:
                for evidence in segment.supporting_evidence:
                    stats["evidence_type_distribution"][evidence.evidence_type.value] += 1

                # Count issues
                for issue in segment.potential_issues:
                    stats["common_issues"][issue] += 1

        count = len(self.evidence_cache)
        stats["average_evidence_coverage"] = total_coverage / count
        stats["average_evidence_quality"] = total_quality / count
        stats["average_transparency_score"] = total_transparency / count

        return stats

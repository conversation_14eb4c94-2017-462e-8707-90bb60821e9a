"""
Citation Manager
Manages source attribution and citation formatting for RAG responses
"""

from typing import Dict, List, Any, Optional, Tuple, Set
import logging
import re
import hashlib
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import asyncio

@dataclass
class SourceDocument:
    """Represents a source document with metadata."""
    
    id: str
    title: str
    content: str
    url: Optional[str] = None
    author: Optional[str] = None
    publication_date: Optional[datetime] = None
    document_type: str = "document"
    section: Optional[str] = None
    page_number: Optional[int] = None
    confidence_score: float = 0.0
    relevance_score: float = 0.0
    
    # Citation metadata
    citation_key: str = field(default="")
    citation_text: str = field(default="")
    
    def __post_init__(self):
        if not self.citation_key:
            self.citation_key = self._generate_citation_key()
        if not self.citation_text:
            self.citation_text = self._generate_citation_text()
    
    def _generate_citation_key(self) -> str:
        """Generate a unique citation key."""
        content_hash = hashlib.md5(f"{self.id}{self.title}".encode()).hexdigest()[:8]
        return f"ref_{content_hash}"
    
    def _generate_citation_text(self) -> str:
        """Generate formatted citation text."""
        parts = []
        
        if self.author:
            parts.append(self.author)
        
        if self.title:
            parts.append(f'"{self.title}"')
        
        if self.publication_date:
            parts.append(f"({self.publication_date.year})")
        
        if self.section:
            parts.append(f"Section: {self.section}")
        
        if self.page_number:
            parts.append(f"Page {self.page_number}")
        
        return ", ".join(parts) if parts else f"Document {self.id}"

@dataclass
class Citation:
    """Represents a citation within response text."""
    
    source_id: str
    citation_key: str
    start_position: int
    end_position: int
    cited_text: str
    citation_type: str = "inline"  # inline, footnote, reference
    confidence: float = 1.0

@dataclass
class AttributionResult:
    """Result of source attribution process."""
    
    attributed_text: str
    citations: List[Citation]
    sources: List[SourceDocument]
    attribution_confidence: float
    coverage_percentage: float  # Percentage of response covered by sources

class CitationStyle(Enum):
    """Citation formatting styles."""
    
    INLINE_NUMBERED = "inline_numbered"  # [1], [2], etc.
    INLINE_AUTHOR = "inline_author"      # (Smith, 2023)
    FOOTNOTE = "footnote"                # Footnotes at bottom
    REFERENCE_LIST = "reference_list"    # Reference list at end
    HYPERLINK = "hyperlink"              # Clickable links
    MINIMAL = "minimal"                  # Minimal citation info

class CitationManager:
    """Manages source attribution and citation formatting."""
    
    def __init__(self, citation_style: CitationStyle = CitationStyle.INLINE_NUMBERED):
        self.logger = logging.getLogger(__name__)
        self.citation_style = citation_style
        
        # Citation formatting templates
        self.citation_templates = self._initialize_citation_templates()
        
        # Source tracking
        self.source_registry: Dict[str, SourceDocument] = {}
        self.citation_counter = 0
        
        # Attribution settings
        self.min_attribution_confidence = 0.7
        self.max_citations_per_paragraph = 3
        self.citation_placement_rules = self._initialize_placement_rules()

    def _initialize_citation_templates(self) -> Dict[str, Dict[str, str]]:
        """Initialize citation formatting templates."""
        
        return {
            CitationStyle.INLINE_NUMBERED.value: {
                "inline": "[{number}]",
                "reference": "{number}. {citation_text}",
                "separator": ", "
            },
            
            CitationStyle.INLINE_AUTHOR.value: {
                "inline": "({author}, {year})",
                "reference": "{author} ({year}). {title}. {source}",
                "separator": "; "
            },
            
            CitationStyle.FOOTNOTE.value: {
                "inline": "^{number}",
                "footnote": "{number}. {citation_text}",
                "separator": " "
            },
            
            CitationStyle.REFERENCE_LIST.value: {
                "inline": "",
                "reference": "• {citation_text}",
                "separator": "\n"
            },
            
            CitationStyle.HYPERLINK.value: {
                "inline": "[{text}]({url})",
                "reference": "{title} - {url}",
                "separator": " | "
            },
            
            CitationStyle.MINIMAL.value: {
                "inline": "¹",
                "reference": "¹ {title}",
                "separator": " "
            }
        }

    def _initialize_placement_rules(self) -> Dict[str, Any]:
        """Initialize citation placement rules."""
        
        return {
            "end_of_sentence": True,
            "after_quotes": True,
            "before_punctuation": True,
            "avoid_mid_sentence": True,
            "group_consecutive": True,
            "max_per_sentence": 2
        }

    async def attribute_sources(self,
                              response_text: str,
                              source_documents: List[SourceDocument],
                              context: Optional[Dict[str, Any]] = None) -> AttributionResult:
        """Attribute sources to response text with citations."""
        
        try:
            # Register sources
            for source in source_documents:
                self.source_registry[source.id] = source
            
            # Find attribution opportunities
            attribution_matches = await self._find_attribution_matches(
                response_text, source_documents
            )
            
            # Generate citations
            citations = await self._generate_citations(
                attribution_matches, response_text
            )
            
            # Apply citations to text
            attributed_text = await self._apply_citations_to_text(
                response_text, citations
            )
            
            # Calculate attribution metrics
            attribution_confidence = self._calculate_attribution_confidence(citations)
            coverage_percentage = self._calculate_coverage_percentage(
                response_text, citations
            )
            
            return AttributionResult(
                attributed_text=attributed_text,
                citations=citations,
                sources=source_documents,
                attribution_confidence=attribution_confidence,
                coverage_percentage=coverage_percentage
            )
            
        except Exception as e:
            self.logger.error(f"Source attribution failed: {e}")
            return AttributionResult(
                attributed_text=response_text,
                citations=[],
                sources=source_documents,
                attribution_confidence=0.0,
                coverage_percentage=0.0
            )

    async def _find_attribution_matches(self,
                                      response_text: str,
                                      sources: List[SourceDocument]) -> List[Dict[str, Any]]:
        """Find text segments that can be attributed to sources."""
        
        matches = []
        
        # Split response into sentences for analysis
        sentences = self._split_into_sentences(response_text)
        
        for i, sentence in enumerate(sentences):
            sentence_matches = await self._match_sentence_to_sources(
                sentence, sources, i
            )
            matches.extend(sentence_matches)
        
        # Filter and rank matches
        filtered_matches = self._filter_attribution_matches(matches)
        
        return filtered_matches

    def _split_into_sentences(self, text: str) -> List[Dict[str, Any]]:
        """Split text into sentences with position tracking."""
        
        # Simple sentence splitting (can be enhanced with NLP)
        sentence_pattern = r'[.!?]+\s+'
        sentences = []
        
        current_pos = 0
        for match in re.finditer(sentence_pattern, text):
            sentence_text = text[current_pos:match.end()].strip()
            if sentence_text:
                sentences.append({
                    "text": sentence_text,
                    "start": current_pos,
                    "end": match.end(),
                    "index": len(sentences)
                })
            current_pos = match.end()
        
        # Add final sentence if no ending punctuation
        if current_pos < len(text):
            final_text = text[current_pos:].strip()
            if final_text:
                sentences.append({
                    "text": final_text,
                    "start": current_pos,
                    "end": len(text),
                    "index": len(sentences)
                })
        
        return sentences

    async def _match_sentence_to_sources(self,
                                       sentence: Dict[str, Any],
                                       sources: List[SourceDocument],
                                       sentence_index: int) -> List[Dict[str, Any]]:
        """Match a sentence to potential source documents."""
        
        matches = []
        sentence_text = sentence["text"].lower()
        
        for source in sources:
            # Calculate similarity between sentence and source
            similarity_score = await self._calculate_text_similarity(
                sentence_text, source.content.lower()
            )
            
            if similarity_score >= self.min_attribution_confidence:
                # Find specific matching phrases
                matching_phrases = self._find_matching_phrases(
                    sentence_text, source.content.lower()
                )
                
                for phrase in matching_phrases:
                    matches.append({
                        "sentence": sentence,
                        "source": source,
                        "similarity_score": similarity_score,
                        "matching_phrase": phrase,
                        "confidence": similarity_score * phrase["confidence"]
                    })
        
        return matches

    async def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two text segments."""
        
        # Simple word overlap similarity (can be enhanced with embeddings)
        words1 = set(re.findall(r'\b\w+\b', text1.lower()))
        words2 = set(re.findall(r'\b\w+\b', text2.lower()))
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0

    def _find_matching_phrases(self, sentence: str, source_content: str) -> List[Dict[str, Any]]:
        """Find specific phrases that match between sentence and source."""
        
        phrases = []
        
        # Look for exact phrase matches (3+ words)
        sentence_words = re.findall(r'\b\w+\b', sentence)
        source_words = re.findall(r'\b\w+\b', source_content)
        
        for i in range(len(sentence_words) - 2):
            for phrase_length in range(3, min(8, len(sentence_words) - i + 1)):
                phrase = " ".join(sentence_words[i:i + phrase_length])
                
                if phrase in source_content:
                    phrases.append({
                        "phrase": phrase,
                        "start_pos": i,
                        "length": phrase_length,
                        "confidence": min(phrase_length / 5.0, 1.0)  # Longer phrases = higher confidence
                    })
        
        # Remove overlapping phrases, keep longest
        phrases = self._remove_overlapping_phrases(phrases)
        
        return phrases

    def _remove_overlapping_phrases(self, phrases: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove overlapping phrases, keeping the longest ones."""
        
        if not phrases:
            return phrases
        
        # Sort by length (descending) then by confidence
        phrases.sort(key=lambda x: (-x["length"], -x["confidence"]))
        
        non_overlapping = []
        used_positions = set()
        
        for phrase in phrases:
            phrase_positions = set(range(phrase["start_pos"], 
                                       phrase["start_pos"] + phrase["length"]))
            
            if not phrase_positions.intersection(used_positions):
                non_overlapping.append(phrase)
                used_positions.update(phrase_positions)
        
        return non_overlapping

    def _filter_attribution_matches(self, matches: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter and rank attribution matches."""
        
        # Filter by confidence threshold
        filtered = [m for m in matches if m["confidence"] >= self.min_attribution_confidence]
        
        # Sort by confidence (descending)
        filtered.sort(key=lambda x: x["confidence"], reverse=True)
        
        # Group by sentence and limit citations per sentence
        sentence_groups = {}
        for match in filtered:
            sentence_idx = match["sentence"]["index"]
            if sentence_idx not in sentence_groups:
                sentence_groups[sentence_idx] = []
            sentence_groups[sentence_idx].append(match)
        
        # Limit citations per sentence
        final_matches = []
        for sentence_idx, sentence_matches in sentence_groups.items():
            # Take top matches up to limit
            limited_matches = sentence_matches[:self.citation_placement_rules["max_per_sentence"]]
            final_matches.extend(limited_matches)
        
        return final_matches

    async def _generate_citations(self,
                                attribution_matches: List[Dict[str, Any]],
                                response_text: str) -> List[Citation]:
        """Generate citation objects from attribution matches."""
        
        citations = []
        citation_numbers = {}  # Track citation numbers for sources
        
        for match in attribution_matches:
            source = match["source"]
            sentence = match["sentence"]
            
            # Assign citation number if not already assigned
            if source.id not in citation_numbers:
                self.citation_counter += 1
                citation_numbers[source.id] = self.citation_counter
            
            # Determine citation placement within sentence
            citation_position = self._determine_citation_position(
                sentence, match["matching_phrase"]
            )
            
            citation = Citation(
                source_id=source.id,
                citation_key=source.citation_key,
                start_position=citation_position,
                end_position=citation_position,
                cited_text=match["matching_phrase"]["phrase"],
                citation_type=self.citation_style.value,
                confidence=match["confidence"]
            )
            
            citations.append(citation)
        
        return citations

    def _determine_citation_position(self,
                                   sentence: Dict[str, Any],
                                   matching_phrase: Dict[str, Any]) -> int:
        """Determine optimal position for citation within sentence."""
        
        sentence_text = sentence["text"]
        sentence_start = sentence["start"]
        
        # Default to end of sentence
        citation_pos = sentence["end"] - 1
        
        # Apply placement rules
        if self.citation_placement_rules["end_of_sentence"]:
            # Find last punctuation
            last_punct = max(
                sentence_text.rfind('.'),
                sentence_text.rfind('!'),
                sentence_text.rfind('?')
            )
            if last_punct > 0:
                citation_pos = sentence_start + last_punct
        
        return citation_pos

    async def _apply_citations_to_text(self,
                                     response_text: str,
                                     citations: List[Citation]) -> str:
        """Apply citations to response text."""
        
        if not citations:
            return response_text
        
        # Sort citations by position (reverse order for insertion)
        citations.sort(key=lambda x: x.start_position, reverse=True)
        
        attributed_text = response_text
        citation_numbers = {}
        
        # Insert citations
        for citation in citations:
            source = self.source_registry[citation.source_id]
            
            # Get citation number
            if citation.source_id not in citation_numbers:
                citation_numbers[citation.source_id] = len(citation_numbers) + 1
            
            citation_number = citation_numbers[citation.source_id]
            
            # Format citation based on style
            citation_mark = self._format_citation_mark(citation_number, source)
            
            # Insert citation
            attributed_text = (
                attributed_text[:citation.start_position] +
                citation_mark +
                attributed_text[citation.start_position:]
            )
        
        # Add reference list if needed
        if self.citation_style in [CitationStyle.REFERENCE_LIST, CitationStyle.FOOTNOTE]:
            reference_section = self._generate_reference_section(citations)
            attributed_text += "\n\n" + reference_section
        
        return attributed_text

    def _format_citation_mark(self, citation_number: int, source: SourceDocument) -> str:
        """Format citation mark based on citation style."""
        
        template = self.citation_templates[self.citation_style.value]["inline"]
        
        if self.citation_style == CitationStyle.INLINE_NUMBERED:
            return template.format(number=citation_number)
        
        elif self.citation_style == CitationStyle.INLINE_AUTHOR:
            author = source.author or "Unknown"
            year = source.publication_date.year if source.publication_date else "n.d."
            return template.format(author=author, year=year)
        
        elif self.citation_style == CitationStyle.FOOTNOTE:
            return template.format(number=citation_number)
        
        elif self.citation_style == CitationStyle.HYPERLINK:
            return template.format(text=source.title, url=source.url or "#")
        
        elif self.citation_style == CitationStyle.MINIMAL:
            return template
        
        return f"[{citation_number}]"

    def _generate_reference_section(self, citations: List[Citation]) -> str:
        """Generate reference section for citations."""
        
        if not citations:
            return ""
        
        # Get unique sources
        unique_sources = {}
        citation_numbers = {}
        
        for citation in citations:
            if citation.source_id not in unique_sources:
                unique_sources[citation.source_id] = self.source_registry[citation.source_id]
                citation_numbers[citation.source_id] = len(citation_numbers) + 1
        
        # Generate reference list
        references = []
        template = self.citation_templates[self.citation_style.value]["reference"]
        
        for source_id, source in unique_sources.items():
            citation_number = citation_numbers[source_id]
            
            if self.citation_style == CitationStyle.INLINE_NUMBERED:
                ref_text = template.format(
                    number=citation_number,
                    citation_text=source.citation_text
                )
            elif self.citation_style == CitationStyle.FOOTNOTE:
                ref_text = template.format(
                    number=citation_number,
                    citation_text=source.citation_text
                )
            else:
                ref_text = template.format(citation_text=source.citation_text)
            
            references.append(ref_text)
        
        # Format section
        section_title = "References:" if self.citation_style == CitationStyle.REFERENCE_LIST else "Footnotes:"
        separator = self.citation_templates[self.citation_style.value]["separator"]
        
        return section_title + "\n" + separator.join(references)

    def _calculate_attribution_confidence(self, citations: List[Citation]) -> float:
        """Calculate overall attribution confidence."""
        
        if not citations:
            return 0.0
        
        total_confidence = sum(citation.confidence for citation in citations)
        return min(total_confidence / len(citations), 1.0)

    def _calculate_coverage_percentage(self, response_text: str, citations: List[Citation]) -> float:
        """Calculate percentage of response covered by citations."""
        
        if not citations:
            return 0.0
        
        # Simple approximation: count cited sentences vs total sentences
        total_sentences = len(self._split_into_sentences(response_text))
        cited_sentences = len(set(citation.start_position for citation in citations))
        
        return min((cited_sentences / total_sentences) * 100, 100.0) if total_sentences > 0 else 0.0

    async def validate_citations(self, attribution_result: AttributionResult) -> Dict[str, Any]:
        """Validate citation accuracy and completeness."""
        
        validation_results = {
            "total_citations": len(attribution_result.citations),
            "valid_citations": 0,
            "invalid_citations": 0,
            "missing_sources": [],
            "duplicate_citations": 0,
            "citation_density": 0.0,
            "issues": []
        }
        
        # Check citation validity
        source_ids = {source.id for source in attribution_result.sources}
        
        for citation in attribution_result.citations:
            if citation.source_id in source_ids:
                validation_results["valid_citations"] += 1
            else:
                validation_results["invalid_citations"] += 1
                validation_results["issues"].append(
                    f"Citation references unknown source: {citation.source_id}"
                )
        
        # Check for missing sources (sources not cited)
        cited_source_ids = {citation.source_id for citation in attribution_result.citations}
        for source in attribution_result.sources:
            if source.id not in cited_source_ids:
                validation_results["missing_sources"].append(source.id)
        
        # Calculate citation density
        text_length = len(attribution_result.attributed_text.split())
        citation_count = len(attribution_result.citations)
        validation_results["citation_density"] = citation_count / text_length if text_length > 0 else 0.0
        
        # Check for duplicate citations
        citation_positions = [c.start_position for c in attribution_result.citations]
        validation_results["duplicate_citations"] = len(citation_positions) - len(set(citation_positions))
        
        return validation_results

    async def optimize_citations(self, attribution_result: AttributionResult) -> AttributionResult:
        """Optimize citation placement and formatting."""
        
        # Remove duplicate citations at same position
        unique_citations = []
        seen_positions = set()
        
        for citation in attribution_result.citations:
            if citation.start_position not in seen_positions:
                unique_citations.append(citation)
                seen_positions.add(citation.start_position)
        
        # Re-apply optimized citations
        optimized_text = await self._apply_citations_to_text(
            attribution_result.attributed_text, unique_citations
        )
        
        return AttributionResult(
            attributed_text=optimized_text,
            citations=unique_citations,
            sources=attribution_result.sources,
            attribution_confidence=attribution_result.attribution_confidence,
            coverage_percentage=attribution_result.coverage_percentage
        )

    def get_citation_statistics(self, attribution_result: AttributionResult) -> Dict[str, Any]:
        """Get detailed statistics about citations."""
        
        stats = {
            "total_sources": len(attribution_result.sources),
            "total_citations": len(attribution_result.citations),
            "attribution_confidence": attribution_result.attribution_confidence,
            "coverage_percentage": attribution_result.coverage_percentage,
            "citations_per_source": {},
            "citation_types": {},
            "confidence_distribution": {
                "high": 0,  # > 0.8
                "medium": 0,  # 0.5 - 0.8
                "low": 0   # < 0.5
            }
        }
        
        # Citations per source
        for citation in attribution_result.citations:
            source_id = citation.source_id
            stats["citations_per_source"][source_id] = stats["citations_per_source"].get(source_id, 0) + 1
        
        # Citation types
        for citation in attribution_result.citations:
            citation_type = citation.citation_type
            stats["citation_types"][citation_type] = stats["citation_types"].get(citation_type, 0) + 1
        
        # Confidence distribution
        for citation in attribution_result.citations:
            if citation.confidence > 0.8:
                stats["confidence_distribution"]["high"] += 1
            elif citation.confidence > 0.5:
                stats["confidence_distribution"]["medium"] += 1
            else:
                stats["confidence_distribution"]["low"] += 1
        
        return stats

    def set_citation_style(self, style: CitationStyle):
        """Change citation style."""
        self.citation_style = style

    def reset_citation_counter(self):
        """Reset citation counter for new document."""
        self.citation_counter = 0
        self.source_registry.clear()
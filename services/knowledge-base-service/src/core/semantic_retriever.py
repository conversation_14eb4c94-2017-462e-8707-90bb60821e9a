from typing import List, Dict, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Union
import asyncio
import numpy as np
from dataclasses import dataclass

try:
    from sentence_transformers import SentenceTransformer, CrossEncoder
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: sentence_transformers not available: {e}")
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    SentenceTransformer = None
    CrossEncoder = None

from langchain_openai import OpenAIEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
import json
import faiss
from datetime import datetime, timedelta
import logging
from .reranker import CrossEncoderReranker, RerankingResult

@dataclass
class SemanticDocument:
    """Document with semantic metadata and embeddings."""
    id: str
    content: str
    title: str
    metadata: Dict[str, Any]
    embedding: Optional[np.ndarray] = None
    dense_embedding: Optional[np.ndarray] = None
    sparse_features: Optional[Dict[str, float]] = None
    created_at: datetime = None
    updated_at: datetime = None

@dataclass
class RetrievalResult:
    """Enhanced retrieval result with multiple scoring mechanisms."""
    document: SemanticDocument
    semantic_score: float
    keyword_score: float
    hybrid_score: float
    rerank_score: Optional[float] = None
    relevance_explanation: str = ""
    retrieval_method: str = ""
    confidence: float = 0.0

class SemanticRetriever:
    """State-of-the-art semantic retrieval system with multiple embedding models and reranking."""
    
    def __init__(self, 
                 primary_model: str = "all-mpnet-base-v2",  # Upgraded from all-MiniLM-L6-v2
                 cross_encoder_model: str = "cross-encoder/ms-marco-MiniLM-L-12-v2",  # Upgraded model
                 use_openai_embeddings: bool = True):  # Enable by default for better performance
        
        # Multi-tier embedding strategy
        self.embedding_models = {
            "primary": "text-embedding-3-large",
            "fallback": "all-mpnet-base-v2",
            "technical": "sentence-transformers/all-MiniLM-L6-v2"  # Keep for speed
        }
        
        # Initialize embedding models
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            self.sentence_transformer = SentenceTransformer(primary_model)
            self.cross_encoder = CrossEncoder(cross_encoder_model)
        else:
            self.sentence_transformer = None
            self.cross_encoder = None
            self.logger.warning("sentence_transformers not available, using OpenAI embeddings only")
        
        if use_openai_embeddings:
            self.openai_embeddings = OpenAIEmbeddings(model="text-embedding-3-large")
        else:
            self.openai_embeddings = None
        
        # FAISS indices for fast similarity search
        self.faiss_index = None
        self.openai_faiss_index = None
        
        # Document storage
        self.documents: List[SemanticDocument] = []
        self.id_to_idx: Dict[str, int] = {}
        
        # Advanced chunking system
        from .advanced_chunking import AdvancedDocumentChunker
        self.chunker = AdvancedDocumentChunker(
            model_name=primary_model,
            max_chunk_size=512,
            overlap_size=100,
            similarity_threshold=0.8
        )
        
        # Fallback text splitter for chunking
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=512,
            chunk_overlap=100,
            length_function=len
        )
        
        # Query enhancement
        self.query_expansion_cache = {}
        
        # Initialize reranker
        self.reranker = CrossEncoderReranker(config)
        self.enable_reranking = getattr(config, 'enable_cross_encoder_reranking', True)
        self.rerank_top_k = getattr(config, 'rerank_top_k', 50)
        self.final_top_k = getattr(config, 'final_retrieval_top_k', 10)

        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    async def add_documents(self, documents: List[Dict[str, Any]]) -> None:
        """Add documents with semantic processing."""
        semantic_docs = []
        
        for doc in documents:
            # Create semantic document
            sem_doc = SemanticDocument(
                id=doc.get("id", str(len(self.documents))),
                content=doc["content"],
                title=doc.get("title", ""),
                metadata=doc.get("metadata", {}),
                created_at=datetime.now()
            )
            
            # Generate embeddings
            await self._generate_embeddings(sem_doc)
            semantic_docs.append(sem_doc)
        
        # Add to storage
        start_idx = len(self.documents)
        self.documents.extend(semantic_docs)
        
        # Update indices
        for i, doc in enumerate(semantic_docs):
            self.id_to_idx[doc.id] = start_idx + i
        
        # Rebuild FAISS indices
        await self._rebuild_indices()
        
        self.logger.info(f"Added {len(semantic_docs)} documents to semantic retriever")
    
    async def _generate_embeddings(self, document: SemanticDocument) -> None:
        """Generate multiple types of embeddings for a document."""
        try:
            # Sentence transformer embedding
            if self.sentence_transformer:
                document.embedding = self.sentence_transformer.encode(
                    document.content,
                    convert_to_numpy=True,
                    normalize_embeddings=True
                )
            else:
                # Use OpenAI embeddings as primary if sentence_transformers not available
                if self.openai_embeddings:
                    embedding = await self.openai_embeddings.aembed_query(document.content)
                    document.embedding = np.array(embedding)
                else:
                    document.embedding = None
            
            # OpenAI embedding if available
            if self.openai_embeddings:
                document.dense_embedding = await self.openai_embeddings.aembed_query(
                    document.content
                )
                document.dense_embedding = np.array(document.dense_embedding)
            
        except Exception as e:
            self.logger.error(f"Error generating embeddings for document {document.id}: {e}")
            # Fallback to zero embedding
            if self.sentence_transformer:
                document.embedding = np.zeros(self.sentence_transformer.get_sentence_embedding_dimension())
            elif self.openai_embeddings:
                document.embedding = np.zeros(3072)  # text-embedding-3-large dimension
            if self.openai_embeddings:
                document.dense_embedding = np.zeros(3072)  # text-embedding-3-large dimension
    
    async def _rebuild_indices(self) -> None:
        """Rebuild FAISS indices for fast similarity search."""
        if not self.documents:
            return
        
        # Build sentence transformer index
        if self.sentence_transformer and all(doc.embedding is not None for doc in self.documents):
            embeddings = np.array([doc.embedding for doc in self.documents])
            dimension = embeddings.shape[1]
            
            self.faiss_index = faiss.IndexFlatIP(dimension)  # Inner product for normalized embeddings
            self.faiss_index.add(embeddings.astype(np.float32))
        else:
            self.faiss_index = None
        
        # Build OpenAI embeddings index if available
        if self.openai_embeddings and all(doc.dense_embedding is not None for doc in self.documents):
            dense_embeddings = np.array([doc.dense_embedding for doc in self.documents])
            dense_dimension = dense_embeddings.shape[1]
            
            self.openai_faiss_index = faiss.IndexFlatIP(dense_dimension)
            self.openai_faiss_index.add(dense_embeddings.astype(np.float32))
        
        self.logger.info(f"Rebuilt FAISS indices for {len(self.documents)} documents")
    
    async def semantic_search(self, 
                            query: str, 
                            top_k: int = 10,
                            use_query_expansion: bool = True,
                            filters: Dict[str, Any] = None) -> List[RetrievalResult]:
        """Perform semantic search with query expansion and reranking."""
        
        if not self.documents or self.faiss_index is None:
            return []
        
        # Search with semantic similarity
        semantic_results = await self._semantic_search_single(query, top_k * 2)
        
        # Keyword search results
        keyword_results = await self._keyword_search(query, top_k * 2)
        
        # Combine and score
        combined_results = await self._combine_results(
            semantic_results, keyword_results, query
        )
        
        # Apply filters if provided
        if filters:
            combined_results = self._apply_filters(combined_results, filters)
        
        # Rerank with cross-encoder
        reranked_results = await self._rerank_results(query, combined_results)
        
        # Final scoring and sorting
        final_results = self._final_scoring(reranked_results, query)
        
        return final_results[:top_k]
    
    async def _semantic_search_single(self, query: str, top_k: int) -> List[Tuple[int, float]]:
        """Perform semantic search for a single query."""
        try:
            if not self.faiss_index:
                return []
            
            # Generate query embedding
            if self.sentence_transformer:
                query_embedding = self.sentence_transformer.encode(
                    query, convert_to_numpy=True, normalize_embeddings=True
                )
            elif self.openai_embeddings:
                query_embedding = await self.openai_embeddings.aembed_query(query)
                query_embedding = np.array(query_embedding)
            else:
                return []
            
            # Search with FAISS
            scores, indices = self.faiss_index.search(
                query_embedding.reshape(1, -1).astype(np.float32), top_k
            )
            
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx != -1 and idx < len(self.documents):  # Valid index
                    results.append((idx, float(score)))
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in semantic search: {e}")
            return []
    
    async def _keyword_search(self, query: str, top_k: int) -> List[Tuple[int, float]]:
        """Perform keyword-based search using BM25-like scoring."""
        query_terms = query.lower().split()
        results = []
        
        for idx, doc in enumerate(self.documents):
            score = self._calculate_bm25_score(query_terms, doc)
            if score > 0:
                results.append((idx, score))
        
        # Sort by score and return top results
        results.sort(key=lambda x: x[1], reverse=True)
        return results[:top_k]
    
    def _calculate_bm25_score(self, query_terms: List[str], document: SemanticDocument, 
                             k1: float = 1.2, b: float = 0.75) -> float:
        """Calculate BM25 score for keyword matching."""
        doc_terms = document.content.lower().split()
        doc_length = len(doc_terms)
        
        if doc_length == 0:
            return 0.0
        
        # Calculate average document length
        avg_dl = np.mean([len(d.content.split()) for d in self.documents])
        
        score = 0.0
        for term in query_terms:
            tf = doc_terms.count(term)
            if tf > 0:
                # Simple IDF approximation
                df = sum(1 for d in self.documents if term in d.content.lower())
                idf = np.log((len(self.documents) - df + 0.5) / (df + 0.5))
                
                # BM25 formula
                score += idf * (tf * (k1 + 1)) / (tf + k1 * (1 - b + b * (doc_length / avg_dl)))
        
        return score
    
    async def _combine_results(self, 
                             semantic_results: List[Tuple[int, float]], 
                             keyword_results: List[Tuple[int, float]],
                             query: str) -> List[RetrievalResult]:
        """Combine semantic and keyword search results with hybrid scoring."""
        
        # Create lookup dictionaries
        semantic_scores = {idx: score for idx, score in semantic_results}
        keyword_scores = {idx: score for idx, score in keyword_results}
        
        # Get all unique document indices
        all_indices = set(semantic_scores.keys()) | set(keyword_scores.keys())
        
        combined_results = []
        
        for idx in all_indices:
            if idx >= len(self.documents):
                continue
                
            document = self.documents[idx]
            
            sem_score = semantic_scores.get(idx, 0.0)
            kw_score = keyword_scores.get(idx, 0.0)
            
            # Normalize scores
            sem_score_norm = min(sem_score, 1.0) if sem_score > 0 else 0.0
            kw_score_norm = min(kw_score / 10.0, 1.0) if kw_score > 0 else 0.0
            
            # Hybrid scoring with weights
            hybrid_score = 0.7 * sem_score_norm + 0.3 * kw_score_norm
            
            # Create retrieval result
            result = RetrievalResult(
                document=document,
                semantic_score=sem_score_norm,
                keyword_score=kw_score_norm,
                hybrid_score=hybrid_score,
                retrieval_method="hybrid",
                relevance_explanation=f"Semantic: {sem_score_norm:.3f}, Keyword: {kw_score_norm:.3f}"
            )
            
            combined_results.append(result)
        
        return combined_results
    
    async def _rerank_results(self, query: str, results: List[RetrievalResult]) -> List[RetrievalResult]:
        """Rerank results using cross-encoder for better relevance."""
        if len(results) <= 1:
            return results
        
        try:
            # Prepare query-document pairs for cross-encoder
            pairs = [(query, result.document.content) for result in results]
            
            # Get rerank scores
            rerank_scores = self.cross_encoder.predict(pairs)
            
            # Update results with rerank scores
            for result, rerank_score in zip(results, rerank_scores):
                result.rerank_score = float(rerank_score)
                result.confidence = (result.hybrid_score + result.rerank_score) / 2
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in reranking: {e}")
            # Return original results if reranking fails
            for result in results:
                result.rerank_score = result.hybrid_score
                result.confidence = result.hybrid_score
            return results
    
    def _apply_filters(self, results: List[RetrievalResult], filters: Dict[str, Any]) -> List[RetrievalResult]:
        """Apply metadata filters to results."""
        filtered_results = []
        
        for result in results:
            include = True
            metadata = result.document.metadata
            
            for key, value in filters.items():
                if key == "date_range":
                    # Handle date range filtering
                    if "created_at" in metadata:
                        doc_date = metadata["created_at"]
                        if not (value["start"] <= doc_date <= value["end"]):
                            include = False
                            break
                elif key == "boost_recent":
                    # Boost recent documents
                    if result.document.created_at:
                        days_old = (datetime.now() - result.document.created_at).days
                        if days_old <= 7:  # Within a week
                            result.hybrid_score *= 1.2
                elif key in metadata:
                    if isinstance(value, list):
                        if metadata[key] not in value:
                            include = False
                            break
                    else:
                        if metadata[key] != value:
                            include = False
                            break
            
            if include:
                filtered_results.append(result)
        
        return filtered_results
    
    def _final_scoring(self, results: List[RetrievalResult], query: str) -> List[RetrievalResult]:
        """Apply final scoring and sort results."""
        for result in results:
            # Combine all scores with weights
            final_score = (
                0.4 * result.hybrid_score +
                0.4 * (result.rerank_score or 0.0) +
                0.2 * result.confidence
            )
            
            # Apply query-specific boosting
            if query.lower() in result.document.title.lower():
                final_score *= 1.3  # Title match boost
            
            # Apply recency boost for time-sensitive queries
            if any(word in query.lower() for word in ["recent", "latest", "new", "current"]):
                if result.document.created_at:
                    days_old = (datetime.now() - result.document.created_at).days
                    recency_boost = max(0.8, 1.0 - (days_old / 365))  # Decay over year
                    final_score *= recency_boost
            
            result.confidence = final_score
        
        # Sort by final score
        results.sort(key=lambda x: x.confidence, reverse=True)
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get retriever statistics."""
        return {
            "total_documents": len(self.documents),
            "faiss_index_size": self.faiss_index.ntotal if self.faiss_index else 0,
            "embedding_dimension": self.sentence_transformer.get_sentence_embedding_dimension(),
            "models_used": {
                "sentence_transformer": "all-MiniLM-L6-v2",
                "cross_encoder": "cross-encoder/ms-marco-MiniLM-L-6-v2",
                "openai_embeddings": "text-embedding-3-large" if self.openai_embeddings else None
            },
            "query_expansion_cache_size": len(self.query_expansion_cache)
        }

    async def retrieve_documents(
        self,
        query: str,
        max_results: int = 10,
        context: Optional[Dict] = None,
        enable_reranking: Optional[bool] = None
    ) -> Dict[str, Any]:
        """Enhanced retrieval with cross-encoder reranking."""
        
        start_time = datetime.now()
        
        try:
            # Step 1: Initial retrieval (get more documents for reranking)
            initial_max = max(max_results * 3, self.rerank_top_k) if self.enable_reranking else max_results
            
            # ... existing retrieval logic to get initial_results ...
            
            # Step 2: Cross-encoder reranking (if enabled)
            use_reranking = (
                enable_reranking if enable_reranking is not None 
                else self.enable_reranking
            )
            
            if use_reranking and len(initial_results.get("documents", [])) > 1:
                self.logger.info(f"Applying cross-encoder reranking to {len(initial_results['documents'])} documents")
                
                reranking_result = await self.reranker.rerank_documents(
                    query=query,
                    documents=initial_results["documents"],
                    top_k=max_results,
                    enable_diversity=True
                )
                
                # Use reranked results
                final_documents = reranking_result.reranked_documents
                
                # Update metadata with reranking info
                retrieval_metadata = {
                    "initial_results": len(initial_results["documents"]),
                    "reranked_results": len(final_documents),
                    "reranking_time": reranking_result.reranking_time,
                    "reranking_model": reranking_result.model_used,
                    "diversity_score": reranking_result.diversity_score,
                    "average_rerank_score": np.mean(reranking_result.relevance_scores) if reranking_result.relevance_scores else 0.0
                }
                
            else:
                # Use original results
                final_documents = initial_results["documents"][:max_results]
                retrieval_metadata = {
                    "reranking_used": False,
                    "reason": "disabled or insufficient documents"
                }
            
            # Step 3: Build final context
            context_pieces = []
            for i, doc in enumerate(final_documents):
                context_piece = f"[Source {i+1}]: {doc.get('content', '')}"
                if 'rerank_score' in doc:
                    context_piece += f" (Relevance: {doc['rerank_score']:.3f})"
                context_pieces.append(context_piece)
            
            final_context = "\n\n".join(context_pieces)
            
            # Calculate overall confidence
            if use_reranking and final_documents:
                # Use reranking scores for confidence
                rerank_scores = [doc.get('rerank_score', 0.5) for doc in final_documents]
                overall_confidence = np.mean(rerank_scores)
            else:
                # Use original similarity scores
                similarity_scores = [doc.get('similarity_score', 0.5) for doc in final_documents]
                overall_confidence = np.mean(similarity_scores)
            
            total_time = (datetime.now() - start_time).total_seconds()
            
            return {
                "documents": final_documents,
                "context": final_context,
                "confidence": float(overall_confidence),
                "retrieval_time": total_time,
                "metadata": retrieval_metadata,
                "query_analysis": {
                    "processed_query": query,
                    "num_results": len(final_documents)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Enhanced retrieval failed: {e}")
            # Fallback to basic retrieval
            return await self._basic_retrieval_fallback(query, max_results, context)

    async def _basic_retrieval_fallback(
        self, 
        query: str, 
        max_results: int, 
        context: Optional[Dict]
    ) -> Dict[str, Any]:
        """Fallback to basic retrieval when enhanced retrieval fails."""
        
        # ... implement basic retrieval without reranking ...
        pass

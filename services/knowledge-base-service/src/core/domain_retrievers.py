"""
Domain Retrievers
Specialized retrievers optimized for different domains and content types
"""

from typing import Dict, List, Any, Optional, Set, Tuple
import logging
import asyncio
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import re
import json
from abc import ABC, abstractmethod
from collections import defaultdict

class RetrievalDomain(Enum):
    """Domains for specialized retrieval."""
    TECHNICAL = "technical"
    BILLING = "billing"
    PRODUCT = "product"
    FAQ = "faq"
    TROUBLESHOOTING = "troubleshooting"
    INTEGRATION = "integration"
    SECURITY = "security"
    ACCOUNT = "account"

@dataclass
class RetrievalResult:
    """Result from domain-specific retrieval."""
    
    document_id: str
    content: str
    title: str
    score: float
    
    # Domain-specific metadata
    domain: RetrievalDomain
    content_type: str = "general"
    section: str = ""
    
    # Retrieval metadata
    retrieval_method: str = ""
    processing_time: float = 0.0
    
    # Additional context
    metadata: Dict[str, Any] = field(default_factory=dict)
    highlights: List[str] = field(default_factory=list)
    related_documents: List[str] = field(default_factory=list)

@dataclass
class DomainRetrievalRequest:
    """Request for domain-specific retrieval."""
    
    query: str
    domain: RetrievalDomain
    max_results: int = 10
    min_score: float = 0.5
    
    # Query context
    user_context: Dict[str, Any] = field(default_factory=dict)
    conversation_history: List[str] = field(default_factory=list)
    
    # Retrieval preferences
    prefer_recent: bool = False
    include_related: bool = True
    highlight_matches: bool = True

class BaseDomainRetriever(ABC):
    """Base class for domain-specific retrievers."""
    
    def __init__(self, domain: RetrievalDomain, config: Dict[str, Any] = None):
        self.domain = domain
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.{domain.value}")
        
        # Performance metrics
        self.metrics = {
            "total_queries": 0,
            "avg_response_time": 0.0,
            "avg_results_count": 0.0,
            "cache_hit_rate": 0.0
        }
        
        # Domain-specific settings
        self.settings = self._initialize_settings()
        
        # Result cache
        self.cache: Dict[str, List[RetrievalResult]] = {}
        self.cache_ttl = 3600  # 1 hour
    
    @abstractmethod
    def _initialize_settings(self) -> Dict[str, Any]:
        """Initialize domain-specific settings."""
        pass
    
    @abstractmethod
    async def _domain_specific_search(self, request: DomainRetrievalRequest) -> List[RetrievalResult]:
        """Perform domain-specific search logic."""
        pass
    
    async def retrieve(self, request: DomainRetrievalRequest) -> List[RetrievalResult]:
        """Main retrieval method."""
        
        start_time = datetime.now()
        
        try:
            # Check cache first
            cache_key = self._generate_cache_key(request)
            if cache_key in self.cache:
                self.logger.debug(f"Cache hit for query: {request.query[:50]}")
                return self.cache[cache_key]
            
            # Perform domain-specific search
            results = await self._domain_specific_search(request)
            
            # Post-process results
            results = await self._post_process_results(results, request)
            
            # Cache results
            self.cache[cache_key] = results
            
            # Update metrics
            processing_time = (datetime.now() - start_time).total_seconds()
            self._update_metrics(processing_time, len(results))
            
            return results
            
        except Exception as e:
            self.logger.error(f"Retrieval failed for domain {self.domain.value}: {e}")
            return []
    
    async def _post_process_results(self, 
                                  results: List[RetrievalResult], 
                                  request: DomainRetrievalRequest) -> List[RetrievalResult]:
        """Post-process retrieval results."""
        
        # Filter by minimum score
        filtered_results = [r for r in results if r.score >= request.min_score]
        
        # Sort by score
        filtered_results.sort(key=lambda x: x.score, reverse=True)
        
        # Limit results
        filtered_results = filtered_results[:request.max_results]
        
        # Add highlights if requested
        if request.highlight_matches:
            for result in filtered_results:
                result.highlights = await self._generate_highlights(result, request.query)
        
        # Add related documents if requested
        if request.include_related:
            for result in filtered_results:
                result.related_documents = await self._find_related_documents(result)
        
        return filtered_results
    
    async def _generate_highlights(self, result: RetrievalResult, query: str) -> List[str]:
        """Generate highlights for search matches."""
        
        highlights = []
        query_terms = query.lower().split()
        content_lower = result.content.lower()
        
        for term in query_terms:
            if len(term) > 2 and term in content_lower:
                # Find context around the term
                start_idx = content_lower.find(term)
                if start_idx != -1:
                    context_start = max(0, start_idx - 50)
                    context_end = min(len(result.content), start_idx + len(term) + 50)
                    context = result.content[context_start:context_end]
                    highlights.append(f"...{context}...")
        
        return highlights[:3]  # Limit to 3 highlights
    
    async def _find_related_documents(self, result: RetrievalResult) -> List[str]:
        """Find related documents (placeholder implementation)."""
        
        # This would typically use similarity search or metadata matching
        return []
    
    def _generate_cache_key(self, request: DomainRetrievalRequest) -> str:
        """Generate cache key for request."""
        
        key_parts = [
            request.query,
            str(request.max_results),
            str(request.min_score),
            str(request.prefer_recent),
            str(request.include_related)
        ]
        return "|".join(key_parts)
    
    def _update_metrics(self, processing_time: float, result_count: int):
        """Update performance metrics."""
        
        self.metrics["total_queries"] += 1
        
        # Update average response time
        total_time = self.metrics["avg_response_time"] * (self.metrics["total_queries"] - 1)
        self.metrics["avg_response_time"] = (total_time + processing_time) / self.metrics["total_queries"]
        
        # Update average results count
        total_results = self.metrics["avg_results_count"] * (self.metrics["total_queries"] - 1)
        self.metrics["avg_results_count"] = (total_results + result_count) / self.metrics["total_queries"]

class TechnicalRetriever(BaseDomainRetriever):
    """Specialized retriever for technical documentation and API references."""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(RetrievalDomain.TECHNICAL, config)
    
    def _initialize_settings(self) -> Dict[str, Any]:
        return {
            "prioritize_code_examples": True,
            "include_api_references": True,
            "boost_recent_versions": True,
            "technical_term_weight": 1.5,
            "code_snippet_weight": 2.0
        }
    
    async def _domain_specific_search(self, request: DomainRetrievalRequest) -> List[RetrievalResult]:
        """Technical domain search with code and API focus."""
        
        results = []
        
        # Enhanced query processing for technical terms
        enhanced_query = await self._enhance_technical_query(request.query)
        
        # Search with technical term boosting
        base_results = await self._search_technical_content(enhanced_query, request)
        
        # Boost results with code examples
        for result in base_results:
            if self._contains_code_examples(result.content):
                result.score *= self.settings["code_snippet_weight"]
                result.content_type = "code_example"
            elif self._is_api_reference(result.content):
                result.score *= self.settings["technical_term_weight"]
                result.content_type = "api_reference"
            else:
                result.content_type = "technical_doc"
            
            results.append(result)
        
        return results
    
    async def _enhance_technical_query(self, query: str) -> str:
        """Enhance query with technical synonyms and expansions."""
        
        technical_expansions = {
            "api": ["api", "application programming interface", "endpoint", "rest api"],
            "auth": ["authentication", "authorization", "login", "oauth", "token"],
            "config": ["configuration", "settings", "setup", "parameters"],
            "error": ["error", "exception", "bug", "issue", "problem"],
            "install": ["installation", "setup", "deploy", "configure"]
        }
        
        enhanced_terms = []
        for word in query.lower().split():
            enhanced_terms.append(word)
            if word in technical_expansions:
                enhanced_terms.extend(technical_expansions[word])
        
        return " ".join(enhanced_terms)
    
    async def _search_technical_content(self, 
                                      query: str, 
                                      request: DomainRetrievalRequest) -> List[RetrievalResult]:
        """Search technical content with domain-specific logic."""
        
        # Placeholder for actual search implementation
        # This would integrate with your vector store/search engine
        
        mock_results = [
            RetrievalResult(
                document_id="tech_001",
                content="API authentication using OAuth 2.0 tokens...",
                title="API Authentication Guide",
                score=0.9,
                domain=self.domain,
                retrieval_method="technical_search"
            ),
            RetrievalResult(
                document_id="tech_002", 
                content="Configuration parameters for the SDK...",
                title="SDK Configuration",
                score=0.8,
                domain=self.domain,
                retrieval_method="technical_search"
            )
        ]
        
        return mock_results
    
    def _contains_code_examples(self, content: str) -> bool:
        """Check if content contains code examples."""
        
        code_indicators = [
            "```", "```python", "```javascript", "```json",
            "function(", "def ", "class ", "import ",
            "curl -", "POST /", "GET /"
        ]
        
        return any(indicator in content for indicator in code_indicators)
    
    def _is_api_reference(self, content: str) -> bool:
        """Check if content is API reference documentation."""
        
        api_indicators = [
            "endpoint", "parameter", "response", "request",
            "HTTP", "REST", "JSON", "XML", "status code"
        ]
        
        return sum(1 for indicator in api_indicators if indicator.lower() in content.lower()) >= 3

class FAQRetriever(BaseDomainRetriever):
    """Specialized retriever for frequently asked questions."""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(RetrievalDomain.FAQ, config)
    
    def _initialize_settings(self) -> Dict[str, Any]:
        return {
            "exact_match_boost": 2.0,
            "question_similarity_weight": 1.8,
            "answer_relevance_weight": 1.2,
            "popularity_boost": 1.3
        }
    
    async def _domain_specific_search(self, request: DomainRetrievalRequest) -> List[RetrievalResult]:
        """FAQ search with question-answer matching."""
        
        results = []
        
        # Extract question intent
        question_type = await self._classify_question_type(request.query)
        
        # Search FAQ database
        faq_results = await self._search_faq_database(request.query, question_type)
        
        # Score based on question similarity and answer relevance
        for result in faq_results:
            question_score = await self._calculate_question_similarity(request.query, result.title)
            answer_score = await self._calculate_answer_relevance(request.query, result.content)
            
            # Combine scores
            result.score = (
                question_score * self.settings["question_similarity_weight"] +
                answer_score * self.settings["answer_relevance_weight"]
            ) / (self.settings["question_similarity_weight"] + self.settings["answer_relevance_weight"])
            
            result.content_type = "faq"
            result.metadata["question_type"] = question_type
            
            results.append(result)
        
        return results
    
    async def _classify_question_type(self, query: str) -> str:
        """Classify the type of question being asked."""
        
        question_patterns = {
            "how_to": [r"^how (do|can|to)", r"how.*\?"],
            "what_is": [r"^what (is|are)", r"what.*\?"],
            "why": [r"^why", r"why.*\?"],
            "when": [r"^when", r"when.*\?"],
            "where": [r"^where", r"where.*\?"],
            "troubleshooting": [r"not working", r"error", r"problem", r"issue"]
        }
        
        query_lower = query.lower()
        for question_type, patterns in question_patterns.items():
            if any(re.search(pattern, query_lower) for pattern in patterns):
                return question_type
        
        return "general"
    
    async def _search_faq_database(self, query: str, question_type: str) -> List[RetrievalResult]:
        """Search FAQ database with question type filtering."""
        
        # Placeholder for actual FAQ search
        mock_results = [
            RetrievalResult(
                document_id="faq_001",
                content="To reset your password, go to the login page and click 'Forgot Password'...",
                title="How do I reset my password?",
                score=0.0,  # Will be calculated
                domain=self.domain,
                retrieval_method="faq_search"
            ),
            RetrievalResult(
                document_id="faq_002",
                content="Billing occurs monthly on the date you signed up...",
                title="When am I billed?",
                score=0.0,  # Will be calculated
                domain=self.domain,
                retrieval_method="faq_search"
            )
        ]
        
        return mock_results
    
    async def _calculate_question_similarity(self, user_query: str, faq_question: str) -> float:
        """Calculate similarity between user query and FAQ question."""
        
        # Simple word overlap similarity
        user_words = set(user_query.lower().split())
        faq_words = set(faq_question.lower().split())
        
        if not user_words or not faq_words:
            return 0.0
        
        intersection = len(user_words & faq_words)
        union = len(user_words | faq_words)
        
        return intersection / union if union > 0 else 0.0
    
    async def _calculate_answer_relevance(self, user_query: str, faq_answer: str) -> float:
        """Calculate relevance of FAQ answer to user query."""
        
        # Simple keyword matching
        query_keywords = user_query.lower().split()
        answer_lower = faq_answer.lower()
        
        matches = sum(1 for keyword in query_keywords if keyword in answer_lower)
        return matches / len(query_keywords) if query_keywords else 0.0

class BillingRetriever(BaseDomainRetriever):
    """Specialized retriever for billing and payment information."""

    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(RetrievalDomain.BILLING, config)

    def _initialize_settings(self) -> Dict[str, Any]:
        return {
            "currency_aware": True,
            "plan_specific_boost": 1.5,
            "recent_changes_boost": 1.3,
            "policy_weight": 1.8
        }

    async def _domain_specific_search(self, request: DomainRetrievalRequest) -> List[RetrievalResult]:
        """Billing domain search with financial context."""

        results = []

        # Extract billing context
        billing_context = await self._extract_billing_context(request.query, request.user_context)

        # Search billing documentation
        billing_results = await self._search_billing_content(request.query, billing_context)

        # Apply billing-specific scoring
        for result in billing_results:
            # Boost plan-specific content
            if billing_context.get("user_plan") and billing_context["user_plan"] in result.content:
                result.score *= self.settings["plan_specific_boost"]

            # Boost policy and legal content for billing disputes
            if self._is_policy_content(result.content):
                result.score *= self.settings["policy_weight"]
                result.content_type = "billing_policy"
            else:
                result.content_type = "billing_info"

            results.append(result)

        return results

    async def _extract_billing_context(self, query: str, user_context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract billing-specific context from query and user data."""

        context = {}

        # Extract monetary amounts
        amounts = re.findall(r'\$\d+(?:\.\d{2})?', query)
        if amounts:
            context["mentioned_amounts"] = amounts

        # Extract plan references
        plan_keywords = ["free", "basic", "premium", "enterprise", "pro"]
        for keyword in plan_keywords:
            if keyword in query.lower():
                context["mentioned_plan"] = keyword
                break

        # Get user plan from context
        if user_context.get("customer_tier"):
            context["user_plan"] = user_context["customer_tier"]

        return context

    async def _search_billing_content(self, query: str, context: Dict[str, Any]) -> List[RetrievalResult]:
        """Search billing-specific content."""

        # Placeholder for actual billing search
        mock_results = [
            RetrievalResult(
                document_id="billing_001",
                content="Premium plan costs $29/month and includes advanced features...",
                title="Premium Plan Pricing",
                score=0.8,
                domain=self.domain,
                retrieval_method="billing_search"
            ),
            RetrievalResult(
                document_id="billing_002",
                content="Refund policy: Full refunds available within 30 days...",
                title="Refund Policy",
                score=0.7,
                domain=self.domain,
                retrieval_method="billing_search"
            )
        ]

        return mock_results

    def _is_policy_content(self, content: str) -> bool:
        """Check if content is policy-related."""

        policy_indicators = ["policy", "terms", "refund", "cancellation", "dispute", "legal"]
        return any(indicator in content.lower() for indicator in policy_indicators)

class TroubleshootingRetriever(BaseDomainRetriever):
    """Specialized retriever for troubleshooting and problem resolution."""

    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(RetrievalDomain.TROUBLESHOOTING, config)

    def _initialize_settings(self) -> Dict[str, Any]:
        return {
            "solution_priority": 2.0,
            "step_by_step_boost": 1.8,
            "error_code_exact_match": 3.0,
            "symptom_matching_weight": 1.5
        }

    async def _domain_specific_search(self, request: DomainRetrievalRequest) -> List[RetrievalResult]:
        """Troubleshooting search with problem-solution matching."""

        results = []

        # Extract problem context
        problem_context = await self._extract_problem_context(request.query)

        # Search troubleshooting database
        troubleshooting_results = await self._search_troubleshooting_content(request.query, problem_context)

        # Apply troubleshooting-specific scoring
        for result in troubleshooting_results:
            # Boost exact error code matches
            if problem_context.get("error_codes"):
                for error_code in problem_context["error_codes"]:
                    if error_code in result.content:
                        result.score *= self.settings["error_code_exact_match"]
                        break

            # Boost step-by-step solutions
            if self._has_step_by_step_solution(result.content):
                result.score *= self.settings["step_by_step_boost"]
                result.content_type = "step_by_step_solution"
            else:
                result.content_type = "troubleshooting_info"

            results.append(result)

        return results

    async def _extract_problem_context(self, query: str) -> Dict[str, Any]:
        """Extract problem-specific context from query."""

        context = {}

        # Extract error codes
        error_codes = re.findall(r'\b(error|code)\s*:?\s*(\d+)\b', query.lower())
        if error_codes:
            context["error_codes"] = [code[1] for code in error_codes]

        # Extract symptoms
        symptom_keywords = ["not working", "broken", "failed", "error", "issue", "problem"]
        symptoms = [keyword for keyword in symptom_keywords if keyword in query.lower()]
        if symptoms:
            context["symptoms"] = symptoms

        # Extract urgency indicators
        urgency_keywords = ["urgent", "critical", "asap", "immediately", "emergency"]
        if any(keyword in query.lower() for keyword in urgency_keywords):
            context["urgency"] = "high"

        return context

    async def _search_troubleshooting_content(self, query: str, context: Dict[str, Any]) -> List[RetrievalResult]:
        """Search troubleshooting content."""

        # Placeholder for actual troubleshooting search
        mock_results = [
            RetrievalResult(
                document_id="trouble_001",
                content="Step 1: Check your internet connection. Step 2: Clear browser cache...",
                title="Login Issues Resolution",
                score=0.9,
                domain=self.domain,
                retrieval_method="troubleshooting_search"
            ),
            RetrievalResult(
                document_id="trouble_002",
                content="Error 404 indicates the page was not found. This usually means...",
                title="Error 404 Troubleshooting",
                score=0.8,
                domain=self.domain,
                retrieval_method="troubleshooting_search"
            )
        ]

        return mock_results

    def _has_step_by_step_solution(self, content: str) -> bool:
        """Check if content contains step-by-step solution."""

        step_indicators = [
            r"step \d+", r"\d+\.", r"first", r"then", r"next", r"finally",
            "follow these steps", "procedure", "instructions"
        ]

        return any(re.search(pattern, content.lower()) for pattern in step_indicators)

class DomainRetrieverManager:
    """Manager for coordinating multiple domain-specific retrievers."""

    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)

        # Initialize domain retrievers
        self.retrievers: Dict[RetrievalDomain, BaseDomainRetriever] = {}
        self._initialize_retrievers()

        # Performance tracking
        self.performance_stats = defaultdict(dict)

    def _initialize_retrievers(self):
        """Initialize all domain retrievers."""

        try:
            self.retrievers[RetrievalDomain.TECHNICAL] = TechnicalRetriever(self.config.get("technical", {}))
            self.retrievers[RetrievalDomain.FAQ] = FAQRetriever(self.config.get("faq", {}))
            self.retrievers[RetrievalDomain.BILLING] = BillingRetriever(self.config.get("billing", {}))
            self.retrievers[RetrievalDomain.TROUBLESHOOTING] = TroubleshootingRetriever(self.config.get("troubleshooting", {}))

            self.logger.info(f"Initialized {len(self.retrievers)} domain retrievers")

        except Exception as e:
            self.logger.error(f"Failed to initialize domain retrievers: {e}")

    async def retrieve(self,
                      domain: RetrievalDomain,
                      request: DomainRetrievalRequest) -> List[RetrievalResult]:
        """Retrieve using specified domain retriever."""

        if domain not in self.retrievers:
            self.logger.warning(f"No retriever available for domain: {domain.value}")
            return []

        try:
            retriever = self.retrievers[domain]
            results = await retriever.retrieve(request)

            # Update performance stats
            self._update_performance_stats(domain, len(results))

            return results

        except Exception as e:
            self.logger.error(f"Retrieval failed for domain {domain.value}: {e}")
            return []

    async def multi_domain_retrieve(self,
                                  domains: List[RetrievalDomain],
                                  request: DomainRetrievalRequest) -> Dict[RetrievalDomain, List[RetrievalResult]]:
        """Retrieve from multiple domains simultaneously."""

        results = {}

        # Create tasks for parallel retrieval
        tasks = []
        for domain in domains:
            if domain in self.retrievers:
                task = asyncio.create_task(self.retrieve(domain, request))
                tasks.append((domain, task))

        # Wait for all tasks to complete
        for domain, task in tasks:
            try:
                domain_results = await task
                results[domain] = domain_results
            except Exception as e:
                self.logger.error(f"Multi-domain retrieval failed for {domain.value}: {e}")
                results[domain] = []

        return results

    async def get_best_domain_for_query(self, query: str) -> RetrievalDomain:
        """Determine the best domain for a given query."""

        domain_scores = {}

        # Score each domain based on query characteristics
        for domain in RetrievalDomain:
            score = await self._calculate_domain_score(query, domain)
            domain_scores[domain] = score

        # Return highest scoring domain
        best_domain = max(domain_scores.items(), key=lambda x: x[1])[0]
        return best_domain

    async def _calculate_domain_score(self, query: str, domain: RetrievalDomain) -> float:
        """Calculate how well a domain matches the query."""

        query_lower = query.lower()

        domain_keywords = {
            RetrievalDomain.TECHNICAL: ["api", "code", "sdk", "integration", "development", "programming"],
            RetrievalDomain.FAQ: ["what", "how", "why", "when", "where", "question"],
            RetrievalDomain.BILLING: ["payment", "billing", "invoice", "plan", "subscription", "cost"],
            RetrievalDomain.TROUBLESHOOTING: ["error", "problem", "issue", "not working", "broken", "fix"],
            RetrievalDomain.INTEGRATION: ["integrate", "connect", "sync", "import", "export", "webhook"],
            RetrievalDomain.SECURITY: ["security", "privacy", "authentication", "authorization", "encryption"],
            RetrievalDomain.ACCOUNT: ["account", "profile", "settings", "login", "password", "user"],
            RetrievalDomain.PRODUCT: ["feature", "functionality", "tool", "service", "capability"]
        }

        keywords = domain_keywords.get(domain, [])
        matches = sum(1 for keyword in keywords if keyword in query_lower)

        return matches / len(keywords) if keywords else 0.0

    def _update_performance_stats(self, domain: RetrievalDomain, result_count: int):
        """Update performance statistics for a domain."""

        if domain.value not in self.performance_stats:
            self.performance_stats[domain.value] = {
                "total_queries": 0,
                "total_results": 0,
                "avg_results_per_query": 0.0
            }

        stats = self.performance_stats[domain.value]
        stats["total_queries"] += 1
        stats["total_results"] += result_count
        stats["avg_results_per_query"] = stats["total_results"] / stats["total_queries"]

    async def get_performance_statistics(self) -> Dict[str, Any]:
        """Get performance statistics for all domain retrievers."""

        stats = {
            "total_domains": len(self.retrievers),
            "domain_performance": dict(self.performance_stats),
            "retriever_metrics": {}
        }

        # Get individual retriever metrics
        for domain, retriever in self.retrievers.items():
            stats["retriever_metrics"][domain.value] = retriever.metrics

        return stats

    def clear_caches(self):
        """Clear all retriever caches."""

        for retriever in self.retrievers.values():
            retriever.cache.clear()

        self.logger.info("All domain retriever caches cleared")

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all domain retrievers."""

        health_status = {
            "overall_status": "healthy",
            "domain_status": {},
            "total_domains": len(self.retrievers),
            "healthy_domains": 0
        }

        for domain, retriever in self.retrievers.items():
            try:
                # Simple health check - try a basic operation
                test_request = DomainRetrievalRequest(
                    query="test",
                    domain=domain,
                    max_results=1
                )

                # This would be a lightweight test in a real implementation
                status = "healthy"
                health_status["healthy_domains"] += 1

            except Exception as e:
                status = f"unhealthy: {e}"
                health_status["overall_status"] = "degraded"

            health_status["domain_status"][domain.value] = status

        if health_status["healthy_domains"] == 0:
            health_status["overall_status"] = "critical"

        return health_status
